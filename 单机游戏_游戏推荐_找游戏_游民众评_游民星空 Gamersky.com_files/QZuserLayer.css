/*user passport*/
.QZshade {
    width: 100%;
    height: 100%;
    _height: 1000px;
    background: #000;
    opacity: 0.6;
    filter: alpha(opacity=60);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 88888;
}

.QZshade {
    _position: absolute;
    _top: expression(documentElement.clientHeight-this.offsetHeight+documentElement.scrollTop);
}

.QZlogin {
    margin: -180px 0 0 -190px;
    padding: 30px 45px;
    width: 280px;
    height: auto;
    background: #fff;
    overflow: hidden;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
}

.QZlogin {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 99999;
}

.QZlogin {
    _position: absolute;
    _top: expression(documentElement.clientHeight-this.offsetHeight+documentElement.scrollTop);
}

.QZlogin .QZ-close {
    display: block;
    width: 30px;
    height: 30px;
    text-indent: -999px;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0;
}

.QZlogin .QZ-close {
    background: url(//image.gamersky.com/webimg15/qzclose.png) no-repeat;
}

.QZlogin .QZ-close:hover {
    background-position: 0 -30px;
    text-decoration: none;
}

.QZlogin input {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
}

.QZlogin .QZ-tit {
    width: auto;
    height: 40px;
    line-height: 20px;
    color: #e23703;
    font-size: 14px;
    font-family: 'Microsoft YaHei';
    text-align: center;
}

.QZlogin .QZ-int {
    width: auto;
    height: 42px;
}

.QZlogin .QZ-int input {
    float: left;
    padding: 0 10px 0 34px;
    width: 234px;
    height: 34px;
    line-height: 34px;
    color: #999;
    font-size: 14px;
    font-family: 'Microsoft YaHei';
}

.QZlogin .QZ-int input {
    background: #f9f9fa url(//image.gamersky.com/webimg15//ico.png) no-repeat;
    border: 1px solid #ddd;
    border-radius: 2px;
}

.QZlogin .QZ-int input#QZuserName {
    background-position: 8px 0;
}

.QZlogin .QZ-int input#QZpassword {
    background-position: 8px -34px;
}

.QZlogin .QZ-int input.cur {
    color: #000;
}

.QZlogin .QZ-chk {
    margin-bottom: 10px;
    width: auto;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    font-family: SimSun;
}

.QZlogin .QZ-chk span {
    float: left;
    color: #676767;
}

.QZlogin .QZ-chk label {
    float: left;
    color: #676767;
}

.QZlogin .QZ-chk input {
    margin-right: 5px;
    vertical-align: middle;
}

.QZlogin .QZ-chk a.QZ-link {
    float: right;
    color: #676767;
}

.QZlogin .QZ-chk a.QZ-link:hover {
    color: #cd4700;
    text-decoration: underline;
}

.QZlogin .QZ-chk a.QZ-reg {
    float: right;
    color: #1481c2;
}

.QZlogin .QZ-chk a.QZ-reg:hover {
    color: #cd4700;
    text-decoration: underline;
}

.QZlogin .QZ-btn {
    width: auto;
    height: 45px;
    text-align: center;
}

.QZlogin .QZ-btn a#QZbtn {
    display: block;
    width: auto;
    height: 38px;
    line-height: 38px;
    color: #fff;
    font-size: 15px;
    font-family: 'Microsoft YaHei';
}

.QZlogin .QZ-btn a#QZbtn {
    background-color: #e23703;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.QZlogin .QZ-btn a#QZbtn:hover {
    background-color: #f64c18;
    text-decoration: none;
}

.QZlogin fieldset {
    margin: 0;
    padding: 0;
    width: 280px;
    height: 30px;
    text-align: center;
    border: 0;
    border-top: 1px solid #ddd;
}

.QZlogin fieldset legend {
    margin: 0 auto;
    padding: 0 6px;
    width: auto;
    height: 20px;
    line-height: 20px;
    color: #666;
}

.QZlogin .QZ-con {
    width: 100%;
    height: auto;
    text-align: center;
}

.QZlogin .QZ-con a {
    margin: 0 22px;
    display: inline-block;
    width: 45px;
    height: 45px;
    background: url(//image.gamersky.com/webimg13/user/qtlgn.png) no-repeat;
}

.QZlogin .QZ-con a#QZsinaLogin {
    background-position: 0 0;
}

.QZlogin .QZ-con a#QZqqLogin {
    background-position: 0 -45px;
}

.QZlogin .QZ-con a#QZweixinLogin {
    background-position: 0 -90px;
}

/*通知消息层*/
.topbar-tz {
    display: none;
    padding-top: 9px;
    width: 330px;
    height: auto;
    overflow: visible;
    position: absolute;
    left: -140px;
    top: 37px;
    z-index: 99999;
}

.topbar-tz {
    background: url(//image.gamersky.com/webimg13/user/userLayer.png) center 0 no-repeat;
    box-shadow: 0 10px 10px 0 rgba(0, 0, 0, 0.1);
}

.topbar-tz .tzcon {
    padding: 20px 0 10px;
    width: 330px;
    height: auto;
    background-color: #fff;
    overflow: hidden;
}

.topbar-tz .tzcon .tzlik {
    padding: 0 20px;
    width: 290px;
    height: auto;
    overflow: hidden;
}

.topbar-tz .tzcon .tzlik a {
    float: left;
    margin-bottom: 8px;
    padding: 10px 13px 10px 17px;
    display: inline-block;
    width: 258px;
    height: 24px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.topbar-tz .tzcon .tzlik a i {
    display: inline-block;
    width: 18px;
    height: 24px;
    background: url(//image.gamersky.com/webimg13/user/tz-li.png) no-repeat;
}

.topbar-tz .tzcon .tzlik a i.l1 {
    float: left;
    background-position: 0 0;
}

.topbar-tz .tzcon .tzlik a i.l2 {
    float: left;
    background-position: 0 -24px;
}

.topbar-tz .tzcon .tzlik a i.l3 {
    float: left;
    background-position: 0 -72px;
}

.topbar-tz .tzcon .tzlik a i.r {
    float: right;
    background-position: 0 -48px;
}

.topbar-tz .tzcon .tzlik a span {
    float: left;
    margin: 0;
    display: inline-block;
    width: auto;
    height: 24px;
    line-height: 24px;
    font-family: 'Microsoft YaHei';
}

.topbar-tz .tzcon .tzlik a span.txt {
    color: #444;
    font-size: 15px;
    padding: 0 6px 0 17px;
}

.topbar-tz .tzcon .tzlik a span.num {
    color: #e23703;
    font-size: 14px;
}

.topbar-tz .tzcon .tzlik a:hover {
    background-color: #f5f5f5;
}

.topbar-tz .tzcon .tzlist {
    margin-top: -5px;
    padding: 0 25px;
    width: 280px;
    height: auto;
    overflow: hidden;
}

.topbar-tz .tzcon .tzlist li {
    margin-top: -1px;
    padding: 10px 0 15px;
    width: 280px;
    height: auto;
    overflow: hidden;
    border-top: 1px solid #f2f2f3;
}

.topbar-tz .tzcon .tzlist li i.new {
    display: block;
    float: right;
    margin: -5px 0 0 -10px;
    width: 10px;
    height: 13px;
    position: absolute;
}

.topbar-tz .tzcon .tzlist li i.new {
    background: url(//image.gamersky.com/webimg15/tz_new.png) no-repeat;
}

.topbar-tz .tzcon .tzlist li h3 {
    margin: 0;
    min-height: 0;
    max-height: 30px;
    line-height: 30px;
    font-size: 15px;
    font-family: 'Microsoft YaHei';
    overflow: hidden;
}

.topbar-tz .tzcon .tzlist li h3 a {
    color: #444 !important;
    font-size: 15px;
}

.topbar-tz .tzcon .tzlist li h3 a:hover {
    color: #ff5400 !important;
}

.topbar-tz .tzcon .tzlist li div {
    width: 280px;
    height: 20px;
    line-height: 20px;
    color: #555;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.topbar-tz .tzcon .tzlist li div a {
    color: #1982cb !important;
}

.topbar-tz .tzcon .tzlist li div a:hover {
    text-decoration: underline;
}

.topbar-tz .tzbot {
    padding: 10px 20px;
    width: auto;
    height: 30px;
    background-color: #f5f5f5;
    border-top: 1px solid #eee;
    overflow: hidden;
}

.topbar-tz .tzbot .lik1 {
    float: left;
    width: auto;
    height: 30px;
    overflow: hidden;
}

.topbar-tz .tzbot .lik1 a {
    float: left;
    display: inline-block;
    width: 28px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.topbar-tz .tzbot .lik1 a {
    background: url(//image.gamersky.com/webimg13/user/tz-setup.png) center center no-repeat;
}

.topbar-tz .tzbot .lik1 a:hover {
    background-color: #fff;
}

.topbar-tz .tzbot .lik2 {
    float: right;
    width: auto;
    height: 30px;
    overflow: hidden;
}

.topbar-tz .tzbot .lik2 a {
    float: left;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    color: #888;
    font-size: 12px;
    font-family: 'Microsoft YaHei';
}

.topbar-tz .tzbot .lik2 a:hover {
    color: #000;
}

.topbar-tz .tzbot .lik2 i {
    float: left;
    padding: 0 15px;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    color: #ccc !important;
    font-size: 12px;
    font-family: 'Microsoft YaHei';
}

/*网址导航*/
.topbar-menu {
    display: none;
    width: 418px;
    height: 176px;
    overflow: hidden;
    position: absolute;
    top: 46px;
    left: 50%;
    margin-left: -209px;
    z-index: 99999;
}

.topbar-menu {
    background: #3b3b3b url(//image.gamersky.com/webimg15/qznav-mk.png) repeat-x;
    _background-image: url(#);
    box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.5);
}

.topbar-navul {
    margin: 15px 0;
    padding: 0 20px;
    width: 378px;
    height: 146px;
    list-style-type: none;
    overflow: hidden;
}

.topbar-navul li {
    float: left;
    width: 188px;
    height: 146px;
    border-left: 1px solid #515151;
    overflow: hidden;
    list-style-type: none;
}

.topbar-navul li h3 {
    margin: 0 0 10px -1px;
    _margin-left: -2px;
    padding-left: 15px;
    width: 160px;
    height: 16px;
    line-height: 18px;
    color: #ddd;
    font-size: 13px;
    font-family: SimSun;
}

.topbar-navul li h3.red {
    border-left: 3px solid #e22200;
}

.topbar-navul li h3.blue {
    border-left: 3px solid #1f79d5;
}

.topbar-navul li .lik {
    float: left;
    width: 94px;
    height: auto;
}

.topbar-navul li .lik a {
    padding: 0 0 0 18px;
    display: inline-block;
    width: auto;
    height: 24px;
    line-height: 24px;
    font-family: SimSun;
    font-size: 12px;
}

.topbar-navul li .lik a.al {
    color: #bbb;
}

.topbar-navul li .lik a.al_red {
    color: #ff5400;
}

.topbar-navul li .lik a.al_blue {
    color: #009cff;
}

.topbar-navul li .lik a:hover {
    color: #fff;
    text-decoration: underline;
}

/*用户下拉*/
.topbar-usercon {
    padding: 15px;
    width: 230px;
    height: auto;
    background-color: #fff;
    border: 1px solid #ddd;
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.3);
}

.topbar-usercon li.user1 {
    width: 230px;
    height: 85px;
    border-bottom: 1px dotted #d5d5d5;
}

.topbar-usercon li.user1 .img {
    float: left;
    width: 88px;
    height: 72px;
}

.topbar-usercon li.user1 .img img {
    width: 72px;
    height: 72px;
    border-radius: 36px;
}

.topbar-usercon li.user1 .time {
    float: left;
    width: 142px;
    height: 18px;
    line-height: 18px;
    color: #666;
    font-size: 12px;
}

.topbar-usercon li.user1 .name {
    float: left;
    margin-bottom: 10px;
    width: 142px;
    height: 26px;
    line-height: 26px;
    color: #333;
    font-size: 16px;
    font-weight: bold;
}

.topbar-usercon li.user1 .name {
    white-space: nowrap;
    text-overflow: ellipsis;
}

.topbar-usercon li.user1 .binding {
    float: left;
    margin-left: -2px;
    width: 144px;
    height: 20px;
}

.topbar-usercon li.user1 .binding a {
    float: left;
    margin-right: 6px;
    display: inline-block;
    width: 20px;
    height: 18px;
    border: 1px solid #fff;
    border-radius: 2px;
    overflow: hidden;
}

.topbar-usercon li.user1 .binding a {
    background: url(//image.gamersky.com/webimg13/user/binding.png) no-repeat;
}

.topbar-usercon li.user1 .binding a.email {
    background-position: 0 0;
}

.topbar-usercon li.user1 .binding a.phone {
    background-position: -20px 0;
}

.topbar-usercon li.user1 .binding a.weixin {
    background-position: -40px 0;
}

.topbar-usercon li.user1 .binding a.qq {
    background-position: -60px 0;
}

.topbar-usercon li.user1 .binding a.sina {
    background-position: -80px 0;
}

.topbar-usercon li.user1 .binding a.email.ok {
    background-position: 0 -18px;
}

.topbar-usercon li.user1 .binding a.phone.ok {
    background-position: -20px -18px;
}

.topbar-usercon li.user1 .binding a.weixin.ok {
    background-position: -40px -18px;
}

.topbar-usercon li.user1 .binding a.qq.ok {
    background-position: -60px -18px;
}

.topbar-usercon li.user1 .binding a.sina.ok {
    background-position: -80px -18px;
}

.topbar-usercon li.user1 .binding a:hover {
    background-color: #f7f7f7;
    border: 1px solid #d5d5d5;
}

.topbar-usercon li.user2 {
    padding: 15px 0 10px;
    width: 230px;
    height: auto;
    border-bottom: 1px dotted #d5d5d5;
    overflow: hidden;
    display: flex;
    flex-direction: row;
}

.topbar-usercon li.user2 a {
    float: left;
    width: 100px;
    height: auto;
    display: flex;
    flex-direction: row;
}

.topbar-usercon li.user2 a i {
    margin: 0 auto;
    display: block;
    width: 34px;
    height: 34px;
    background: url(//image.gamersky.com/webimg13/user/ubg.png) no-repeat;
}

.topbar-usercon li.user2 a p {
    margin: 0;
    height: 30px;
    line-height: 30px;
    color: #e23703;
    font-size: 14px;
    text-align: center;
}

.topbar-usercon li.user2 a.uc i {
    background-position: 0 0;
}

.topbar-usercon li.user2 a.zl {
    margin-left: 20px;
}

.topbar-usercon li.user2 a.zl i {
    background-position: -102px 0;
}

.topbar-usercon li.user2 a.jb i {
    background-position: -68px 0;
}

.topbar-usercon li.user2 a.uc:hover i {
    background-position: 0 -34px;
}

.topbar-usercon li.user2 a.zl:hover i {
    background-position: -102px -34px;
}

.topbar-usercon li.user2 a.jb:hover i {
    background-position: -68px -34px;
}

.topbar-usercon li.user3 {
    padding-top: 15px;
    width: 230px;
    height: 28px;
}

.topbar-usercon li.user3 .link {
    float: left;
    width: auto;
    height: 28px;
    line-height: 28px;
    color: #c5c5c5;
    font-size: 12px;
    font-family: SimSun;
}

.topbar-usercon li.user3 .link a {
    color: #888;
    font-size: 14px;
    font-family: 'Microsoft YaHei';
}

.topbar-usercon li.user3 .link a:hover {
    color: #e11d03;
}

.topbar-usercon li.user3 .ExitSign {
    float: right;
    width: auto;
    height: 28px;
}

.topbar-usercon li.user3 .ExitSign a {
    display: block;
    width: 63px;
    height: 26px;
    line-height: 26px;
    color: #666;
    text-align: center;
    background-color: #f7f7f7;
    border: 1px solid #e5e5e5;
    border-radius: 2px;
}

.topbar-usercon li.user3 .ExitSign a:hover {
    color: #fff;
    background-color: #e23703;
    border-color: #ec5b2f;
    border-radius: 2px;
}