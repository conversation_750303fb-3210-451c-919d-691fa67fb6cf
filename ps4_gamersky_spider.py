import pandas as pd
from bs4 import BeautifulSoup, Tag
import difflib
import os

# 配置
EXCEL_PATH = '工作簿1.xlsx'  # 你的Excel文件名
HTML_PATH = '单机游戏_游戏推荐_找游戏_游民众评_游民星空 Gamersky.com.html'  # 本地HTML文件名
RESULT_PATH = 'ps4_gamersky_result.xlsx'

# 读取Excel
excel_df = pd.read_excel(EXCEL_PATH)
if '游戏名称' not in excel_df.columns:
    raise ValueError('Excel中必须有"游戏名称"这一列')

# 解析本地HTML，提取所有游戏条目
with open(HTML_PATH, 'r', encoding='utf-8') as f:
    soup = BeautifulSoup(f, 'html.parser')

# 构建本地游戏库
game_list = []
for li in soup.find_all('li', class_='gamelist'):
    if not isinstance(li, Tag):
        continue
    a = li.find('a', href=True) if isinstance(li, Tag) else None
    img = li.find('img') if isinstance(li, Tag) else None
    p = li.find('p') if isinstance(li, Tag) else None
    if a and img and p and isinstance(a, Tag) and isinstance(img, Tag) and isinstance(p, Tag):
        game_list.append({
            'name': p.get_text(strip=True),
            'cover': img.get('src'),
            'detail_url': a.get('href')
        })

all_titles = [g['name'] for g in game_list]

def extract_detail_info(detail_html_path):
    intro = ''
    screenshots = []
    videos = []
    if not os.path.exists(detail_html_path):
        return intro, screenshots, videos
    with open(detail_html_path, 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f, 'html.parser')
        # 介绍
        p = soup.select_one('.YXXX-B .con p')
        if p:
            intro = p.get_text(strip=True)
        # 截图
        for img in soup.select('.preview_Imgs_list.jt img'):
            pic = img.get('data-pic')
            if pic:
                screenshots.append(pic)
        # 视频
        for a in soup.select('.piclist a.vd'):
            vid = a.get('data-vid')
            if vid:
                videos.append(vid)
    # 只保留前3张截图和第1个视频
    screenshots = screenshots[:3]
    videos = videos[:1]
    return intro, screenshots, videos

# 结果列表
game_results = []

for idx, row in excel_df.iterrows():
    search_name = str(row['游戏名称']).strip()
    match = difflib.get_close_matches(search_name, all_titles, n=1, cutoff=0.4)
    if match:
        matched_title = match[0]
        for g in game_list:
            if g['name'] == matched_title:
                # 查找详情页HTML文件
                detail_html_path = ''
                for file in os.listdir('.'):
                    if file.endswith('.html') and matched_title in file:
                        detail_html_path = file
                        break
                intro, screenshots, videos = extract_detail_info(detail_html_path) if detail_html_path else ('', [], [])
                result = {
                    '游戏名称': search_name,
                    '匹配到的名称': matched_title,
                    '封面': g['cover'],
                    '详情页链接': g['detail_url'],
                    '介绍': intro,
                    '截图1': screenshots[0] if len(screenshots) > 0 else '',
                    '截图2': screenshots[1] if len(screenshots) > 1 else '',
                    '截图3': screenshots[2] if len(screenshots) > 2 else '',
                    '视频1': videos[0] if len(videos) > 0 else ''
                }
                game_results.append(result)
                break
    else:
        game_results.append({
            '游戏名称': search_name,
            '匹配到的名称': '',
            '封面': '',
            '详情页链接': '',
            '介绍': '',
            '截图1': '',
            '截图2': '',
            '截图3': '',
            '视频1': ''
        })

# 固定输出字段顺序
columns = ['游戏名称', '匹配到的名称', '封面', '详情页链接', '介绍', '截图1', '截图2', '截图3', '视频1']
result_df = pd.DataFrame(game_results)
result_df = result_df[columns]
result_df.to_excel(RESULT_PATH, index=False)
print(f'已写入{RESULT_PATH}') 