import pandas as pd
from bs4 import BeautifulSoup, Tag
import difflib
import os
import re
import requests
import time
from urllib.parse import urljoin, urlparse

# 配置
EXCEL_PATH = '工作簿1.xlsx'  # 你的Excel文件名
HTML_PATH = '单机游戏_游戏推荐_找游戏_游民众评_游民星空 Gamersky.com.html'  # 本地HTML文件名
RESULT_PATH = 'ps4_gamersky_result.xlsx'

# 检查文件是否存在
if not os.path.exists(EXCEL_PATH):
    raise FileNotFoundError(f'Excel文件不存在: {EXCEL_PATH}')
if not os.path.exists(HTML_PATH):
    raise FileNotFoundError(f'HTML文件不存在: {HTML_PATH}')

# 读取Excel
excel_df = pd.read_excel(EXCEL_PATH)
if '游戏名称' not in excel_df.columns:
    raise ValueError('Excel中必须有"游戏名称"这一列')

print(f'读取到 {len(excel_df)} 个游戏名称')

# 解析本地HTML，提取所有游戏条目
with open(HTML_PATH, 'r', encoding='utf-8') as f:
    soup = BeautifulSoup(f, 'html.parser')

# 构建本地游戏库
game_list = []
for li in soup.find_all('li', class_='gamelist'):
    if not isinstance(li, Tag):
        continue
    a = li.find('a', href=True) if isinstance(li, Tag) else None
    img = li.find('img') if isinstance(li, Tag) else None
    p = li.find('p') if isinstance(li, Tag) else None
    if a and img and p and isinstance(a, Tag) and isinstance(img, Tag) and isinstance(p, Tag):
        game_list.append({
            'name': p.get_text(strip=True),
            'cover': img.get('src'),
            'detail_url': a.get('href')
        })

all_titles = [g['name'] for g in game_list]

def normalize_filename(name):
    """将游戏名称标准化为可能的文件名格式"""
    # 移除或替换特殊字符
    name = re.sub(r'[：:]', '', name)  # 移除冒号
    name = re.sub(r'[？?]', '', name)  # 移除问号
    name = re.sub(r'[！!]', '', name)  # 移除感叹号
    name = re.sub(r'[/\\]', '', name)  # 移除斜杠
    name = re.sub(r'[<>"|*]', '', name)  # 移除其他特殊字符
    return name.strip()

def download_game_detail_page(detail_url, game_name):
    """下载游戏详情页HTML"""
    try:
        print(f'  正在下载详情页: {detail_url}')

        # 设置请求头，模拟浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # 发送请求
        response = requests.get(detail_url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'

        # 生成本地文件名
        safe_name = normalize_filename(game_name)
        local_filename = f'{safe_name}_详情页.html'

        # 保存HTML文件
        with open(local_filename, 'w', encoding='utf-8') as f:
            f.write(response.text)

        print(f'  详情页已保存: {local_filename}')
        return local_filename

    except requests.RequestException as e:
        print(f'  下载详情页失败: {e}')
        return None
    except Exception as e:
        print(f'  保存详情页失败: {e}')
        return None

def find_detail_html_file(game_name):
    """查找游戏对应的详情页HTML文件"""
    # 获取所有HTML文件
    html_files = [f for f in os.listdir('.') if f.endswith('.html') and f != HTML_PATH]

    if not html_files:
        print(f'  未找到任何详情页HTML文件')
        return None

    print(f'  可用的HTML文件: {html_files}')

    # 标准化游戏名称
    normalized_game_name = normalize_filename(game_name)
    print(f'  标准化游戏名称: "{normalized_game_name}"')

    # 尝试多种匹配方式
    for html_file in html_files:
        # 方式1: 直接包含匹配
        if game_name in html_file:
            print(f'  找到匹配文件(直接匹配): {html_file}')
            return html_file

        # 方式2: 标准化后匹配
        if normalized_game_name and normalized_game_name in html_file:
            print(f'  找到匹配文件(标准化匹配): {html_file}')
            return html_file

        # 方式3: 部分匹配
        game_words = game_name.split()
        if len(game_words) > 1:
            for word in game_words:
                if len(word) > 1 and word in html_file:
                    print(f'  找到匹配文件(部分匹配 "{word}"): {html_file}')
                    return html_file

    print(f'  未找到匹配的HTML文件')
    return None

def extract_detail_info(detail_html_path):
    """从详情页HTML文件中提取游戏信息"""
    intro = ''
    screenshots = []
    videos = []

    if not detail_html_path or not os.path.exists(detail_html_path):
        print(f'  详情页文件不存在: {detail_html_path}')
        return intro, screenshots, videos

    print(f'  正在解析详情页: {detail_html_path}')

    try:
        with open(detail_html_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')

            # 介绍
            p = soup.select_one('.YXXX-B .con p')
            if p:
                intro = p.get_text(strip=True)
                print(f'  找到介绍: {intro[:50]}...')
            else:
                print(f'  未找到介绍 (选择器: .YXXX-B .con p)')

            # 截图 - 尝试多种选择器
            # 方式1: 新版页面结构 (.preview_Imgs_list.jt img)
            img_elements = soup.select('.preview_Imgs_list.jt img')
            print(f'  方式1找到 {len(img_elements)} 个截图元素')
            for img in img_elements:
                pic = img.get('data-pic')
                if pic:
                    screenshots.append(pic)

            # 方式2: 旧版页面结构 (.piclist a)
            if not screenshots:
                piclist_elements = soup.select('.piclist a')
                print(f'  方式2找到 {len(piclist_elements)} 个piclist元素')
                for a in piclist_elements:
                    if not a.get('class') or 'vd' not in a.get('class', []):  # 排除视频元素
                        pic = a.get('data-pic')
                        if pic:
                            screenshots.append(pic)

            print(f'  最终截图数量: {len(screenshots)}')

            # 视频 - 尝试多种选择器
            # 方式1: 通用的 a.vd 选择器
            video_elements = soup.select('a.vd')
            print(f'  方式1找到 {len(video_elements)} 个视频元素')
            for a in video_elements:
                vid = a.get('data-vid')
                if vid:
                    videos.append(vid)

            # 方式2: 在piclist中查找视频
            if not videos:
                piclist_videos = soup.select('.piclist a.vd')
                print(f'  方式2找到 {len(piclist_videos)} 个piclist视频元素')
                for a in piclist_videos:
                    vid = a.get('data-vid')
                    if vid:
                        videos.append(vid)

            print(f'  最终视频数量: {len(videos)}')

    except Exception as e:
        print(f'  解析详情页时出错: {e}')

    # 只保留前3张截图和第1个视频
    screenshots = screenshots[:3]
    videos = videos[:1]

    print(f'  提取结果: 介绍={len(intro)}字符, 截图={len(screenshots)}张, 视频={len(videos)}个')
    return intro, screenshots, videos

# 结果列表
game_results = []

for idx, row in excel_df.iterrows():
    search_name = str(row['游戏名称']).strip()
    print(f'\n处理游戏 {idx+1}/{len(excel_df)}: "{search_name}"')

    # 使用模糊匹配查找游戏
    match = difflib.get_close_matches(search_name, all_titles, n=1, cutoff=0.3)

    # 如果没有找到，尝试更低的阈值
    if not match:
        match = difflib.get_close_matches(search_name, all_titles, n=1, cutoff=0.2)
        if match:
            print(f'  使用低阈值匹配到: "{match[0]}"')

    # 显示所有可能的匹配供参考
    all_matches = difflib.get_close_matches(search_name, all_titles, n=3, cutoff=0.2)
    if all_matches:
        print(f'  所有可能匹配:')
        for i, m in enumerate(all_matches, 1):
            similarity = difflib.SequenceMatcher(None, search_name, m).ratio()
            print(f'    {i}. {m} (相似度: {similarity:.3f})')
    else:
        print(f'  在当前游戏库({len(all_titles)}个游戏)中未找到任何相似游戏')

    if match:
        matched_title = match[0]
        print(f'  匹配到: "{matched_title}"')

        # 查找对应的游戏信息
        matched_game = None
        for g in game_list:
            if g['name'] == matched_title:
                matched_game = g
                break

        if matched_game:
            # 首先尝试查找本地详情页HTML文件
            detail_html_path = find_detail_html_file(matched_title)
            downloaded_file = None  # 记录是否是新下载的文件

            # 如果本地没有，则自动下载
            if not detail_html_path:
                print(f'  本地未找到详情页，尝试自动下载...')
                detail_html_path = download_game_detail_page(matched_game['detail_url'], matched_title)
                downloaded_file = detail_html_path  # 标记为新下载的文件
                # 下载后稍等一下，避免请求过快
                time.sleep(2)

            # 提取详细信息
            intro, screenshots, videos = extract_detail_info(detail_html_path)

            # 如果是新下载的文件，提取完信息后删除
            if downloaded_file and os.path.exists(downloaded_file):
                try:
                    os.remove(downloaded_file)
                    print(f'  已删除临时文件: {downloaded_file}')
                except Exception as e:
                    print(f'  删除临时文件失败: {e}')

            result = {
                '游戏名称': search_name,
                '匹配到的名称': matched_title,
                '封面': matched_game['cover'],
                '详情页链接': matched_game['detail_url'],
                '介绍': intro,
                '截图1': screenshots[0] if len(screenshots) > 0 else '',
                '截图2': screenshots[1] if len(screenshots) > 1 else '',
                '截图3': screenshots[2] if len(screenshots) > 2 else '',
                '视频1': videos[0] if len(videos) > 0 else ''
            }
            game_results.append(result)
        else:
            print(f'  错误: 在游戏列表中找不到匹配的游戏')
            game_results.append({
                '游戏名称': search_name,
                '匹配到的名称': matched_title,
                '封面': '',
                '详情页链接': '',
                '介绍': '',
                '截图1': '',
                '截图2': '',
                '截图3': '',
                '视频1': ''
            })
    else:
        print(f'  未找到匹配的游戏')
        game_results.append({
            '游戏名称': search_name,
            '匹配到的名称': '',
            '封面': '',
            '详情页链接': '',
            '介绍': '',
            '截图1': '',
            '截图2': '',
            '截图3': '',
            '视频1': ''
        })

# 固定输出字段顺序
columns = ['游戏名称', '匹配到的名称', '封面', '详情页链接', '介绍', '截图1', '截图2', '截图3', '视频1']
result_df = pd.DataFrame(game_results)
result_df = result_df[columns]
result_df.to_excel(RESULT_PATH, index=False)
print(f'已写入{RESULT_PATH}') 