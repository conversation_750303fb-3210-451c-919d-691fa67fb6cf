#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游民星空游戏信息自动提取器
功能：根据Excel中的游戏名称，自动提取游戏介绍、封面、截图、视频
"""

import pandas as pd
from bs4 import BeautifulSoup
import requests
import time
import json
import os
import re
import difflib
from urllib.parse import urljoin, urlparse

# 配置
EXCEL_PATH = '工作簿1.xlsx'
GAME_CACHE_FILE = 'game_library_cache.json'
RESULT_PATH = 'gamersky_result.xlsx'
BASE_URL = 'https://ku.gamersky.com'
GAME_LIST_URL = 'https://ku.gamersky.com/sp/1758-0-0-0-0-0.html'
MAX_PAGES = 20  # 最大爬取页数

class GamerskySpider:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
            'Accept-Encoding': 'gzip, deflate, br',
            'Upgrade-Insecure-Requests': '1',
        })

        # 设置Cookie以解决视频播放问题
        self.session.cookies.update({
            'gamersky_cookie': 'enabled',
            'video_enabled': '1',
            'user_consent': 'true'
        })

        self.game_library = []
    
    def search_game_directly(self, game_name):
        """直接搜索游戏"""
        print(f"  正在搜索游戏: {game_name}")

        try:
            # 使用游民星空的搜索功能
            search_url = f"https://so.gamersky.com/all/ku?s={requests.utils.quote(game_name)}"

            response = self.session.get(search_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找搜索结果
            results = soup.find_all('div', class_='result-item') or soup.find_all('li', class_='result')

            if not results:
                # 尝试其他可能的结构
                results = soup.find_all('div', class_='item') or soup.find_all('li')

            for result in results[:3]:  # 只检查前3个结果
                # 提取游戏名称
                title_elem = result.find('h3') or result.find('h2') or result.find('a') or result.find('p')
                if not title_elem:
                    continue

                title = title_elem.get_text(strip=True)

                # 检查是否匹配
                if self.is_game_match(game_name, title):
                    # 提取详情页链接
                    link_elem = result.find('a', href=True)
                    if link_elem:
                        detail_url = link_elem['href']
                        if not detail_url.startswith('http'):
                            detail_url = urljoin(BASE_URL, detail_url)

                        # 提取封面
                        img_elem = result.find('img')
                        cover_url = ''
                        if img_elem:
                            cover_url = img_elem.get('src') or img_elem.get('data-src') or ''
                            if cover_url and not cover_url.startswith('http'):
                                cover_url = urljoin(BASE_URL, cover_url)

                        print(f"  ✓ 搜索匹配: {title}")
                        return {
                            'name': title,
                            'detail_url': detail_url,
                            'cover_url': cover_url
                        }

            print(f"  ✗ 搜索无结果")
            return None

        except Exception as e:
            print(f"  搜索失败: {e}")
            return None

    def is_game_match(self, search_name, found_name):
        """判断游戏是否匹配"""
        # 标准化名称
        search_norm = self.normalize_game_name(search_name.lower())
        found_norm = self.normalize_game_name(found_name.lower())

        # 精确匹配
        if search_norm == found_norm:
            return True

        # 包含匹配
        if search_norm in found_norm or found_norm in search_norm:
            return True

        # 相似度匹配
        similarity = difflib.SequenceMatcher(None, search_norm, found_norm).ratio()
        return similarity > 0.7
    
    def build_game_library(self):
        """构建游戏库 - 使用AJAX API"""
        page = 1
        total_games = 0

        while page <= MAX_PAGES:
            print(f"正在爬取第 {page} 页...")

            # 使用AJAX API获取游戏列表
            ajax_url = "https://ku.gamersky.com/AjaxLoad/GetGameList"

            # 构建POST数据
            post_data = {
                'nodeId': '20039',
                'pageIndex': str(page),
                'pageSize': '36',
                'orderBy': '0',
                'platformId': '0',
                'typeId': '0',
                'yearId': '0',
                'monthId': '0',
                'languageId': '0'
            }

            try:
                response = self.session.post(ajax_url, data=post_data, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'

                # 解析返回的HTML片段
                soup = BeautifulSoup(response.text, 'html.parser')

                # 查找游戏列表
                games_found = 0
                for li in soup.find_all('li'):
                    game_info = self.extract_game_info_from_ajax(li)
                    if game_info:
                        self.game_library.append(game_info)
                        games_found += 1

                print(f"  第 {page} 页找到 {games_found} 个游戏")
                total_games += games_found

                # 如果没有找到游戏，说明已经到最后一页
                if games_found == 0:
                    print("已到达最后一页")
                    break

                page += 1
                time.sleep(1)  # 避免请求过快

            except Exception as e:
                print(f"爬取第 {page} 页失败: {e}")
                break

        print(f"游戏库构建完成，总共 {total_games} 个游戏")

        # 保存缓存
        try:
            with open(GAME_CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.game_library, f, ensure_ascii=False, indent=2)
            print(f"游戏库已保存到 {GAME_CACHE_FILE}")
        except Exception as e:
            print(f"保存缓存失败: {e}")

    def extract_game_info_from_ajax(self, li_element):
        """从AJAX返回的li元素中提取游戏信息"""
        try:
            # 游戏名称
            title_elem = li_element.find('p', class_='tit') or li_element.find('p')
            if not title_elem:
                return None
            name = title_elem.get_text(strip=True)

            # 详情页链接
            a = li_element.find('a', href=True)
            if not a:
                return None
            detail_url = a['href']
            if not detail_url.startswith('http'):
                detail_url = urljoin(BASE_URL, detail_url)

            # 封面图片
            img = li_element.find('img')
            cover_url = ''
            if img:
                cover_url = img.get('src') or img.get('data-src') or img.get('data-original') or ''
                if cover_url and not cover_url.startswith('http'):
                    cover_url = urljoin(BASE_URL, cover_url)

            return {
                'name': name,
                'detail_url': detail_url,
                'cover_url': cover_url
            }
        except Exception as e:
            print(f"提取游戏信息失败: {e}")
            return None
    
    def extract_game_info(self, li_element):
        """从li元素中提取游戏信息"""
        try:
            # 游戏名称
            p = li_element.find('p')
            if not p:
                return None
            name = p.get_text(strip=True)
            
            # 详情页链接
            a = li_element.find('a', href=True)
            if not a:
                return None
            detail_url = urljoin(BASE_URL, a['href'])
            
            # 封面图片
            img = li_element.find('img')
            cover_url = ''
            if img:
                cover_url = img.get('src') or img.get('data-src') or ''
                if cover_url and not cover_url.startswith('http'):
                    cover_url = urljoin(BASE_URL, cover_url)
            
            return {
                'name': name,
                'detail_url': detail_url,
                'cover_url': cover_url
            }
        except Exception as e:
            print(f"提取游戏信息失败: {e}")
            return None
    
    def normalize_game_name(self, name):
        """标准化游戏名称"""
        # 移除特殊字符
        name = re.sub(r'[：:？?！!（）()【】\[\]《》<>""'']', '', name)
        # 移除多余空格
        name = re.sub(r'\s+', ' ', name).strip()
        return name
    
    def find_game_by_name(self, search_name):
        """根据名称查找游戏"""
        print(f"  正在查找游戏: {search_name}")

        # 直接搜索游戏（不依赖游戏库）
        return self.search_game_directly(search_name)
    
    def download_game_detail(self, detail_url, game_name):
        """下载游戏详情页"""
        try:
            print(f"  正在下载详情页: {detail_url}")
            response = self.session.get(detail_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 生成临时文件名
            safe_name = re.sub(r'[^\w\s-]', '', game_name).strip()
            temp_file = f"temp_{safe_name}.html"
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            return temp_file
        except Exception as e:
            print(f"  下载详情页失败: {e}")
            return None
    
    def extract_game_details(self, html_file):
        """从详情页提取游戏详细信息"""
        intro = ''
        screenshots = []
        videos = []
        large_cover = ''

        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f, 'html.parser')

            # 提取大尺寸封面图片
            print(f"  正在查找大尺寸封面...")
            cover_selectors = [
                '.Mid_T_L img',  # 主要封面位置
                '.game-cover img',
                '.cover-image img',
                '.main-image img',
                'img[src*="origin_"]'  # 直接查找origin开头的图片
            ]

            for selector in cover_selectors:
                cover_elem = soup.select_one(selector)
                if cover_elem:
                    cover_src = cover_elem.get('src') or cover_elem.get('data-src')
                    if cover_src:
                        # 确保是完整URL
                        if not cover_src.startswith('http'):
                            cover_src = urljoin(BASE_URL, cover_src)

                        # 优先选择origin开头的大图
                        if 'origin_' in cover_src:
                            large_cover = cover_src
                            print(f"  ✓ 找到大尺寸封面: {cover_src}")
                            break
                        elif not large_cover:  # 如果还没有找到大图，先保存这个
                            large_cover = cover_src

            # 提取介绍
            intro_selectors = [
                '.YXXX-B .con p',
                '.game-intro p',
                '.intro-content p',
                '.content p'
            ]

            for selector in intro_selectors:
                intro_elem = soup.select_one(selector)
                if intro_elem:
                    intro = intro_elem.get_text(strip=True)
                    if intro:
                        break
            
            # 提取截图
            screenshot_selectors = [
                '.preview_Imgs_list.jt img',
                '.piclist a:not(.vd)',
                '.screenshot-list img',
                '.game-pics img'
            ]
            
            for selector in screenshot_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    if elem.name == 'img':
                        pic_url = elem.get('data-pic') or elem.get('src') or elem.get('data-src')
                    else:  # a标签
                        pic_url = elem.get('data-pic')
                    
                    if pic_url and pic_url not in screenshots:
                        if not pic_url.startswith('http'):
                            pic_url = urljoin(BASE_URL, pic_url)
                        screenshots.append(pic_url)
                        if len(screenshots) >= 3:
                            break
                
                if screenshots:
                    break
            
            # 提取视频 - 重点关注轮播图中的视频
            print(f"  正在查找视频...")

            # 方法1: 查找.piclist中的所有a标签（轮播图结构）
            piclist = soup.find('div', class_='piclist')
            if piclist:
                all_links = piclist.find_all('a')
                print(f"  在piclist中找到 {len(all_links)} 个链接")

                for i, link in enumerate(all_links):
                    print(f"    检查链接 {i+1}: class={link.get('class', [])}")

                    # 检查是否是视频链接
                    if 'vd' in link.get('class', []):
                        vid = link.get('data-vid')
                        if vid:
                            videos.append(vid)
                            print(f"    ✓ 在位置 {i+1} 找到视频: {vid[:50]}...")
                            break

                    # 检查data-vid属性
                    elif link.get('data-vid'):
                        vid = link.get('data-vid')
                        videos.append(vid)
                        print(f"    ✓ 在位置 {i+1} 找到视频(data-vid): {vid[:50]}...")
                        break
            else:
                print(f"  未找到piclist容器")

            # 方法2: 传统的视频选择器
            if not videos:
                video_selectors = [
                    'a.vd',
                    '.video-list a',
                    '.piclist a.vd',
                    'a[data-vid]',
                    '.video-container a',
                    '.media-video a'
                ]

                for selector in video_selectors:
                    elements = soup.select(selector)
                    print(f"  选择器 '{selector}' 找到 {len(elements)} 个元素")
                    for elem in elements:
                        vid = elem.get('data-vid')
                        if vid and vid not in videos:
                            videos.append(vid)
                            print(f"  ✓ 找到视频: {vid}")
                            break

                    if videos:
                        break

            # 方法3: 查找页面中所有的data-vid属性
            if not videos:
                all_vid_elements = soup.find_all(attrs={'data-vid': True})
                print(f"  页面中共有 {len(all_vid_elements)} 个data-vid元素")
                for elem in all_vid_elements:
                    vid = elem.get('data-vid')
                    if vid and vid not in videos:
                        videos.append(vid)
                        print(f"  ✓ 找到视频: {vid}")
                        break
            
            print(f"  提取结果: 介绍={len(intro)}字符, 截图={len(screenshots)}张, 视频={len(videos)}个, 大封面={'有' if large_cover else '无'}")

        except Exception as e:
            print(f"  提取详细信息失败: {e}")

        return intro, screenshots[:3], videos[:1], large_cover
    
    def process_excel_games(self):
        """处理Excel中的游戏列表"""
        # 检查Excel文件
        if not os.path.exists(EXCEL_PATH):
            raise FileNotFoundError(f'Excel文件不存在: {EXCEL_PATH}')
        
        # 读取Excel
        df = pd.read_excel(EXCEL_PATH)
        if '游戏名称' not in df.columns:
            raise ValueError('Excel中必须有"游戏名称"这一列')
        
        print(f"读取到 {len(df)} 个游戏")
        print("使用直接搜索模式，无需构建游戏库")

        results = []
        
        for idx, row in df.iterrows():
            game_name = str(row['游戏名称']).strip()
            print(f"\n处理游戏 {idx+1}/{len(df)}: {game_name}")
            
            # 查找游戏
            game_info = self.find_game_by_name(game_name)
            
            if game_info:
                # 下载详情页
                temp_file = self.download_game_detail(game_info['detail_url'], game_info['name'])
                
                if temp_file:
                    # 提取详细信息
                    intro, screenshots, videos, large_cover = self.extract_game_details(temp_file)

                    # 删除临时文件
                    try:
                        os.remove(temp_file)
                        print(f"  已删除临时文件: {temp_file}")
                    except:
                        pass

                    # 优先使用大尺寸封面，如果没有则使用搜索结果中的封面
                    final_cover = large_cover if large_cover else game_info['cover_url']

                    result = {
                        '游戏名称': game_name,
                        '匹配到的名称': game_info['name'],
                        '封面': final_cover,
                        '详情页链接': game_info['detail_url'],
                        '介绍': intro,
                        '截图1': screenshots[0] if len(screenshots) > 0 else '',
                        '截图2': screenshots[1] if len(screenshots) > 1 else '',
                        '截图3': screenshots[2] if len(screenshots) > 2 else '',
                        '视频1': videos[0] if len(videos) > 0 else ''
                    }
                else:
                    result = {
                        '游戏名称': game_name,
                        '匹配到的名称': game_info['name'],
                        '封面': game_info['cover_url'],
                        '详情页链接': game_info['detail_url'],
                        '介绍': '下载详情页失败',
                        '截图1': '', '截图2': '', '截图3': '', '视频1': ''
                    }
            else:
                result = {
                    '游戏名称': game_name,
                    '匹配到的名称': '',
                    '封面': '', '详情页链接': '', '介绍': '',
                    '截图1': '', '截图2': '', '截图3': '', '视频1': ''
                }
            
            results.append(result)
            time.sleep(1)  # 避免请求过快
        
        # 保存结果
        result_df = pd.DataFrame(results)
        result_df.to_excel(RESULT_PATH, index=False)
        print(f"\n结果已保存到 {RESULT_PATH}")
        
        return result_df

def main():
    spider = GamerskySpider()
    try:
        result_df = spider.process_excel_games()
        
        # 显示统计信息
        total = len(result_df)
        matched = len(result_df[result_df['匹配到的名称'] != ''])
        print(f"\n处理完成！")
        print(f"总游戏数: {total}")
        print(f"成功匹配: {matched}")
        print(f"匹配率: {matched/total*100:.1f}%")
        
    except Exception as e:
        print(f"处理失败: {e}")

if __name__ == "__main__":
    main()
