.jubao_mode{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 98;
}
.jubao{
    width: 500px;
    height: 518px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 99;
    background: #fff;
    border-radius: 8px;
}

.jubao .header{
    width: 100%;
    height: 60px;
    line-height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #ededed;
    padding: 0 20px;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
    background: #fff;

}
.jubao .header .header_title{
    font-size: 22px;
    font-weight: normal;
    line-height: 60px;
    text-align: center;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    /* 一级文字#222222#D7D7D9 */
    color: #222222;
}
.jubao .header .gz{
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0px;
    font-variation-settings: "opsz" auto;
    /* 四级文字#999999#717173 */
    color: #999999;
    cursor: pointer;
}
.jubao .header .gz img{
    width: 14px;
    height: 14px;
    margin-left: 4px;
}
.jubao .header .close{
    display: block;
    width: 26px;
    height: 60px;
    line-height: 60px;
    color: #ccc;
    font-size: 32px;
    font-family: SimSun;
    text-align: center;
    cursor: pointer;
}
.jubao .list{
    padding: 62px 20px 10px;
    height: 374px;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    user-select: none;
}
.jubao .list::-webkit-scrollbar {
    width: 0;
}
.jubao .list .item{
    margin-bottom: 15px;
}
.jubao .list .item .tit{
    font-size: 15px;
    font-weight: normal;
    line-height: 40px;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    /* 三级文字#666666&#A3A3A6 */
    color: #666666;
}
.jubao .list .item .select{
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}
/* .jubao .list .item .select_item{
    padding-left: 30px;
    width: 150px;
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    color: #222;
    font-size: 16px;
    background: url(//image.gamersky.com/webimg15/user/report/cmtRt-a.png) no-repeat;
    border-radius: 5px;
    cursor: pointer;
} */
.jubao .list .item .select_item{
    width: 150px;
    /* height: 30px; */
    line-height: 30px;
    border-radius: 5px;
    cursor: pointer;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}
.jubao .list .item .select_item span{
    color: #222;
    font-size: 16px;
}
.jubao .list .item .select_item .img{
    width: 30px;
    height: 30px;
    background: url(//image.gamersky.com/webimg15/user/report/cmtRt-a.png) no-repeat;
}
.jubao .list .item .select_item .cur{
    background-position: 0 -30px;
}


.jubao .footer{
    height: 80px;
    box-sizing: border-box;
}
.jubao .footer .btn_box{
    padding: 10px 20px 20px;
    border-top: 2px solid #ededed;
    display: flex;
    justify-content: center;
    gap: 30px;
}
.jubao .footer .btn{
    width: 164px;
    height: 52px;
    line-height: 52px;
    text-align: center;
    cursor: pointer;
}
.jubao .footer .qx{
    font-size: 18px;
    font-weight: normal;
    line-height: 48px;
    text-align: center;
    letter-spacing: 0em;
    /* 四级文字#999999#717173 */
    color: #999999;
    border-radius: 5px;
    opacity: 1;
    box-sizing: border-box;
    /* 四级文字#999999#717173 */
    border: 2px solid #999999;
}
.jubao .footer .qr{
    font-size: 18px;
    font-weight: normal;
    line-height: 48px;
    text-align: center;
    letter-spacing: 0em;
    /* 二级背景色#FFFFFF#222226 */
    color: #FFFFFF;
    border-radius: 5px;
    opacity: 1;
    /* 标准红#EB413D */
    background: #EB413D;
    box-sizing: border-box;
    /* 标准红#EB413D */
    border: 0px solid #EB413D;
}
.jubao .input_tit .title{
    font-size: 15px;
    font-weight: normal;
    line-height: 40px;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    /* 三级文字#666666&#A3A3A6 */
    color: #666666;
}
.jubao .list input{
    padding: 12px;
    width: 100%;
    box-sizing: border-box;
    border: 2px solid #EDEDED;
}
.jubao .list input::placeholder,.jubao .list textarea::placeholder{
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    /* www.gamersky.com/Silver */
    color: #BBBBBB;
}
.jubao .list textarea{
    padding: 12px;
    width: 100%;
    height: 118px;
    box-sizing: border-box;
    border: 2px solid #EDEDED;
    resize:none
}
.jubao .list .liyou{
    position: relative;
}
.jubao .list .liyou .tarea-num{
    padding: 0 5px;
    height: 20px;
    line-height: 20px;
    color: #ccc;
    font-size: 12px;
    font-family: SimSun;
    position: absolute;
    right: 0;
    bottom: 0;
}
.jubao .cmtRt-pic {
    padding: 10px 0 0;
    width: 480px;
    height: auto;
    overflow: hidden;
}
.jubao .cmtRt-pic li {
    float: left;
    margin-right: 10px;
    width: 68px;
    height: 68px;
    overflow: visible;
    position: relative;
}
.jubao .cmtRt-pic li img {
    display: block;
    width: 68px;
    height: 68px;
    border-radius: 3px;
}

.jubao .cmtRt-pic li .picdel {
    display: block;
    width: 16px;
    height: 16px;
    position: absolute;
    right: -5px;
    top: -5px;
    background: url(//image.gamersky.com/webimg15/user/report/cmtRt-del.png) no-repeat;
}
.jubao a {
    text-decoration: none;
}
.jubao .cmtRt-pic li .upPicBtn {
    display: block;
    width: 64px;
    height: 64px;
    border: 2px solid #eee;
    border-radius: 3px;
    position: relative;
}
.jubao .cmtRt-pic li .upPicBtn {
    background: url(//image.gamersky.com/webimg15/user/report/cmtRt-noimg.png) center 10px no-repeat;
}

.jubao .cmtRt-pic li .upPicBtn p {
    position: absolute;
    left: 0;
    bottom: 0;
    margin: 0;
    width: 100%;
    height: 30px;
    line-height: 30px;
    color: #999;
    font-size: 12px;
    font-family: "Microsoft YaHei";
    text-align: center;
}

