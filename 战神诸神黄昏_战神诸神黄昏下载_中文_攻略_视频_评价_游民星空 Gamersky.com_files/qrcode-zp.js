(function($){
/**
 * 参数说明
 * 未添加注释的已写好
 * midurl.mid  还没确定中转页地址，确定后更换
 * midurl.type 如杂谈:ent,新闻中心:news...未来可扩展，目前只加杂谈就是ent
 * midurl.id   文章ID 跳转APP用
 */
  //if(!-[1,]){return;}//IE678隐藏二维码
  return false;
  function codeHtml(txt){
    var htm = '<style>';
    htm += '.codepop {padding:8px; width:90px; height:auto; background:#fff; border:1px solid #ddd; border-radius:4px;}';
    htm += '.codepop {position:fixed; bottom:60px; left:50%; z-index:2000; _position:absolute;_top:expression(eval(document.documentElement.scrollTop));}';
    htm += '.codepop .code {margin-bottom:5px; width:90px; height:90px; background:url(//image.gamersky.com/webimg15/user/center2/loading.gif) center center no-repeat;}';
    htm += '.codepop .code img {display:block; width:90px; height:90px;}';
    htm += '.codepop .code table {width:90px; height:90px;}';
    htm += '.codepop .text {width:90px; height:auto; line-height:20px; color:#07090c; font-size:12px; font-family:Microsoft YaHei;}';
    htm += '.codepop .text i {font-style:normal; color:#e03800;}';
    htm += '.codepop a.close {display:block; width:12px; height:12px; line-height:12px; color:#888; font-size:12px; font-family:SimSun; text-align:center; position:absolute; right:-1px; bottom:-1px;}';
    htm += '.codepop a.close {border-left:1px solid #ddd; border-top:1px solid #ddd; border-radius:3px 0 0 0;}';
    htm += '</style>';
    htm += '<div class="codepop"><div class="code" id="qrcode"></div><div class="text">'+txt+'</div><a href="javascript:;" class="close">×</a></div>';
    return htm;
  }
  var $codeJs = $("#codeJs");
  var type = $codeJs.attr("data-type");
  var tit= $(".tit_CH").text() || $(".CHtit").text() || '';
  var txt='',count=0,okcount=0;
  
  switch (type){
    case 'game' : //众评首页
    case 'gamecon' : //众评内容页
      txt = "使用<i>游民APP</i>与更多<span>"+tit+"</span>玩家品评交流";
      count = 1005106;
      okcount = 1005107;
    break;
    case 'gamezq' : //普通专区
      txt="一手掌握<span>"+tit+"</span>信息尽在<i>游民APP</i>";
      count = 1007599;
      okcount = 1007600;
    break;
  }
  
  $("body").append(codeHtml(txt)); //载入二维码层
  $(".codepop").on("click",".close",function(){$(".codepop").remove()});
  
  var mid = 'http://appapi2.gamersky.com/guide/pc_mid.shtml';
  var oaid = $(".tit_CH").attr("gameid") || gameLib || 0;
  var qrurl = mid + '?oatype=' + type + '&oaid=' + oaid  + '&oacount=' + count+ '&oarel=' + okcount;
  
  $.cachedScript('//j.gamersky.com/g/lib/qrcode/jquery.qrcode.min.js').done(function () {
      new QRCode(document.getElementById("qrcode"), {
          text:qrurl, //生成二维码的内容
          width : 90, //宽
          height : 90, //高
          colorDark : "#000000", //前景色
          colorLight : "#ffffff", //背景色
          correctLevel : QRCode.CorrectLevel.L //精确度L|M|Q|H
      });
  },true);
  
  var width = $codeJs.attr("data-width"),dingwei=0;
  switch (width){
    case '1200' : dingwei=-1860; break;
    case '1000' : dingwei=-1670; break;
    case '950' : dingwei=-1620; break;
    default : dingwei=-1860; break;
  }
  
  function Fnav(){
    var $str=$(".codepop");
    if($str.length==0){return;}
    var sWidth=$(window).width(),sTop=$(window).scrollTop(),fnav=$str.outerWidth(),
      nWidth = dingwei, //主体内容宽度
      tTop=0,    //距离头部200距离显示层
      tLeft=200;   //距离主体内容距离
    var dLeft=sWidth<=(sWidth-nWidth)/2+nWidth+fnav+tLeft?(sWidth-((sWidth-nWidth)/2+nWidth+fnav)):tLeft;
    $str.css({"left":(sWidth-nWidth)/2+nWidth+dLeft});
    if(sTop>=tTop && sWidth>=(nWidth+fnav*2)){
      if($str.css("display")=="none"){$str.fadeIn("slow");}
    }else{
      if($str.css("display")=="block"){$str.fadeOut("slow");}
    }
  }
  $(window).resize(Fnav).scroll(Fnav).trigger("resize");//浮动导航
})(jQuery);