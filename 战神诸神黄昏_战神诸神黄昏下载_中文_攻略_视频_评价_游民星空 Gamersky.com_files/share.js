(function () {
	var isWap = /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent);
	var doc = document;
	var css = location.protocol + '//j.gamersky.com/static/api/css/share.css?v='+dateFtt({f:10});
	$("<link>").attr({rel: "stylesheet",href: css}).appendTo("head");
  $.cachedScript('//j.gamersky.com/g/lib/qrcode/jquery.qrcode.min.js');
  
	var openCss = "height=540,width=720,top=" + (window.screen.height - 600) / 2 + ",left=" + (window.screen.width - 800) / 2 + ",toolbar=no,menubar=no,resizable=yes,location=yes,status=no";
	var ymxkShare = {
		//获取地址传参数
		getQueryString: function (name) {
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
			var r = window.location.search.substr(1).match(reg);
			return r != null ? unescape(r[2]) : null;
		},
		config: function (callback) {
			var shareTit = '',shareDesc = '',sharePic = '',shareUrl = '';
			if ($(".shareConfig").length > 0) {
				shareTit = $(".shareConfig h3").text();
				shareDesc = $(".shareConfig p").text();
				sharePic = $(".shareConfig img").attr("src");
				shareUrl = $(".shareConfig a").attr("href");
			} else if (window._bd_share_config) {
				shareTit = window._bd_share_config.common.bdText;
				shareDesc = window._bd_share_config.common.bdDesc;
				sharePic = window._bd_share_config.common.bdPic;
				shareUrl = window._bd_share_config.common.bdUrl;
			}
      
      shareTit = shareTit || $("title").text() || '';
			shareDesc = shareDesc || $("meta[name='description']").attr("content") || '';
			sharePic = sharePic || $("meta[itemprop='image']").attr("content") || $("body").find("img").attr("src") || '';
			shareUrl = shareUrl || location.href;
      
      sharePic = /https:|http:/.test(sharePic)?sharePic:location.protocol+sharePic;
      
			var title = encodeURIComponent(shareTit),
			bdDesc = encodeURIComponent(shareDesc),
			bdPic = encodeURIComponent(sharePic),
			bdUrl = encodeURIComponent(shareUrl);
			callback(title, bdDesc, bdPic, bdUrl);
		},
		shareHtm: function (shareName) {
      var that = this;
      var shareLogo = "https://image.gamersky.com/webimg15/wap/2019/logo2019.png";
			var appVersion = ymxkShare.getQueryString('appVersion');
			if (appVersion == null || appVersion.length < 1) {
				appVersion = ymxkShare.getQueryString('InGSApp');
			}
			if (appVersion != null) {
				var gsAppFun = new GSApp(),shareInfo = {};
        that.config(function (title, bdDesc, bdPic, bdUrl) {
          shareInfo.platform = shareName; // “qq”，“qqKongJian”，“weiXin”，“pengYouQuan”，“weiBo” 等，
          shareInfo.type = "url"; //可选值有：url，分享页面链接；screenshot，页面截图。
          shareInfo.title = title; // 分享标题
          shareInfo.description = bdDesc; // 分享描述
          shareInfo.url = bdUrl; // 分享链接
          shareInfo.thumbnailURL = bdPic || shareLogo; // 分享图标
          shareInfo.lotteryId = 0;
          gsAppFun.share(shareInfo);
        });
			} else {
				$("body").append('<div class="shareMask"><i></i></div>');
				$(".shareMask").unbind().click(function () {
					$(this).remove();
				});
			}
		},
		//统计
		GetHits: function (cid) {
			var url = '//click.gamersky.com/Common/' + (isWap ? 'GetWapHits' : 'GetHits') + '.aspx';
			$.ajax({type: "GET",dataType: "jsonp",url: url,data: {id: cid,script: 3},success: function () {}});
		},
		//微信扫码
		weixin: function () { //分享弹窗
			var that = this;
			that.GetHits(1287129); //统计
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				if (isWap) {
					that.shareHtm('weiXin');
				} else {
					var htm = '';
					htm += '<div class="bd_weixin_popup">'
					htm += '<div class="bd_weixin_popup_head">';
					htm += '<h3>分享到微信朋友圈</h3><a href="javascript:;" id="bd_weixin_popup_close">×</a>';
					htm += '</div>';
					htm += '<div class="bd_weixin_popup_main" id="ewmimg"></div>';
					htm += '<div class="bd_weixin_popup_foot">打开微信，点击底部的“发现”，<br>使用“扫一扫”即可将网页分享至朋友圈。</div>';
					htm += '</div>';
					$(".bd_weixin_popup").remove();
					$("body").append(htm);
					$("#bd_weixin_popup_close").off("click").on("click", function () {
						$(".bd_weixin_popup").remove();
					});
					new QRCode(doc.getElementById("ewmimg"), {
						text: decodeURIComponent(bdUrl), //生成二维码的内容
						width: 200, //宽
						height: 200, //高
						colorDark: "#000000", //前景色
						colorLight: "#ffffff", //背景色
						correctLevel: QRCode.CorrectLevel.L //精确度L|M|Q|H
					});
				}
			});
		},
		//微信朋友圈
		wxquan: function () {
			var that = this;
			that.GetHits(1287130); //统计
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				if (isWap) {
					that.shareHtm('pengYouQuan');
				} else {
					var i = "http://tieba.baidu.com/f/commit/share/openShareApi?url=" + bdUrl + "&title=" + title + "&desc=" + bdDesc + "&pic=" + bdPic;
					window.open(i, "shareTieBa", openCss);
				}
			});
		},
		//qq好友
		sqq: function () {
			var that = this;
			that.GetHits(1287127); //统计
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				if (isWap) {
					that.shareHtm('qq');
				} else {
          var i = "http://connect.qq.com/widget/shareqq/index.html?url=" + bdUrl + "&sharesource=qzone&title=" + title + "&pics=" + bdPic +"&summary=" + bdDesc;
					window.open(i, "shareQQ", openCss);
				}
			});
		},
		//QQ空间
		qzone: function () {
			var that = this;
			that.GetHits(1287128); //统计
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				if (isWap) {
					that.shareHtm('qqKongJian');
				} else {
					var i = "http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=" + bdUrl + "&title=" + title + "&desc=" + bdDesc + "&pics=" + bdPic;
					window.open(i, "shareQzone", openCss);
				}
			});
		},
		//新浪微博
		tsina: function () {
			var that = this;
			that.GetHits(1287126); //统计
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				if (isWap) {
					that.shareHtm('weiBo');
				} else {
          var i = "http://service.weibo.com/share/share.php?c=nie&content=gb2312&source=nie&url=" + bdUrl + "&title=" + title + "&pic=" + bdPic;
					window.open(i, "shareSina", openCss);
				}
			});
		},
		//人人网
		renren: function () {
			var that = this;
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				var i = "http://widget.renren.com/dialog/share?resourceUrl=" + bdUrl + "&title=" + title + "&description=" + bdDesc + "&pic=" + bdPic;
				window.open(i, "shareRenRen", openCss);
			});
		},
		//百度贴吧
		tieba: function () {
			var that = this;
			that.config(function (title, bdDesc, bdPic, bdUrl) {
				var i = "http://tieba.baidu.com/f/commit/share/openShareApi?url=" + bdUrl + "&title=" + title + "&desc=" + bdDesc + "&pic=" + bdPic;
				window.open(i, "shareTieBa", openCss);
			});
		},
		//分派
		shareTit: {
			tsina: "新浪微博",
			weixin: "微信",
			wxquan: "微信朋友圈",
			sqq: "QQ好友",
			qzone: "QQ空间",
			tqq: "QQ微博",
			tieba: "百度贴吧",
			renren: "人人网",
			linkedIn: "职场社区",
			more: "更多",
			share: "分享",
			count: "统计"
		},
		clickShare: function (name) {
			var that = this;
			$(name).find("a").each(function () {
				var $this = $(this),cmd = $this.attr("data-cmd");
				if (cmd == 'more' || cmd == 'share' || cmd == 'count') {
					$this.attr("title", that.shareTit[cmd]);
				} else {
					$this.attr("title", "分享到" + that.shareTit[cmd]);
				}
			});
			$(document).on("click", ".bdsharebuttonbox a", function (event) {
				event.preventDefault();
				try {that[$(this).attr("data-cmd")]();}
				catch(e) {}
			});
		}
	};

	ymxkShare.clickShare(".bdsharebuttonbox");

})();
