var CopyrightHtm = '<div class="Copyright">';
    CopyrightHtm += '<a href="http://www.gamersky.com/about/index.html" target="_blank">关于本站</a> | ';
    CopyrightHtm += '<a href="http://www.gamersky.com/about/join.html" target="_blank">网站招聘</a> | ';
    CopyrightHtm += '<a href="http://www.gamersky.com/about/contact.html" target="_blank">联系我们</a> | ';
    CopyrightHtm += '<a href="http://www.gamersky.com/about/post.html" target="_blank">玩家投稿</a> | ';
    CopyrightHtm += '<a href="http://weibo.com/mygamersky" target="_blank">新浪微博</a> | ';
    CopyrightHtm += '<a href="https://a.gamersky.com/index.html?14" target="_blank">手机客户端</a><br />';
    CopyrightHtm += 'Copyright©2003-'+(new Date().getFullYear())+' GamerSky.com All rights reserved. 游民星空 版权所有 ';
    CopyrightHtm += '<a href="http://www.miibeian.gov.cn/" target="_blank">冀ICP备05003890号-1</a> ';
    CopyrightHtm += '冀ICP证B2-20180049 ';
    CopyrightHtm += '<a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=冀公网安备13019902000982号" target="_blank">冀公网备13019902000982</a><br />';
    CopyrightHtm+='违法和不良信息举报电话：0311-89619111&nbsp;&nbsp;邮箱：<EMAIL>&nbsp;&nbsp;&nbsp;&nbsp;';
    CopyrightHtm+='从业人员违法违规行为举报：<a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>&nbsp;&nbsp;&nbsp;&nbsp;';
    CopyrightHtm+='未成年人专用举报：<a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>';
    CopyrightHtm += '</div>';
document.write(CopyrightHtm);