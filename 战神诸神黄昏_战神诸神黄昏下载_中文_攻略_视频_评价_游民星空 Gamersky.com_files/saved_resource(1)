function isSecurity(n){if(n.length<6){iss.reset(n.length);return}var t=-1,i=n.search(/[a-zA-Z]/)!=-1?1:0,r=n.search(/[0-9]/)!=-1?1:0,u=n.search(/[^A-Za-z0-9_]/)!=-1?1:0,t=i+r+u;switch(t){case 1:iss.level0();break;case 2:iss.level1();break;case 3:iss.level2();break;default:iss.reset(n.length)}}function Bardisplaynone(){$("#BarBorder_TxtUserPassword").hide()}function Bardisplayshow(){$("#BarBorder_TxtUserPassword").empty();$("#ctl00_CphContent_ValrUserPassword").hide();document.getElementById("BarBorder_TxtUserPassword").style.display="inline"}function batchconfirm(n,t){for(var i,n=arguments.length>0?arguments[0]:"确定要进行此批量操作？",t=arguments.length>1?arguments[1]:"请选择所要操作的记录！",u=!1,r=0;r<document.forms[0].length;r++)if(i=document.forms[0][r],i.type=="checkbox"&&i.name.endWith("CheckBoxButton")&&i.checked){u=!0;break}if(u){if(!confirm(n))return!1}else return alert(t),!1}function loadJs(n,t,i){var u=document.getElementsByTagName("head")[0],r=null;document.getElementById(n)==null?(r=document.createElement("script"),r.setAttribute("src",t),r.setAttribute("id",n),i!=null&&(r.onload=r.onreadystatechange=function(){if(r.ready)return!1;r.readyState&&r.readyState!="loaded"&&r.readyState!="complete"||(r.ready=!0,i())}),u.appendChild(r)):i!=null&&i()}var JSON2,DD_belatedPNG,siteSetup,iss;if(JSON2||(JSON2={}),function(){"use strict";function i(n){return n<10?"0"+n:n}function o(n){return e.lastIndex=0,e.test(n)?'"'+n.replace(e,function(n){var t=s[n];return typeof t=="string"?t:"\\u"+("0000"+n.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+n+'"'}function u(i,f){var s,l,h,a,v=n,c,e=f[i];e&&typeof e=="object"&&typeof e.toJSON=="function"&&(e=e.toJSON(i));typeof t=="function"&&(e=t.call(f,i,e));switch(typeof e){case"string":return o(e);case"number":return isFinite(e)?String(e):"null";case"boolean":case"null":return String(e);case"object":if(!e)return"null";if(n+=r,c=[],Object.prototype.toString.apply(e)==="[object Array]"){for(a=e.length,s=0;s<a;s+=1)c[s]=u(s,e)||"null";return h=c.length===0?"[]":n?"[\n"+n+c.join(",\n"+n)+"\n"+v+"]":"["+c.join(",")+"]",n=v,h}if(t&&typeof t=="object")for(a=t.length,s=0;s<a;s+=1)typeof t[s]=="string"&&(l=t[s],h=u(l,e),h&&c.push(o(l)+(n?": ":":")+h));else for(l in e)Object.prototype.hasOwnProperty.call(e,l)&&(h=u(l,e),h&&c.push(o(l)+(n?": ":":")+h));return h=c.length===0?"{}":n?"{\n"+n+c.join(",\n"+n)+"\n"+v+"}":"{"+c.join(",")+"}",n=v,h}}typeof Date.prototype.toJSON!="function"&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+i(this.getUTCMonth()+1)+"-"+i(this.getUTCDate())+"T"+i(this.getUTCHours())+":"+i(this.getUTCMinutes())+":"+i(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()});var f=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,e=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,n,r,s={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},t;typeof JSON2.stringify!="function"&&(JSON2.stringify=function(i,f,e){var o;if(n="",r="",typeof e=="number")for(o=0;o<e;o+=1)r+=" ";else typeof e=="string"&&(r=e);if(t=f,f&&typeof f!="function"&&(typeof f!="object"||typeof f.length!="number"))throw new Error("JSON2.stringify");return u("",{"":i})});typeof JSON2.parse!="function"&&(JSON2.parse=function(text,reviver){function walk(n,t){var r,u,i=n[t];if(i&&typeof i=="object")for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&(u=walk(i,r),u!==undefined?i[r]=u:delete i[r]);return reviver.call(n,t,i)}var j;if(text=String(text),f.lastIndex=0,f.test(text)&&(text=text.replace(f,function(n){return"\\u"+("0000"+n.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),typeof reviver=="function"?walk({"":j},""):j;throw new SyntaxError("JSON2.parse");})}(),navigator.appName=="Microsoft Internet Explorer"&&navigator.appVersion.split(";")[1].replace(/[ ]/g,"")=="MSIE6.0"){DD_belatedPNG={ns:"DD_belatedPNG",imgSize:{},delay:10,nodesFixed:0,createVmlNameSpace:function(){document.namespaces&&!document.namespaces[this.ns]&&document.namespaces.add(this.ns,"urn:schemas-microsoft-com:vml")},createVmlStyleSheet:function(){var n,t;n=document.createElement("style");n.setAttribute("media","screen");document.documentElement.firstChild.insertBefore(n,document.documentElement.firstChild.firstChild);n.styleSheet&&(n=n.styleSheet,n.addRule(this.ns+"\\:*","{behavior:url(#default#VML)}"),n.addRule(this.ns+"\\:shape","position:absolute;"),n.addRule("img."+this.ns+"_sizeFinder","behavior:none; border:none; position:absolute; z-index:-1; top:-10000px; visibility:hidden;"),this.screenStyleSheet=n,t=document.createElement("style"),t.setAttribute("media","print"),document.documentElement.firstChild.insertBefore(t,document.documentElement.firstChild.firstChild),t=t.styleSheet,t.addRule(this.ns+"\\:*","{display: none !important;}"),t.addRule("img."+this.ns+"_sizeFinder","{display: none !important;}"))},readPropertyChange:function(){var n,i,t;if(n=event.srcElement,n.vmlInitiated){if((event.propertyName.search("background")!=-1||event.propertyName.search("border")!=-1)&&DD_belatedPNG.applyVML(n),event.propertyName=="style.display"){i=n.currentStyle.display=="none"?"none":"block";for(t in n.vml)n.vml.hasOwnProperty(t)&&(n.vml[t].shape.style.display=i)}event.propertyName.search("filter")!=-1&&DD_belatedPNG.vmlOpacity(n)}},vmlOpacity:function(n){if(n.currentStyle.filter.search("lpha")!=-1){var t=n.currentStyle.filter;t=parseInt(t.substring(t.lastIndexOf("=")+1,t.lastIndexOf(")")),10)/100;n.vml.color.shape.style.filter=n.currentStyle.filter;n.vml.image.fill.opacity=t}},handlePseudoHover:function(n){setTimeout(function(){DD_belatedPNG.applyVML(n)},1)},fix:function(n){if(this.screenStyleSheet)for(var i=n.split(","),t=0;t<i.length;t++)this.screenStyleSheet.addRule(i[t],"behavior:expression(DD_belatedPNG.fixPng(this))")},applyVML:function(n){n.runtimeStyle.cssText="";this.vmlFill(n);this.vmlOffsets(n);this.vmlOpacity(n);n.isImg&&this.copyImageBorders(n)},attachHandlers:function(n){var f,t,e,i,r,u;if(f=this,t={resize:"vmlOffsets",move:"vmlOffsets"},n.nodeName=="A"){i={mouseleave:"handlePseudoHover",mouseenter:"handlePseudoHover",focus:"handlePseudoHover",blur:"handlePseudoHover"};for(r in i)i.hasOwnProperty(r)&&(t[r]=i[r])}for(u in t)t.hasOwnProperty(u)&&(e=function(){f[t[u]](n)},n.attachEvent("on"+u,e));n.attachEvent("onpropertychange",this.readPropertyChange)},giveLayout:function(n){n.style.zoom=1;n.currentStyle.position=="static"&&(n.style.position="relative")},copyImageBorders:function(n){var i,t;i={borderStyle:!0,borderWidth:!0,borderColor:!0};for(t in i)i.hasOwnProperty(t)&&(n.vml.color.shape.style[t]=n.currentStyle[t])},vmlFill:function(n){if(n.currentStyle){var i,u,r,f,t,e;i=n.currentStyle}else return;for(f in n.vml)n.vml.hasOwnProperty(f)&&(n.vml[f].shape.style.zIndex=i.zIndex);n.runtimeStyle.backgroundColor="";n.runtimeStyle.backgroundImage="";u=!0;(i.backgroundImage!="none"||n.isImg)&&(n.isImg?n.vmlBg=n.src:(n.vmlBg=i.backgroundImage,n.vmlBg=n.vmlBg.substr(5,n.vmlBg.lastIndexOf('")')-5)),r=this,r.imgSize[n.vmlBg]||(t=document.createElement("img"),r.imgSize[n.vmlBg]=t,t.className=r.ns+"_sizeFinder",t.runtimeStyle.cssText="behavior:none; position:absolute; left:-10000px; top:-10000px; border:none; margin:0; padding:0;",e=function(){this.width=this.offsetWidth;this.height=this.offsetHeight;r.vmlOffsets(n)},t.attachEvent("onload",e),t.src=n.vmlBg,t.removeAttribute("width"),t.removeAttribute("height"),document.body.insertBefore(t,document.body.firstChild)),n.vml.image.fill.src=n.vmlBg,u=!1);n.vml.image.fill.on=!u;n.vml.image.fill.color="none";n.vml.color.shape.style.backgroundColor=i.backgroundColor;n.runtimeStyle.backgroundImage="none";n.runtimeStyle.backgroundColor="transparent"},vmlOffsets:function(n){var u,t,f,l,i,o,e,s,h,r,c;if(u=n.currentStyle,t={W:n.clientWidth+1,H:n.clientHeight+1,w:this.imgSize[n.vmlBg].width,h:this.imgSize[n.vmlBg].height,L:n.offsetLeft,T:n.offsetTop,bLW:n.clientLeft,bTW:n.clientTop},f=t.L+t.bLW==1?1:0,l=function(n,t,i,r,u,f){n.coordsize=r+","+u;n.coordorigin=f+","+f;n.path="m0,0l"+r+",0l"+r+","+u+"l0,"+u+" xe";n.style.width=r+"px";n.style.height=u+"px";n.style.left=t+"px";n.style.top=i+"px"},l(n.vml.color.shape,t.L+(n.isImg?0:t.bLW),t.T+(n.isImg?0:t.bTW),t.W-1,t.H-1,0),l(n.vml.image.shape,t.L+t.bLW,t.T+t.bTW,t.W,t.H,1),i={X:0,Y:0},n.isImg)i.X=parseInt(u.paddingLeft,10)+1,i.Y=parseInt(u.paddingTop,10)+1;else for(h in i)i.hasOwnProperty(h)&&this.figurePercentage(i,t,h,u["backgroundPosition"+h]);n.vml.image.fill.position=i.X/t.W+","+i.Y/t.H;o=u.backgroundRepeat;e={T:1,R:t.W+f,B:t.H,L:1+f};s={X:{b1:"L",b2:"R",d:"W"},Y:{b1:"T",b2:"B",d:"H"}};o!="repeat"||n.isImg?(r={T:i.Y,R:i.X+t.w,B:i.Y+t.h,L:i.X},o.search("repeat-")!=-1&&(c=o.split("repeat-")[1].toUpperCase(),r[s[c].b1]=1,r[s[c].b2]=t[s[c].d]),r.B>t.H&&(r.B=t.H),n.vml.image.shape.style.clip="rect("+r.T+"px "+(r.R+f)+"px "+r.B+"px "+(r.L+f)+"px)"):n.vml.image.shape.style.clip="rect("+e.T+"px "+e.R+"px "+e.B+"px "+e.L+"px)"},figurePercentage:function(n,t,i,r){var u,f;f=!0;u=i=="X";switch(r){case"left":case"top":n[i]=0;break;case"center":n[i]=.5;break;case"right":case"bottom":n[i]=1;break;default:r.search("%")!=-1?n[i]=parseInt(r,10)/100:f=!1}return n[i]=Math.ceil(f?t[u?"W":"H"]*n[i]-t[u?"w":"h"]*n[i]:parseInt(r,10)),n[i]%2==0&&n[i]++,n[i]},fixPng:function(n){n.style.behavior="none";var i,u,f,t,r;if(n.nodeName!="BODY"&&n.nodeName!="TD"&&n.nodeName!="TR"){if(n.isImg=!1,n.nodeName=="IMG")if(n.src.toLowerCase().search(/\.png$/)!=-1)n.isImg=!0,n.style.visibility="hidden";else return;else if(n.currentStyle.backgroundImage.toLowerCase().search(".png")==-1)return;i=DD_belatedPNG;n.vml={color:{},image:{}};u={shape:{},fill:{}};for(t in n.vml)if(n.vml.hasOwnProperty(t)){for(r in u)u.hasOwnProperty(r)&&(f=i.ns+":"+r,n.vml[t][r]=document.createElement(f));n.vml[t].shape.stroked=!1;n.vml[t].shape.appendChild(n.vml[t].fill);n.parentNode.insertBefore(n.vml[t].shape,n)}n.vml.image.shape.fillcolor="none";n.vml.image.fill.type="tile";n.vml.color.fill.on=!1;i.attachHandlers(n);i.giveLayout(n);i.giveLayout(n.offsetParent);n.vmlInitiated=!0;i.applyVML(n)}}};try{document.execCommand("BackgroundImageCache",!1,!0)}catch(r){}DD_belatedPNG.createVmlNameSpace();DD_belatedPNG.createVmlStyleSheet()}(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):n(jQuery)})(function(n){function u(n){return n}function f(n){return decodeURIComponent(n.replace(r," "))}function i(n){n.indexOf('"')===0&&(n=n.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{return t.json?JSON.parse(n):n}catch(i){}}var r=/\+/g,t=n.cookie=function(r,e,o){var l,h,s,y;if(e!==undefined)return o=n.extend({},t.defaults,o),typeof o.expires=="number"&&(l=o.expires,h=o.expires=new Date,h.setDate(h.getDate()+l)),e=t.json?JSON.stringify(e):String(e),document.cookie=[t.raw?r:encodeURIComponent(r),"=",t.raw?e:encodeURIComponent(e),o.expires?"; expires="+o.expires.toUTCString():"",o.path?"; path="+o.path:"",o.domain?"; domain="+o.domain:"",o.secure?"; secure":""].join("");var a=t.raw?u:f,v=document.cookie.split("; "),c=r?undefined:{};for(s=0,y=v.length;s<y;s++){var p=v[s].split("="),w=a(p.shift()),b=a(p.join("="));if(r&&r===w){c=i(b);break}r||(c[w]=i(b))}return c};t.defaults={};n.removeCookie=function(t,i){return n.cookie(t)!==undefined?(n.cookie(t,"",n.extend({},i,{expires:-1})),!0):!1}}),function(n){n.fn.__bind__=n.fn.bind;n.fn.__unbind__=n.fn.unbind;n.fn.__find__=n.fn.find;var t={version:"0.7.9",override:/keypress|keydown|keyup/g,triggersMap:{},specialKeys:{27:"esc",9:"tab",32:"space",13:"return",8:"backspace",145:"scroll",20:"capslock",144:"numlock",19:"pause",45:"insert",36:"home",46:"del",35:"end",33:"pageup",34:"pagedown",37:"left",38:"up",39:"right",40:"down",109:"-",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",191:"/"},shiftNums:{"`":"~","1":"!","2":"@","3":"#","4":"$","5":"%","6":"^","7":"&","8":"*","9":"(","0":")","-":"_","=":"+",";":":","'":'"',",":"<",".":">","/":"?","\\":"|"},newTrigger:function(n,t,i){var r={};return r[n]={},r[n][t]={cb:i,disableInInput:!1},r}};return t.specialKeys=n.extend(t.specialKeys,{96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/"}),n.fn.find=function(t){return this.query=t,n.fn.__find__.apply(this,arguments)},n.fn.unbind=function(i,r,u){var o,e,f;if(n.isFunction(r)&&(u=r,r=null),r&&typeof r=="string")for(o=(this.prevObject&&this.prevObject.query||this[0].id&&this[0].id||this[0]).toString(),e=i.split(" "),f=0;f<e.length;f++)delete t.triggersMap[o][e[f]][r];return this.__unbind__(i,u)},n.fn.bind=function(i,r,u){var c=i.match(t.override),l,v,a,h;if(n.isFunction(r)||!c)return this.__bind__(i,r,u);if(l=null,v=n.trim(i.replace(t.override,"")),v&&(l=this.__bind__(v,r,u)),typeof r=="string"&&(r={combi:r}),r.combi)for(a=0;a<c.length;a++){var e=c[a],o=r.combi.toLowerCase(),s=t.newTrigger(e,o,u),f=(this.prevObject&&this.prevObject.query||this[0].id&&this[0].id||this[0]).toString();s[e][o].disableInInput=r.disableInInput;t.triggersMap[f]?t.triggersMap[f][e]||(t.triggersMap[f][e]=s[e]):t.triggersMap[f]=s;h=t.triggersMap[f][e][o];h?h.constructor!==Array?t.triggersMap[f][e][o]=[h]:t.triggersMap[f][e][o][h.length]=s[e][o]:t.triggersMap[f][e][o]=[s[e][o]];this.each(function(){var t=n(this);t.attr("hkId")&&t.attr("hkId")!==f&&(f=t.attr("hkId")+";"+f);t.attr("hkId",f)});l=this.__bind__(c.join(" "),r,t.handler)}return l},t.findElement=function(t){if(!n(t).attr("hkId")&&(n.browser.opera||n.browser.safari))while(!n(t).attr("hkId")&&t.parentNode)t=t.parentNode;return t},t.handler=function(i){var k=t.findElement(i.currentTarget),h=n(k),o=h.attr("hkId"),f,e,c,r,l;if(o){o=o.split(";");var v=i.which,y=i.type,a=t.specialKeys[v],s=!a&&String.fromCharCode(v).toLowerCase(),p=i.shiftKey,w=i.ctrlKey,b=i.altKey||i.originalEvent.altKey,u=null;for(r=0;r<o.length;r++)if(t.triggersMap[o[r]][y]){u=t.triggersMap[o[r]][y];break}if(u&&(p||w||b?(e="",b&&(e+="alt+"),w&&(e+="ctrl+"),p&&(e+="shift+"),f=u[e+a],f||s&&(f=u[e+s]||u[e+t.shiftNums[s]]||e==="shift+"&&u[t.shiftNums[s]])):f=u[a]||s&&u[s],f)){for(c=!1,r=0;r<f.length;r++){if(f[r].disableInInput&&(l=n(i.target),h.is("input")||h.is("textarea")||h.is("select")||l.is("input")||l.is("textarea")||l.is("select")))return!0;c=c||f[r].cb.apply(this,[i])}return c}}},window.hotkeys=t,n}(jQuery);siteSetup={sitePath:"//www.gamersky.com/",ajaxPath:"/ajax.aspx",skinPath:"//www.gamersky.com/Template/Default/mbk/gscss/"},function(n){function b(){if(n.browser.msie){var t=n(document).height(),i=n(window).height();return[window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,t-i<20?i:t]}return[n(document).width(),n(document).height()]}function o(t){if(t)return t.call(n.mask)}function k(t,i){var r=this,s=t.add(r),y=n(window),o,u,h,f=n.pe.expose&&(i.mask||i.expose),c=Math.random().toString().slice(10),l,e,p;if(f&&(typeof f=="string"&&(f={color:f}),f.closeOnClick=f.closeOnEsc=!1),l=i.target||t.attr("rel"),u=l?n(l):null||t,i.render&&(u.html(""),e='<div style="width:'+i.width+"px;height:"+i.height+'px" class="overlay">',e+='<div class="overlay_header"><span class="overlay_header_title_sign"><\/span><h3 class="overlay_header_title">',e+=i.title,e+='<\/h3><span class="overlay_header_background_right"><\/span>',e+='<a class="overlay_header_close" href="javascript:void(0)"><\/a><\/div>',e+='<div class="overlay_body"><iframe height="'+(i.height-30)+'" frameborder="0" width="100%" src="',e+=i.loadUrl,e+=' "marginwidth="0" marginheight="0"><\/iframe><iframe id="iframe_IE6_Z-Index" width="'+i.width+'" frameborder=0 height="'+i.height+'" style="position:absolute; top:0px; left:0px; z-index:-1; border-style:none;"><\/iframe><\/div>',u.append(e)),!u.length)throw"Could not find Overlay: "+l;t&&t.index(u)==-1&&t.click(function(n){return r.load(n),n.preventDefault()});n.extend(r,{load:function(t){var o;if(r.isOpened())return r;if(o=a[i.effect],!o)throw'Overlay: cannot find effect : "'+i.effect+'"';if(i.oneInstance&&n.each(v,function(){this.close(t)}),t=t||n.Event(),t.type="onBeforeLoad",s.trigger(t),t.isDefaultPrevented())return r;h=!0;f&&n(u).expose(f);var e=i.top,l=i.left,p=u.outerWidth({margin:!0}),w=u.outerHeight({margin:!0});if(typeof e=="string"&&(e=e=="center"?Math.max((y.height()-i.height)/2,0):parseInt(e,10)/100*y.height()),l=="center"&&(l=Math.max((y.width()-i.width)/2,0)),o[0].call(r,{top:e,left:l},function(){h&&(t.type="onLoad",s.trigger(t))}),s.css("display","block"),f&&i.closeOnClick)n.mask.getMask().one("click",r.close);return i.closeOnClick&&n(document).bind("click."+c,function(t){n(t.target).parents(u).length||r.close(t)}),i.closeOnEsc&&n(document).bind("keydown."+c,function(n){n.keyCode==27&&r.close(n)}),r},close:function(t){return r.isOpened()?(t=t||n.Event(),t.type="onBeforeClose",s.trigger(t),t.isDefaultPrevented())?void 0:(h=!1,a[i.effect][1].call(r,function(){t.type="onClose";s.trigger(t)}),n(document).unbind("click."+c).unbind("keydown."+c),f&&n.mask.close(),r):r},getOverlay:function(){return u},getTrigger:function(){return t},getClosers:function(){return o},isOpened:function(){return h},getConf:function(){return i}});n.each("onBeforeLoad,onStart,onLoad,onBeforeClose,onClose".split(","),function(t,u){n.isFunction(i[u])&&n(r).bind(u,i[u]);r[u]=function(t){return n(r).bind(u,t),r}});p=u.find(".overlay_header");u.easyDrag(p);o=u.find(i.close||".overlay_header_close"||".close");o.length||i.close||(o=n('<a class="close"><\/a>'),u.prepend(o));o.click(function(n){r.close(n)});i.load&&r.load()}function d(t,i,r){var u=this,s=t.add(this),f=t.find(r.tabs),e=i.jquery?i:t.children(i),o;f.length||(f=t.children());e.length||(e=t.parent().find(i));e.length||(e=n(i));n.extend(this,{click:function(t,i){var e=f.eq(t),h;if(typeof t=="string"&&t.replace("#","")&&(e=f.filter("[href*="+t.replace("#","")+"]"),t=Math.max(f.index(e),0)),r.rotate){if(h=f.length-1,t<0)return u.click(h,i);if(t>h)return u.click(0,i)}if(!e.length){if(o>=0)return u;t=r.initialIndex;e=f.eq(t)}return t===o?u:(i=i||n.Event(),i.type="onBeforeClick",s.trigger(i,[t]),i.isDefaultPrevented())?void 0:(y[r.effect].call(u,t,function(){i.type="onClick";s.trigger(i,[t])}),o=t,f.removeClass(r.current),e.addClass(r.current),u)},getConf:function(){return r},getTabs:function(){return f},getPanes:function(){return e},getCurrentPane:function(){return e.eq(o)},getCurrentTab:function(){return f.eq(o)},getIndex:function(){return o},next:function(){return u.click(o+1)},prev:function(){return u.click(o-1)},destroy:function(){return f.unbind(r.event).removeClass(r.current),e.find("a[href^=#]").unbind("click.T"),u}});n.each("onBeforeClick,onClick".split(","),function(t,i){n.isFunction(r[i])&&n(u).bind(i,r[i]);u[i]=function(t){return n(u).bind(i,t),u}});r.history&&n.fn.history&&(n.pe.history.init(f),r.event="history");f.each(function(t){n(this).bind(r.event,function(n){return u.click(t,n),n.preventDefault()})});e.find("a[href^=#]").bind("click.T",function(t){u.click(n(this).attr("href"),t)});location.hash?u.click(location.hash):(r.initialIndex===0||r.initialIndex>0)&&u.click(r.initialIndex)}var t,c,f,e,l,v,a,y,p;n.pe=n.pe||{version:"0.3.7"};n.fn.easyDrag=function(n){return u(this,n,"d")};n.fn.easyResize=function(n){return u(this,n,"r")};n.dragAndResize={dragAndResize:{},e:0,drag:function(n){return i.k=="d"?r.css({left:i.X+n.pageX-i.pX,top:i.Y+n.pageY-i.pY}):r.css({width:Math.max(n.pageX-i.pX+i.W,0),height:Math.max(n.pageY-i.pY+i.H,0)}),!1},stop:function(){n(document).unbind("mousemove",s.drag).unbind("mouseup",s.stop)}};var s=n.dragAndResize,i=s.dragAndResize,r=s.e,u=function(t,u,f){return t.each(function(){u=u?n(u,t):t;u.css("cursor","move");u.bind("mousedown",{e:t,k:f},function(t){var f=t.data,u={};if(r=f.e,r.css("position")!="relative")try{r.position(u)}catch(e){}return i={X:u.left||h("left")||0,Y:u.top||h("top")||0,W:h("width")||r[0].scrollWidth||0,H:h("height")||r[0].scrollHeight||0,pX:t.pageX,pY:t.pageY,k:f.k},r.css({}),n(document).mousemove(n.dragAndResize.drag).mouseup(n.dragAndResize.stop),!1})})},h=function(n){return parseInt(r.css(n))||!1},w;w=n.pe.expose={conf:{maskId:"expose",loadSpeed:"slow",closeSpeed:"fast",closeOnClick:!0,closeOnEsc:!0,zIndex:9998,opacity:.9,startOpacity:0,color:"#000",onLoad:null,onClose:null}};n.mask={load:function(i,r){if(f)return this;typeof i=="string"&&(i={color:i});i=i||e;e=i=n.extend(n.extend({},w.conf),i);t=n("#"+i.maskId);t.length||(t=n("<div/>").attr("id",i.maskId),n("body").append(t));var u=b();return(t.css({position:"absolute",top:0,left:0,width:u[0],height:u[1],display:"none",opacity:i.startOpacity,zIndex:i.zIndex}),i.color&&t.css("backgroundColor",i.color),o(i.onBeforeLoad)===!1)?this:(i.closeOnEsc&&n(document).bind("keydown.mask",function(t){t.keyCode==27&&n.mask.close(t)}),i.closeOnClick&&t.bind("click.mask",function(t){n.mask.close(t)}),n(window).bind("resize.mask",function(){n.mask.fit()}),r&&r.length&&(l=r.eq(0).css("zIndex"),n.each(r,function(){var t=n(this);/relative|absolute|fixed/i.test(t.css("position"))||t.css("position","relative")}),c=r.css({zIndex:Math.max(i.zIndex+1,l=="auto"?0:l)})),t.css({display:"block"}).fadeTo(i.loadSpeed,i.opacity,function(){n.mask.fit();o(i.onLoad)}),f=!0,this)},close:function(){if(f){if(o(e.onBeforeClose)===!1)return this;t.fadeOut(e.closeSpeed,function(){o(e.onClose);c&&c.css({zIndex:l})});n(document).unbind("keydown.mask");t.unbind("click.mask");n(window).unbind("resize.mask");f=!1}return this},fit:function(){if(f){var n=b();t.css({width:n[0],height:n[1]})}},getMask:function(){return t},isLoaded:function(){return f},getConf:function(){return e},getExposed:function(){return c}};n.fn.mask=function(t){return n.mask.load(t),this};n.fn.expose=function(t){return n.mask.load(t,this),this};n.pe.overlay={addEffect:function(n,t,i){a[n]=[t,i]},conf:{close:null,closeOnClick:!0,closeOnEsc:!0,closeSpeed:"fast",effect:"default",render:!1,width:500,height:360,title:"标题",loadUrl:"http://www.powereasy.net",fixed:!n.browser.msie||n.browser.version>6,left:"center",load:!1,mask:null,oneInstance:!0,speed:"normal",target:null,top:"center"}};v=[];a={};n.pe.overlay.addEffect("default",function(t,i){var r=this.getConf(),u=n(window);r.fixed||(t.top+=u.scrollTop(),t.left+=u.scrollLeft());t.position=r.fixed?"fixed":"absolute";this.getOverlay().css(t).fadeIn(r.speed,i)},function(n){this.getOverlay().fadeOut(this.getConf().closeSpeed,n)});n.fn.overlay=function(t){var i=this.data("overlay");return i&&this.removeData("overlay"),n.isFunction(t)&&(t={onBeforeLoad:t}),t=n.extend(!0,{},n.pe.overlay.conf,t),this.each(function(){i=new k(n(this),t);v.push(i);n(this).data("overlay",i)}),t.api?i:this};n.pe.tabs={conf:{tabs:"a",current:"current",onBeforeClick:null,onClick:null,effect:"default",initialIndex:0,event:"click",rotate:!1,history:!1},addEffect:function(n,t){y[n]=t}};y={"default":function(n,t){this.getPanes().hide().eq(n).show();t.call()},fade:function(n,t){var r=this.getConf(),u=r.fadeOutSpeed,i=this.getPanes();u?i.fadeOut(u):i.hide();i.eq(n).fadeIn(r.fadeInSpeed,t)},slide:function(n,t){this.getPanes().slideUp(200);this.getPanes().eq(n).slideDown(400,t)},ajax:function(n,t){this.getPanes().eq(0).load(this.getTabs().eq(n).attr("href"),t)}};n.pe.tabs.addEffect("horizontal",function(t,i){p||(p=this.getPanes().eq(0).width());this.getCurrentPane().animate({width:0},function(){n(this).hide()});this.getPanes().eq(t).animate({width:p},function(){n(this).show();i.call()})});n.fn.tabs=function(t,i){var r=this.data("tabs");return r&&(r.destroy(),this.removeData("tabs")),n.isFunction(i)&&(i={onBeforeClick:i}),i=n.extend({},n.pe.tabs.conf,i),this.each(function(){r=new d(n(this),t,i);n(this).data("tabs",r)}),i.api?r:this};n.fn.extend({jsRightMenu:function(t){return t=n.extend({menuList:[]},t),n("#div_RightMenu").size()==0,n(document.body).append('<div class="div_RightMenu" id="div_RightMenu"><\/div>'),n("#div_RightMenu").hide(),this.each(function(){this.oncontextmenu=function(){var o=t.menuList.length,s="",i,r;if(o>0){for(i=0;i<o;i++)s+='<div class="divMenuItem" id="divMenuItem'+n(this).attr("id")+'" onclick="'+t.menuList[i].clickEvent+'"  onmouseover="'+t.menuList[i].mouseoverEvent+'" onmouseout="'+t.menuList[i].mouseoutEvent+'">'+t.menuList[i].menuName+"<\/div>";n("#div_RightMenu").html(s);n("#div_RightMenu").hide()}if(r=n("#div_RightMenu"),r.size()>0){r.hide();var h=arguments[0]||window.event,f=h.clientX,e=h.clientY,a=document.documentElement.clientWidth-f,v=document.documentElement.clientHeight-e,u=r.get(0),c=0,l=0;return c=a<u.offsetWidth?document.documentElement.scrollLeft+f-u.offsetWidth:document.documentElement.scrollLeft+f,l=v<u.offsetHeight?document.documentElement.scrollTop+e-u.offsetHeight:document.documentElement.scrollTop+e,r.css({top:l+"px",left:c+"px"}),r.show(),!1}};document.onclick=function(){var t=n("#div_RightMenu");t.size()>0&&t.hide()}})}});n.fn.extend({check:function(){return this.each(function(){this.checked=!0})},uncheck:function(){return this.each(function(){this.checked=!1})},inverse:function(){return this.each(function(){this.checked=this.checked==!0?!1:!0})},shiftSelect:function(t){var r=this,i;n(this).click(function(f){var h,c;if(!i){i=this;return}if(f.shiftKey){var e=r.index(this),s=r.index(i),l=i.checked;if(e==s)return!0;for(h=Math.max(e,s),c=Math.min(e,s),u=c;u<=h;u++)r[u].checked=l;n.isFunction(t)&&o(t)}i=this})},lostFocus:function(){n(this).focus(function(){this.blur()})},addFavorite:function(){n(this).click(function(){var t=n("title").text();document.all?window.external.addFavorite(location.href,t):window.sidebar?window.sidebar.addPanel(t,location.href,null):alert("您可以尝试通过快捷键CTRL + D 加入到收藏夹")})},setHomePage:function(){n(this).click(function(){if(document.all)document.body.style.behavior="url(#default#homepage)",document.body.setHomePage(location.href);else if(window.sidebar){if(window.netscape)try{netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect")}catch(t){alert("该操作被浏览器拒绝，如果想启用该功能，请在地址栏内输入 about:config,然后将项 signed.applets.codebase_principal_support 值该为true");return}var n=Components.classes["@mozilla.org/preferences-service;1"].getService(Components.interfaces.nsIPrefBranch);n.setCharPref("browser.startup.homepage",location.href)}})},imageResize:function(t){var i=jQuery.extend({height:500,width:660},t);this.each(function(){var t=i.height,r=i.width,u=n(this).height(),f=n(this).width(),e=Math.ceil,o=Math.floor;u<=t||f<=r||(u>=f?r=o(e(f/u*t)):t=o(e(u/f*r)),n(this).attr({height:t,width:r}).css({height:t+"px",width:r+"px"}))})},useKeypressSubmit:function(t){n(this).keypress(function(i){return i.which&&i.which==13||i.keyCode&&i.keyCode==13?(n(t).click(),!1):!0})}});n.extend(n.pe,{htmlEncode:function(t){var i=function(t){var i=n("<span>");return i.html(t),i.html()},r=i("\n").toLowerCase()=="<br>"?function(n){return i(n).replace(/<br>/gi,"\n")}:i,u=i(">")==">"?function(n){return r(n).replace(/>/g,"&gt;")}:r,f=i("  ")=="&nbsp; "?function(n){return u(n).replace(/&nbsp;/g," ")}:u;return this.htmlEncode=f,this.htmlEncode(t)},random:function(n){return Math.floor(n*(Math.random()%1))},cookie:function(t,i,r){var e,u,o,s,f,h;if(typeof i!="undefined"){r=r||{};i===null&&(i="",r.expires=-1);e="";r.expires&&(typeof r.expires=="number"||r.expires.toUTCString)&&(typeof r.expires=="number"?(u=new Date,u.setTime(u.getTime()+r.expires*864e5)):u=r.expires,e="; expires="+u.toUTCString());var c=r.path?"; path="+r.path:"",l=r.domain?"; domain="+r.domain:"",a=r.secure?"; secure":"";document.cookie=[t,"=",encodeURIComponent(i),e,c,l,a].join("")}else{if(o=null,document.cookie&&document.cookie!="")for(s=document.cookie.split(";"),f=0;f<s.length;f++)if(h=n.trim(s[f]),h.substring(0,t.length+1)==t+"="){o=decodeURIComponent(h.substring(t.length+1));break}return o}},stringToJSON:function(string){return eval("("+string+")")},jsonToString:function(n){var t=this,r,e,i,u,f;switch(typeof n){case"string":return'"'+n.replace(/(["\\])/g,"\\$1")+'"';case"array":return"["+n.map(t.jsonToString).join(",")+"]";case"object":if(n instanceof Array){for(r=[],e=n.length,i=0;i<e;i++)r.push(t.jsonToString(n[i]));return"["+r.join(",")+"]"}if(n==null)return"null";u=[];for(f in n)u.push(t.jsonToString(f)+":"+t.jsonToString(n[f]));return"{"+u.join(",")+"}";case"number":return n;case!1:return n}},ajax:function(t,i){var r;if(typeof t=="string"&&t!="undefind"){var e={url:siteSetup.ajaxPath,data:"",type:"POST",dataType:"xml",params:{}},f=function(t,i){var u="";for(var r in t.params)u+=i?"<attrib><"+r+">"+n.pe.htmlEncode(t.params[r])+"<\/"+r+"><\/attrib>":"<"+r+">"+n.pe.htmlEncode(t.params[r])+"<\/"+r+">";return u},u=n.extend(e,i),t=n.trim(t).toLowerCase();switch(t){case"accesslabel":t="updatelabel";break;case"accesspage":t="updatepage";break;case"checkuserlogin":t="logincheck";break;case"checkloginvalidate":t="EnableValidCode";break;case"logout":t="userlogout"}r="<root><type>"+t+"<\/type>";switch(t){case"updatelabel":r+="<labelname>"+u.labelName+"<\/labelname>";r+="<currentpage>"+u.currentPage+"<\/currentpage>";r+=f(u,!0);break;case"updatepage":r+="<labelname>"+u.labelName+"<\/labelname>";r+="<sourcename>"+u.pageName+"<\/sourcename>";r+="<pagesize>"+u.pageSize+"<\/pagesize>";r+="<currentpage>"+u.currentPage+"<\/currentpage>";r+="<total>"+u.recordCount+"<\/total>";break;default:r+=f(u,!1)}r+="<\/root>";u.data=r;n.ajax(u)}},refreshValidateCode:function(t){var i=siteSetup.sitePath+"Controls/ValidateCodeImage.aspx?code="+n.pe.random(100);n(t).attr("src",i)},replaceUserInfoVariable:function(n,t){return n=n.replace("{username}",t.find("username").text()),n=n.replace("{experience}",t.find("exp").text()),n=n.replace("{message}",t.find("msg").text()),n=n.replace("{loginCount}",t.find("logintimes").text()),n=n.replace("{pointName}",t.find("pointname").text()),n=n.replace("{point}",t.find("point").text()),n=n.replace("{pointUnit}",t.find("pointunit").text()),n=n.replace("{signinArticle}",t.find("signincontent").text()),n.replace("{balances}",t.find("balance").text())},supplyDemandInfoVariable:function(n,t){return n=n.replace("{username}",t.find("username").text()),n=n.replace("{experience}",t.find("exp").text()),n=n.replace("{message}",t.find("msg").text()),n=n.replace("{loginCount}",t.find("logintimes").text()),n=n.replace("{pointName}",t.find("pointname").text()),n=n.replace("{point}",t.find("point").text()),n=n.replace("{signinArticle}",t.find("signincontent").text()),n=n.replace("{balances}",t.find("balance").text()),n=n.replace(new RegExp("{indexurl}","g"),t.find("indexurl").text()),n=n.replace(new RegExp("{contacturl}","g"),t.find("contacturl").text()),n=n.replace("{supplylisturl}",t.find("supplylisturl").text()),n=n.replace("{companyurl}",t.find("companyurl").text()),n=n.replace("{contact}",t.find("contact").text()),n.replace(new RegExp("{companyname}","g"),t.find("companyname").text())}})}(jQuery),function(n){function f(n,t){return 32-new Date(n,t,32).getDate()}function e(n,t){for(n=""+n,t=t||2;n.length<t;)n="0"+n;return n}function o(n,t,i){var o=n.getDate(),s=n.getDay(),u=n.getMonth(),f;return n=n.getFullYear(),f={d:o,dd:e(o),ddd:r[i].shortDays[s],dddd:r[i].days[s],m:u+1,mm:e(u+1),mmm:r[i].shortMonths[u],mmmm:r[i].months[u],yy:String(n).slice(2),yyyy:n},t=t.replace(l,function(n){return n in f?f[n]:n.slice(1,n.length-1)}),a.html(t).html()}function t(n){return parseInt(n,10)}function s(n,t){return n.getFullYear()===t.getFullYear()&&n.getMonth()==t.getMonth()&&n.getDate()==t.getDate()}function i(n){if(n){if(n.constructor==Date)return n;if(typeof n=="string"){var i=n.split("-");if(i.length==3)return new Date(t(i[0]),t(i[1])-1,t(i[2]));if(!/^-?\d+$/.test(n))return;n=t(n)}return i=new Date,i.setDate(i.getDate()+n),i}}function v(u,e){function ht(t,i,r){y=t;g=t.getFullYear();nt=t.getMonth();ft=t.getDate();r=r||n.Event("api");r.type="change";tt.trigger(r,[t]);r.isDefaultPrevented()||(u.val(o(t,i.format,i.lang)),u.data("date",t),a.hide(r))}function vt(t){t.type="onShow";tt.trigger(t);n(document).bind("keydown.d",function(t){var i=t.keyCode;if(i==8)return u.val(""),a.hide(t);if(i==27)return a.hide(t);if(n(c).index(i)>=0){if(!b)return a.show(t),t.preventDefault();var e=n("#"+l.weeks+" a"),f=n("."+l.focus),r=e.index(f);return f.removeClass(l.focus),i==74||i==40?r+=7:i==75||i==38?r-=7:i==76||i==39?r+=1:(i==72||i==37)&&(r-=1),r==-1?(a.addMonth(-1),f=n("#"+l.weeks+" a:last")):r==35?(a.addMonth(),f=n("#"+l.weeks+" a:first")):f=e.eq(r),f.addClass(l.focus),t.preventDefault()}return i==34?a.addMonth():i==33?a.addMonth(-1):i==36?a.today():(i==13&&(n(t.target).is("select")||n("."+l.focus).click()),n([16,17,18,9]).index(i)>=0)});n(document).bind("click.d",function(t){var i=t.target;n(i).parents("#"+l.root).length||i==u[0]||at&&i==at[0]||a.hide(t)})}var a=this,ct=new Date,l=e.css,it=r[e.lang],v=n("#"+l.root),lt=v.find("#"+l.title),at,rt,ut,g,nt,ft,y=u.attr("data-value")||e.value||u.val(),p=u.attr("min")||e.min,w=u.attr("max")||e.max,b,et,tt,k,d,yt,ot,st;if(y=i(y)||ct,p=i(p||e.yearRange[0]*365),w=i(w||e.yearRange[1]*365),!it)throw"Dateinput: invalid language: "+e.lang;if(u.attr("type")=="date"&&(et=n("<input/>"),n.each("name,readonly,disabled,value,required".split(","),function(n,t){et.attr(t,u.attr(t))}),u.replaceWith(et),u=et),u.addClass(l.input),tt=u.add(a),!v.length){for(v=n("<div><div><a/><div/><a/><\/div><div><div/><div/><\/div><\/div>").hide().css({position:"absolute"}).attr("id",l.root),v.children().eq(0).attr("id",l.head).end().eq(1).attr("id",l.body).children().eq(0).attr("id",l.days).end().eq(1).attr("id",l.weeks).end().end().end().find("a").eq(0).attr("id",l.prev).end().eq(1).attr("id",l.next),lt=v.find("#"+l.head).find("div").attr("id",l.title),e.selectors&&(k=n("<select/>").attr("id",l.month),d=n("<select/>").attr("id",l.year),lt.append(k.add(d))),yt=v.find("#"+l.days),ot=0;ot<7;ot++)yt.append(n("<span/>").text(it.shortDays[(ot+e.firstDay)%7]));u.after(v)}e.trigger&&(at=n("<a/>").attr("href","#").addClass(l.trigger).click(function(n){return a.show(),n.preventDefault()}).insertAfter(u));st=v.find("#"+l.weeks);d=v.find("#"+l.year);k=v.find("#"+l.month);n.extend(a,{show:function(t){if(!(u.is("[readonly]")||b)&&(t=t||n.Event(),t.type="onBeforeShow",tt.trigger(t),!t.isDefaultPrevented())){n.each(h,function(){this.hide()});b=!0;k.unbind("change").change(function(){a.setValue(d.val(),n(this).val())});d.unbind("change").change(function(){a.setValue(n(this).val(),k.val())});rt=v.find("#"+l.prev).unbind("click").click(function(){return rt.hasClass(l.disabled)||a.addMonth(-1),!1});ut=v.find("#"+l.next).unbind("click").click(function(){return ut.hasClass(l.disabled)||a.addMonth(),!1});a.setValue(y);var i=u.position();return v.css({top:i.top+u.outerHeight({margins:!0})+e.offset[0],left:i.left+e.offset[1]}),e.speed?v.show(e.speed,function(){vt(t)}):(v.show(),vt(t)),a}},setValue:function(i,r,u){var o,tt,et,ft,c,h,v;if(parseInt(r,10)>=-1?(i=t(i),r=t(r),u=t(u),o=new Date(i,r,u)):(o=i||y,i=o.getFullYear(),r=o.getMonth(),u=o.getDate()),r==-1?(r=11,i--):r==12&&(r=0,i++),!b)return ht(o,e),a;if(nt=r,g=i,o=new Date(i,r,1-e.firstDay),u=o.getDay(),tt=f(i,r),et=f(i,r-1),e.selectors){for(k.empty(),n.each(it.months,function(t,r){p<new Date(i,t+1,-1)&&w>new Date(i,t,0)&&k.append(n("<option/>").html(r).attr("value",t))}),d.empty(),o=i+e.yearRange[0];o<i+e.yearRange[1];o++)p<new Date(o+1,-1,0)&&w>new Date(o,0,0)&&d.append(n("<option/>").text(o));k.val(r);d.val(i)}else lt.html(it.months[r]+" "+i);for(st.empty(),rt.add(ut).removeClass(l.disabled),c=0;c<42;c++)h=n("<a/>"),c%7==0&&(ft=n("<div/>").addClass(l.week),st.append(ft)),c<u?(h.addClass(l.off),v=et-u+c+1,o=new Date(i,r-1,v)):c>=u+tt?(h.addClass(l.off),v=c-tt-u+1,o=new Date(i,r+1,v)):(v=c-u+1,o=new Date(i,r,v),s(y,o)?h.attr("id",l.current).addClass(l.focus):s(ct,o)&&h.attr("id",l.today)),p&&o<p&&h.add(rt).addClass(l.disabled),w&&o>w&&h.add(ut).addClass(l.disabled),h.attr("href","#"+v).text(v).data("date",o),ft.append(h),h.click(function(t){var i=n(this);return i.hasClass(l.disabled)||(n("#"+l.current).removeAttr("id"),i.attr("id",l.current),ht(i.data("date"),e,t)),!1});return l.sunday&&st.find(l.week).each(function(){var t=e.firstDay?7-e.firstDay:0;n(this).children().slice(t,t+1).addClass(l.sunday)}),a},setMin:function(n,t){return p=i(n),t&&y<p&&a.setValue(p),a},setMax:function(n,t){return w=i(n),t&&y>w&&a.setValue(w),a},today:function(){return a.setValue(ct)},addDay:function(n){return this.setValue(g,nt,ft+(n||1))},addMonth:function(n){return this.setValue(g,nt+(n||1),ft)},addYear:function(n){return this.setValue(g+(n||1),nt,ft)},hide:function(t){if(b){if(t=t||n.Event(),t.type="onHide",tt.trigger(t),n(document).unbind("click.d").unbind("keydown.d"),t.isDefaultPrevented())return;v.hide();b=!1}return a},getConf:function(){return e},getInput:function(){return u},getCalendar:function(){return v},getValue:function(n){return n?o(y,n,e.lang):y},isOpen:function(){return b}});n.each(["onBeforeShow","onShow","change","onHide"],function(t,i){n.isFunction(e[i])&&n(a).bind(i,e[i]);a[i]=function(t){return n(a).bind(i,t),a}});u.bind("focus click",a.show).keydown(function(t){var i=t.keyCode;return!b&&n(c).index(i)>=0?(a.show(t),t.preventDefault()):t.shiftKey||t.ctrlKey||t.altKey||i==9?!0:t.preventDefault()});i(u.val())&&ht(y,e)}var l,a;n.tools=n.tools||{version:"1.2.3"};var h=[],u,c=[75,76,38,39,74,72,40,37],r={};u=n.tools.dateinput={conf:{format:"mm/dd/yy",selectors:!1,yearRange:[-5,5],lang:"en",offset:[0,0],speed:0,firstDay:0,min:0,max:0,trigger:!1,css:{prefix:"cal",input:"date",root:0,head:0,title:0,prev:0,next:0,month:0,year:0,days:0,body:0,weeks:0,today:0,current:0,week:0,off:0,sunday:0,focus:0,disabled:0,trigger:0}},localize:function(t,i){n.each(i,function(n,t){i[n]=t.split(",")});r[t]=i}};u.localize("en",{months:"一月,二月,三月,四月,五月,六月,七月,八月,九月,十月,十一月,十二月",shortMonths:"一月,二月,三月,四月,五月,六月,七月,八月,九月,十月,十一月,十二月",days:"日,一,二,三,四,五,六",shortDays:"日,一,二,三,四,五,六"});l=/d{1,4}|m{1,4}|yy(?:yy)?|"[^"]*"|'[^']*'/g;a=n("<a/>");n.expr[":"].date=function(t){var i=t.getAttribute("type");return i&&i=="date"||!!n(t).data("dateinput")};n.fn.dateinput=function(t){if(this.data("dateinput"))return this;t=n.extend(!0,{},u.conf,t);n.each(t.css,function(n,i){i||n=="prefix"||(t.css[n]=(t.css.prefix||"")+(i||n))});var i;return this.each(function(){var r=new v(n(this),t);h.push(r);r=r.getInput().data("dateinput",r);i=i?i.add(r):r}),i?i:this}}(jQuery),function(n){typeof define=="function"&&define.amd?typeof jQuery!="undefined"?define(["jquery"],n):define([],n):typeof jQuery!="undefined"?n(jQuery):n()}(function(n,t){function a(n,t){for(var u=decodeURI(n),f=l[t||!1?"strict":"loose"].exec(u),i={attr:{},param:{},seg:{}},r=14;r--;)i.attr[h[r]]=f[r]||"";return i.param.query=o(i.attr.query),i.param.fragment=o(i.attr.fragment),i.seg.path=i.attr.path.replace(/^\/+|\/+$/g,"").split("/"),i.seg.fragment=i.attr.fragment.replace(/^\/+|\/+$/g,"").split("/"),i.attr.base=i.attr.host?(i.attr.protocol?i.attr.protocol+"://"+i.attr.host:i.attr.host)+(i.attr.port?":"+i.attr.port:""):"",i}function v(n){var t=n.tagName;return typeof t!="undefined"?s[t.toLowerCase()]:t}function e(n,t){var i,r;if(n[t].length==0)return n[t]={};i={};for(r in n[t])i[r]=n[t][r];return n[t]=i,i}function u(n,t,f,o){var h=n.shift(),s;h?(s=t[f]=t[f]||[],"]"==h?i(s)?""!=o&&s.push(o):"object"==typeof s?s[k(s).length]=o:s=t[f]=[t[f],o]:~h.indexOf("]")?(h=h.substr(0,h.length-1),!r.test(h)&&i(s)&&(s=e(t,f)),u(n,s,h,o)):(!r.test(h)&&i(s)&&(s=e(t,f)),u(n,s,h,o))):i(t[f])?t[f].push(o):t[f]="object"==typeof t[f]?o:"undefined"==typeof t[f]?o:[t[f],o]}function y(n,t,f){var e,o;if(~t.indexOf("]")){var s=t.split("["),h=s.length,c=h-1;u(s,n,"base",f)}else{if(!r.test(t)&&i(n.base)){e={};for(o in n.base)e[o]=n.base[o];n.base=e}p(n.base,t,f)}return n}function o(n){return b(String(n).split(/&|;/),function(n,t){try{t=decodeURIComponent(t.replace(/\+/g," "))}catch(e){}var u=t.indexOf("="),f=w(t),r=t.substr(0,f||u),i=t.substr(f||u,t.length),i=i.substr(i.indexOf("=")+1,i.length);return""==r&&(r=t,i=""),y(n,r,i)},{base:{}}).base}function p(n,r,u){var f=n[r];t===f?n[r]=u:i(f)?f.push(u):n[r]=[f,u]}function w(n){for(var u=n.length,r,t,i=0;i<u;++i)if(t=n[i],"]"==t&&(r=!1),"["==t&&(r=!0),"="==t&&!r)return i}function b(n,i){for(var r=0,f=n.length>>0,u=arguments[2];r<f;)r in n&&(u=i.call(t,u,n[r],r,n)),++r;return u}function i(n){return Object.prototype.toString.call(n)==="[object Array]"}function k(n){var t=[];for(prop in n)n.hasOwnProperty(prop)&&t.push(prop);return t}function f(n,i){return arguments.length===1&&n===!0&&(i=!0,n=t),i=i||!1,n=n||window.location.toString(),{data:a(n,i),attr:function(n){return n=c[n]||n,typeof n!="undefined"?this.data.attr[n]:this.data.attr},param:function(n){return typeof n!="undefined"?this.data.param.query[n]:this.data.param.query},fparam:function(n){return typeof n!="undefined"?this.data.param.fragment[n]:this.data.param.fragment},segment:function(n){return typeof n=="undefined"?this.data.seg.path:(n=n<0?this.data.seg.path.length+n:n-1,this.data.seg.path[n])},fsegment:function(n){return typeof n=="undefined"?this.data.seg.fragment:(n=n<0?this.data.seg.fragment.length+n:n-1,this.data.seg.fragment[n])}}}var s={a:"href",img:"src",form:"action",base:"href",script:"src",iframe:"src",link:"href"},h=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","fragment"],c={anchor:"fragment"},l={strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/},d=Object.prototype.toString,r=/^[0-9]+$/;typeof n!="undefined"?(n.fn.url=function(t){var i="";return this.length&&(i=n(this).attr(v(this[0]))||""),f(i,t)},n.url=f):window.purl=f});iss={width:["60","80","100","10"],reset:function(){$("#BarBorder_TxtUserPassword").html("<span style='color:red'>  密码强度：弱<\/span>")},level0:function(){$("#BarBorder_TxtUserPassword").html("<span style='color:#FF3300'>  密码强度：一般<\/span>")},level1:function(){$("#BarBorder_TxtUserPassword").html("<span style='color:green'>  密码强度：强<\/span>")},level2:function(){$("#BarBorder_TxtUserPassword").html("<span style='color:green'>  密码强度：很强<\/span>")}};String.prototype.endWith=function(n){var t=new RegExp(n+"$");return t.test(this)},function(n){function t(t,i){return parseInt(n.curCSS(t.jquery?t[0]:t,i,!0))||0}n.curCSS||(n.curCSS=n.css);n.ui=n.ui||{};n.extend(n.ui,{plugin:{add:function(t,i,r){var f=n.ui[t].prototype;for(var u in r)f.plugins[u]=f.plugins[u]||[],f.plugins[u].push([i,r[u]])},call:function(n,t,i){var u=n.plugins[t],r;if(u)for(r=0;r<u.length;r++)n.options[u[r][0]]&&u[r][1].apply(n.element,i)}},cssCache:{},css:function(t){if(n.ui.cssCache[t])return n.ui.cssCache[t];var i=n('<div class="ui-resizable-gen">').addClass(t).css({position:"absolute",top:"-5000px",left:"-5000px",display:"block"}).appendTo("body");n.ui.cssCache[t]=!!(!/auto|default/.test(i.css("cursor"))||/^[1-9]/.test(i.css("height"))||/^[1-9]/.test(i.css("width"))||!/none/.test(i.css("backgroundImage"))||!/transparent|rgba\(0, 0, 0, 0\)/.test(i.css("backgroundColor")));try{n("body").get(0).removeChild(i.get(0))}catch(r){}return n.ui.cssCache[t]},disableSelection:function(n){n.unselectable="on";n.onselectstart=function(){return!1};n.style&&(n.style.MozUserSelect="none")},enableSelection:function(n){n.unselectable="off";n.onselectstart=function(){return!0};n.style&&(n.style.MozUserSelect="")},hasScroll:function(n,t){var i=/top/.test(t||"top")?"scrollTop":"scrollLeft",r=!1;return n[i]>0?!0:(n[i]=1,r=n[i]>0?!0:!1,n[i]=0,r)}});n.each(["Left","Top"],function(t,i){n.fn["scroll"+i]||(n.fn["scroll"+i]=function(t){return t!=undefined?this.each(function(){this==window||this==document?window.scrollTo(i=="Left"?t:n(window).scrollLeft(),i=="Top"?t:n(window).scrollTop()):this["scroll"+i]=t}):this[0]==window||this[0]==document?self[i=="Left"?"pageXOffset":"pageYOffset"]||n.boxModel&&document.documentElement["scroll"+i]||document.body["scroll"+i]:this[0]["scroll"+i]})});var i=n.fn.remove;n.fn.extend({position:function(){var i=this.offset(),n=this.offsetParent(),r=n.offset();return{top:i.top-t(this[0],"marginTop")-r.top-t(n,"borderTopWidth"),left:i.left-t(this[0],"marginLeft")-r.left-t(n,"borderLeftWidth")}},offsetParent:function(){for(var t=this[0].offsetParent;t&&!/^body|html$/i.test(t.tagName)&&n.css(t,"position")=="static";)t=t.offsetParent;return n(t)},mouseInteraction:function(t){return this.each(function(){new n.ui.mouseInteraction(this,t)})},removeMouseInteraction:function(){return this.each(function(){n.data(this,"ui-mouse")&&n.data(this,"ui-mouse").destroy()})},remove:function(){return jQuery("*",this).add(this).trigger("remove"),i.apply(this,arguments)}});n.ui.mouseInteraction=function(t,i){var r=this;this.element=t;n.data(this.element,"ui-mouse",this);this.options=n.extend({},i);n(t).bind("mousedown.draggable",function(){return r.click.apply(r,arguments)});n.browser.msie&&n(t).attr("unselectable","on");n(t).mouseup(function(){r.timer&&clearInterval(r.timer)})};n.extend(n.ui.mouseInteraction.prototype,{destroy:function(){n(this.element).unbind("mousedown.draggable")},trigger:function(){return this.click.apply(this,arguments)},click:function(t){if(t.which!=1||n.inArray(t.target.nodeName.toLowerCase(),this.options.dragPrevention||[])!=-1||this.options.condition&&!this.options.condition.apply(this.options.executor||this,[t,this.element]))return!0;var i=this,r=function(){i._MP={left:t.pageX,top:t.pageY};n(document).bind("mouseup.draggable",function(){return i.stop.apply(i,arguments)});n(document).bind("mousemove.draggable",function(){return i.drag.apply(i,arguments)});(!i.initalized&&Math.abs(i._MP.left-t.pageX)>=i.options.distance||Math.abs(i._MP.top-t.pageY)>=i.options.distance)&&(i.options.start&&i.options.start.call(i.options.executor||i,t,i.element),i.options.drag&&i.options.drag.call(i.options.executor||i,t,this.element),i.initialized=!0)};return this.options.delay?(this.timer&&clearInterval(this.timer),this.timer=setTimeout(r,this.options.delay)):r(),!1},stop:function(t){var i=this.options;return this.initialized?(this.options.stop&&this.options.stop.call(this.options.executor||this,t,this.element),n(document).unbind("mouseup.draggable").unbind("mousemove.draggable"),this.initialized=!1,!1):n(document).unbind("mouseup.draggable").unbind("mousemove.draggable")},drag:function(t){var i=this.options;if(n.browser.msie&&!t.button)return this.stop.apply(this,[t]);if(!this.initialized&&(Math.abs(this._MP.left-t.pageX)>=i.distance||Math.abs(this._MP.top-t.pageY)>=i.distance))this.options.start&&this.options.start.call(this.options.executor||this,t,this.element),this.initialized=!0;else if(!this.initialized)return!1;return i.drag&&i.drag.call(this.options.executor||this,t,this.element),!1}})}(jQuery),function(n){n.fn.tabs=function(){var t=typeof arguments[0]=="string"&&arguments[0],i=t&&Array.prototype.slice.call(arguments,1)||arguments;return t=="length"?n.data(this[0],"tabs").$tabs.length:this.each(function(){if(t){var r=n.data(this,"tabs");r&&r[t].apply(r,i)}else new n.ui.tabs(this,i[0]||{})})};n.ui.tabs=function(t,i){var r=this;this.options=n.extend({},n.ui.tabs.defaults,i);this.element=t;i.selected===null&&(this.options.selected=null);this.options.event+=".tabs";n(t).bind("setData.tabs",function(n,t,i){/^selected/.test(t)?r.select(i):(r.options[t]=i,r.tabify())}).bind("getData.tabs",function(n,t){return r.options[t]});n.data(t,"tabs",this);this.tabify(!0)};n.ui.tabs.defaults={selected:0,unselect:!1,event:"click",disabled:[],cookie:null,spinner:"Loading&#8230;",cache:!1,idPrefix:"ui-tabs-",ajaxOptions:{},fx:null,tabTemplate:'<li><a href="#{href}"><span>#{label}<\/span><\/a><\/li>',panelTemplate:"<div><\/div>",navClass:"ui-tabs-nav",selectedClass:"ui-tabs-selected",unselectClass:"ui-tabs-unselect",disabledClass:"ui-tabs-disabled",panelClass:"ui-tabs-panel",hideClass:"ui-tabs-hide",loadingClass:"ui-tabs-loading"};n.extend(n.ui.tabs.prototype,{tabId:function(t){return t.title&&t.title.replace(/\s/g,"_").replace(/[^A-Za-z0-9\-_:\.]/g,"")||this.options.idPrefix+n.data(t)},ui:function(n,t){return{instance:this,options:this.options,tab:n,panel:t}},tabify:function(t){function v(t,r,u){r.animate(f,f.duration||c,function(){r.addClass(i.hideClass).css(s);n.browser.msie&&f.opacity&&(r[0].style.filter="");u&&l(t,u,r)})}function l(t,f){u===e&&f.css("display","block");f.animate(u,u.duration||c,function(){f.removeClass(i.hideClass).css(s);n.browser.msie&&u.opacity&&(f[0].style.filter="");n(r.element).triggerHandler("tabsshow",[r.ui(t,f[0])],i.show)})}function y(n,t,r,u){t.addClass(i.selectedClass).siblings().removeClass(i.selectedClass);v(n,r,u)}var r,i,a,o,h,f,u,e,c,s;for(this.$lis=n("li:has(a[href])",this.element),this.$tabs=this.$lis.map(function(){return n("a",this)[0]}),this.$panels=n([]),r=this,i=this.options,this.$tabs.each(function(t,u){var e,f;u.hash&&u.hash.replace("#","")?r.$panels=r.$panels.add(u.hash):n(u).attr("href")!="#"?(n.data(u,"href.tabs",u.href),n.data(u,"load.tabs",u.href),e=r.tabId(u),u.href="#"+e,f=n("#"+e),f.length||(f=n(i.panelTemplate).attr("id",e).addClass(i.panelClass).insertAfter(r.$panels[t-1]||r.element),f.data("destroy.tabs",!0)),r.$panels=r.$panels.add(f)):i.disabled.push(t+1)}),t&&(n(this.element).hasClass(i.navClass)||n(this.element).addClass(i.navClass),this.$panels.each(function(){var t=n(this);t.hasClass(i.panelClass)||t.addClass(i.panelClass)}),this.$tabs.each(function(t,u){var f,o,e;if(location.hash){if(u.hash==location.hash)return i.selected=t,(n.browser.msie||n.browser.opera)&&(f=n(location.hash),o=f.attr("id"),f.attr("id",""),setTimeout(function(){f.attr("id",o)},500)),scrollTo(0,0),!1}else if(i.cookie){if(e=parseInt(n.cookie("ui-tabs"+n.data(r.element)),10),e&&r.$tabs[e])return i.selected=e,!1}else if(r.$lis.eq(t).hasClass(i.selectedClass))return i.selected=t,!1}),this.$panels.addClass(i.hideClass),this.$lis.removeClass(i.selectedClass),i.selected!==null&&(this.$panels.eq(i.selected).show().removeClass(i.hideClass),this.$lis.eq(i.selected).addClass(i.selectedClass)),a=i.selected!==null&&n.data(this.$tabs[i.selected],"load.tabs"),a&&this.load(i.selected),i.disabled=n.unique(i.disabled.concat(n.map(this.$lis.filter("."+i.disabledClass),function(n){return r.$lis.index(n)}))).sort(),n(window).bind("unload",function(){r.$tabs.unbind(".tabs");r.$lis=r.$tabs=r.$panels=null})),o=0;h=this.$lis[o];o++)n(h)[n.inArray(o,i.disabled)!=-1&&!n(h).hasClass(i.selectedClass)?"addClass":"removeClass"](i.disabledClass);i.cache===!1&&this.$tabs.removeData("cache.tabs");e={"min-width":0,duration:1};c="normal";i.fx&&i.fx.constructor==Array?(f=i.fx[0]||e,u=i.fx[1]||e):f=u=i.fx||e;s={display:"",overflow:"",height:""};n.browser.msie||(s.opacity="");this.$tabs.unbind(".tabs").bind(i.event,function(){var t=n(this).parents("li:eq(0)"),e=r.$panels.filter(":visible"),u=n(this.hash),f;if(t.hasClass(i.selectedClass)&&!i.unselect||t.hasClass(i.disabledClass)||n(this).hasClass(i.loadingClass)||n(r.element).triggerHandler("tabsselect",[r.ui(this,u[0])],i.select)===!1)return this.blur(),!1;if(r.options.selected=r.$tabs.index(this),i.unselect){if(t.hasClass(i.selectedClass))return r.options.selected=null,t.removeClass(i.selectedClass),r.$panels.stop(),v(this,e),this.blur(),!1;if(!e.length)return r.$panels.stop(),f=this,r.load(r.$tabs.index(this),function(){t.addClass(i.selectedClass).addClass(i.unselectClass);l(f,u)}),this.blur(),!1}if(i.cookie&&n.cookie("ui-tabs"+n.data(r.element),r.options.selected,i.cookie),r.$panels.stop(),u.length)f=this,r.load(r.$tabs.index(this),e.length?function(){y(f,t,e,u)}:function(){t.addClass(i.selectedClass);l(f,u)});else throw"jQuery UI Tabs: Mismatching fragment identifier.";return n.browser.msie&&this.blur(),!1});/^click/.test(i.event)||this.$tabs.bind("click.tabs",function(){return!1})},add:function(t,i,r){var u,e,o,f,s;r==undefined&&(r=this.$tabs.length);u=this.options;e=n(u.tabTemplate.replace(/#\{href\}/,t).replace(/#\{label\}/,i));e.data("destroy.tabs",!0);o=t.indexOf("#")==0?t.replace("#",""):this.tabId(n("a:first-child",e)[0]);f=n("#"+o);f.length||(f=n(u.panelTemplate).attr("id",o).addClass(u.panelClass).addClass(u.hideClass),f.data("destroy.tabs",!0));r>=this.$lis.length?(e.appendTo(this.element),f.appendTo(this.element.parentNode)):(e.insertBefore(this.$lis[r]),f.insertBefore(this.$panels[r]));u.disabled=n.map(u.disabled,function(n){return n>=r?++n:n});this.tabify();this.$tabs.length==1&&(e.addClass(u.selectedClass),f.removeClass(u.hideClass),s=n.data(this.$tabs[0],"load.tabs"),s&&this.load(r,s));n(this.element).triggerHandler("tabsadd",[this.ui(this.$tabs[r],this.$panels[r])],u.add)},remove:function(t){var i=this.options,r=this.$lis.eq(t).remove(),u=this.$panels.eq(t).remove();r.hasClass(i.selectedClass)&&this.$tabs.length>1&&this.select(t+(t+1<this.$tabs.length?1:-1));i.disabled=n.map(n.grep(i.disabled,function(n){return n!=t}),function(n){return n>=t?--n:n});this.tabify();n(this.element).triggerHandler("tabsremove",[this.ui(r.find("a")[0],u[0])],i.remove)},enable:function(t){var i=this.options,r;n.inArray(t,i.disabled)!=-1&&(r=this.$lis.eq(t).removeClass(i.disabledClass),n.browser.safari&&(r.css("display","inline-block"),setTimeout(function(){r.css("display","block")},0)),i.disabled=n.grep(i.disabled,function(n){return n!=t}),n(this.element).triggerHandler("tabsenable",[this.ui(this.$tabs[t],this.$panels[t])],i.enable))},disable:function(t){var r=this,i=this.options;t!=i.selected&&(this.$lis.eq(t).addClass(i.disabledClass),i.disabled.push(t),i.disabled.sort(),n(this.element).triggerHandler("tabsdisable",[this.ui(this.$tabs[t],this.$panels[t])],i.disable))},select:function(n){typeof n=="string"&&(n=this.$tabs.index(this.$tabs.filter("[href$="+n+"]")[0]));this.$tabs.eq(n).trigger(this.options.event)},load:function(t,i){var u=this,r=this.options,e=this.$tabs.eq(t),f=e[0],l=i==undefined||i===!1,h=e.data("load.tabs"),o,s,c;if(i=i||function(){},!h||n.data(f,"cache.tabs")&&!l){i();return}r.spinner&&(o=n("span",f),o.data("label.tabs",o.html()).html("<em>"+r.spinner+"<\/em>"));s=function(){u.$tabs.filter("."+r.loadingClass).each(function(){if(n(this).removeClass(r.loadingClass),r.spinner){var t=n("span",this);t.html(t.data("label.tabs")).removeData("label.tabs")}});u.xhr=null};c=n.extend({},r.ajaxOptions,{url:h,success:function(e,o){n(f.hash).html(e);s();i();r.cache&&n.data(f,"cache.tabs",!0);n(u.element).triggerHandler("tabsload",[u.ui(u.$tabs[t],u.$panels[t])],r.load);r.ajaxOptions.success&&r.ajaxOptions.success(e,o)}});this.xhr&&(this.xhr.abort(),s());e.addClass(r.loadingClass);setTimeout(function(){u.xhr=n.ajax(c)},0)},url:function(n,t){this.$tabs.eq(n).removeData("cache.tabs").data("load.tabs",t)},destroy:function(){var t=this.options;n(this.element).unbind(".tabs").removeClass(t.navClass).removeData("tabs");this.$tabs.each(function(){var t=n.data(this,"href.tabs"),i;t&&(this.href=t);i=n(this).unbind(".tabs");n.each(["href","load","cache"],function(n,t){i.removeData(t+".tabs")})});this.$lis.add(this.$panels).each(function(){n.data(this,"destroy.tabs")?n(this).remove():n(this).removeClass([t.selectedClass,t.unselectClass,t.disabledClass,t.panelClass,t.hideClass].join(" "))})}});n.extend(n.ui.tabs.prototype,{rotation:null,rotate:function(n,t){function f(){i.rotation=setInterval(function(){r=++r<i.$tabs.length?r:0;i.select(r)},n)}function u(n){(!n||n.clientX)&&clearInterval(i.rotation)}t=t||!1;var i=this,r=this.options.selected;n?(f(),t?this.$tabs.bind(this.options.event,function(){u();r=i.options.selected;f()}):this.$tabs.bind(this.options.event,u)):(u(),this.$tabs.unbind(this.options.event,u))}})}(jQuery);$(document).ready(function(){$("#featured > ul").tabs({fx:[{opacity:"fadeOut",duration:"fast"},{opacity:"toggle",duration:"fast"}]}).tabs("rotate",5e3,!0);navigator.appName=="Microsoft Internet Explorer"&&navigator.appVersion.split(";")[1].replace(/[ ]/g,"")=="MSIE6.0"&&DD_belatedPNG.fix("div, ul, img, li, input , a:hover");$("#featured > ul a").each(function(){$(this).focus(function(){$(this).blur()})})}),function(n){n.fn.ContentHit=function(){return this.each(function(){var t=n(this);n.ajax({type:"GET",dataType:"jsonp",url:"//click.gamersky.com/Common/GetHits.aspx",data:{id:t.attr("generalId"),script:"3"},success:function(n){t.html(n.hits)}})})};n.fn.ContentHrefHit=function(){return this.each(function(){var t=n(this),r="false",i;t.attr("data-hot")&&(r=t.attr("data-hot"));i="";t.attr("data-fieldName")&&(i=t.attr("data-fieldName"));t.click(function(){n.ajax({type:"GET",dataType:"jsonp",url:"//click.gamersky.com/Common/GetHits.aspx",data:{id:t.attr("data-itemid"),script:"3",hot:r,fieldName:i},success:function(){}})})})};n.fn.PagerHotKey=function(){return this.each(function(){var t=n(this);n(document).bind("keydown","left",function(i){if(!n(i.target).is("textarea")){var r=t.find("b");r.prev("a").length>0&&(window.location.href=r.prev("a").attr("href"))}});n(document).bind("keydown","right",function(i){if(!n(i.target).is("textarea")){var r=t.find("b");r.next("a").length>0&&(window.location.href=r.next("a").attr("href"))}})})}}(jQuery);$("#countn").ContentHit();$(document).ready(function(){function e(n){$.ajax({type:"GET",dataType:"jsonp",url:"//cm.gamersky.com/commentapi/count",data:{topic_source_id:n},success:function(n){$(".cy_comment").each(function(){if(n.result.hasOwnProperty($(this).attr("data-sid"))){var t=n.result[$(this).attr("data-sid")];$(this).text(t.joinCount).attr("data-lddt","yes")}})}})}function o(n){$.ajax({type:"GET",dataType:"jsonp",url:"//cm.gamersky.com/commentapi/count",data:{topic_source_id:n},success:function(n){$(".cy_commentnum").each(function(){if(n.result.hasOwnProperty($(this).attr("data-sid"))){var t=n.result[$(this).attr("data-sid")];$(this).text(t.comments).attr("data-lddt","yes")}})}})}function s(n){$.ajax({type:"GET",dataType:"jsonp",url:"//db2.gamersky.com/showAllHits.aspx",data:{id:n},success:function(n){for(var t=0;t<n.length;t++){var r=n[t],i=parseInt(r.hits),u=i>9999?parseFloat(parseFloat(i/1e4).toFixed(1))+"w":i;$(".gshit[data-gid='"+r.generalId+"']").text(u).attr("data-lddt","yes")}}})}function h(n){$.ajax({type:"GET",dataType:"jsonp",url:"//db2.gamersky.com/showAllHits.aspx",data:{clubId:n},success:function(n){for(var t=0;t<n.length;t++){var r=n[t],i=parseInt(r.hits),u=i>9999?parseFloat(parseFloat(i/1e4).toFixed(1))+"w":i;$(".gshit[data-clubId='"+r.generalId+"']").text(u).attr("data-lddt","yes")}}})}function f(n,t,i){function u(n,t){for(var r,f,u=[],i=0;i<Math.ceil(n.length/t);i++)r=i*t,f=r+t,u.push(n.slice(r,f));return u}var f=n.split(","),r=[];r=u(f,t);$.each(r,function(n,t){var r=t+"";typeof i=="function"&&i(r)})}var n,t,i,r,u;$(document).on("click",".countHit,.countHitSql",function(){var n=$(this),r="false",t,i;n.hasClass("countHitSql")&&(r="true");t="false";n.attr("data-hot")&&(t=n.attr("data-hot"));i="";n.attr("data-fieldName")&&(i=n.attr("data-fieldName"));$.ajax({type:"GET",dataType:"jsonp",url:"//click.gamersky.com/Common/GetHits.aspx",data:{id:n.attr("data-itemid"),script:"3",hot:t,fieldName:i,judge:r},success:function(){}})});$(document).on("click",".downloadUrlCountHit",function(){var n=$(this),r="false",t,i;n.hasClass("countHitSql")&&(r="true");t="false";n.attr("data-hot")&&(t=n.attr("data-hot"));i="";n.attr("data-fieldName")&&(i=n.attr("data-fieldName"));$.ajax({type:"GET",dataType:"jsonp",url:"//click.gamersky.com/Common/GetHits.aspx",data:{id:n.attr("data-itemid"),hitAction:"3",script:"3",hot:t,fieldName:i,judge:r},success:function(){}})});$(".page_css").PagerHotKey();n="";$(".cy_comment").each(function(){$(this).attr("data-clubId")!=""&&$(this).attr("data-clubId")!=undefined&&(n!=""&&(n=n+","),n=n+$(this).attr("data-clubId"))});n!=""&&$.ajax({type:"GET",url:"//club.gamersky.com/club/api/GetCommentCount",dataType:"jsonp",data:{jsondata:JSON.stringify({clubContentId:String(n)})},success:function(n){if(n.status=="ok"){var t=$.parseJSON(n.body);$.each(t,function(n,t){$(".cy_comment[data-clubId='"+t.Id+"']").html(t.commentCount)})}}});t="";$(".cy_comment").each(function(){$(this).attr("data-clubId")!=""&&$(this).attr("data-clubId")!=undefined||(t!=""&&(t=t+","),t=t+$(this).attr("data-sid"))});i="";$(".cy_commentnum").each(function(){$(this).attr("data-clubId")!=""&&$(this).attr("data-clubId")!=undefined||(i!=""&&(i=i+","),i=i+$(this).attr("data-sid"))});r="";$(".gshit").each(function(){$(this).attr("data-clubId")!=""&&$(this).attr("data-clubId")!=undefined||(r!=""&&(r=r+","),r=r+$(this).attr("data-gid"))});u="";$(".gshit").each(function(){$(this).attr("data-clubId")!=""&&(u!=""&&(u=u+","),u=u+$(this).attr("data-clubId"))});t!=""&&f(t,180,e);i!=""&&f(i,180,o);r!=""&&f(r,180,s);u!=""&&f(u,180,h)}),function(n){function r(){n("#commentform").comment()}var t="//cm.gamersky.com/commentajax.aspx",i="CommentConent",u=function(n){for(var t="",i=0;i<n;i++)t+=Math.floor(Math.random()*10);return t},f=function(n){for(var t,i=0,r=0;r<n.length;r++)t=n.charCodeAt(r),t>=1&&t<=126||65376<=t&&t<=65439?i++:i+=2;return i},e=function(u,f,e,o,s,h){var c,l;if(f.length>500){alert("发表评论字数不能超500字！");return}c=f.replace(/[\r\n]/g,"<br />");c=encodeURI(n.trim(c));l={type:"addcomment",username:u,commenttitle:"",content:c,email:"",gid:n(".commentStatus").attr("itemid"),nid:n(".commentStatus").attr("nodeid"),private:!1,position:0,score:0,TxtValidCode:e,isguest:s.toString(),referenceid:o};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(l)},success:function(u){switch(u.status){case"ok":n.removeCookie(i,{path:"/"});typeof r=="function"&&n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify({type:"removecache",itemId:n(".commentStatus").attr("itemid")})},success:function(){r()}});break;case"check":n.removeCookie(i,{path:"/"});typeof r=="function"?n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify({type:"removecache",itemId:n(".commentStatus").attr("itemid")})},success:function(){r()}}):window.location.href="#commentform";break;case"err":alert("发表评论失败！");break;case"nopurview":alert("此栏目已禁止发表评论！");break;case"noTourists":alert("此栏目已禁止游客发表评论！");break;case"checkCodeError":n(h).click();alert("您输入的验证码和系统产生的不一致，请重新输入！");break;case"lenError":alert("发表评论字数不能超500字！");break;default:alert("发表评论失败！")}}})};n.fn.extend({comment:function(r){return n(window).unload(function(){n.removeCookie(i,{path:"/"})}),this.each(function(){var u=n.extend({itemId:parseInt(n(this).attr("itemId")),nodeId:parseInt(n(this).attr("nodeId")),isShowMore:n(this).attr("isShowMore"),isShowHot:n(this).attr("isShowHot"),showListType:n(this).attr("showListType"),isPage:"true",pageSize:parseInt(n(this).attr("pageSize"))==0?10:parseInt(n(this).attr("pageSize")),currentPage:0},r),i=n(this),f;i.html("");n(".commentloading").show();f={type:"updatelabel",labelname:"游民星空评论",attr:{generalId:u.itemId,nodeId:u.nodeId,displayType:"all",isShowMore:u.isShowMore,isShowHot:u.isShowHot,commnetPageSize:u.pageSize,showListType:u.showListType}};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(f)},success:function(t){i.append(n(t.body));i.find(".commentlist").commentList({callback:function(){n(".commentloading").hide();i.find(".commentCount").text(i.find(".commentlist").attr("total"));var t=i.find(".commentlist").attr("showCommentList");t=="false"&&(i.find(".hd").hide(),i.find(".bd .title").remove(),i.find(".bd").prepend(i.find(".hd .title")))}});i.find(".commentHotList").commentHot();i.find(".mainCommentContainer").commentAdd();i.find(".mainCommentContainer").commentUserLogin()}})})},commentHot:function(i){return this.each(function(){var r=n(this),u=n.extend({itemId:parseInt(n(this).attr("itemId")),nodeId:parseInt(n(this).attr("nodeId")),listLabelName:"游民星空热门评论"},i),f={type:"updatelabel",labelname:u.listLabelName,attr:{itemId:u.itemId}};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(f)},success:function(n){r.html(n.body);r.commentAction()}})})},commentAdd:function(){return this.each(function(){var t=n(this),r=t.find(".title"),o=n("#addCommentTemplate").html(),i,s;t.html("");t.append(r);t.append(n(o));i=t.find(".mainCommentContent");i.keyup(function(){var i=500,r;n(this).val().length>i&&n(this).val(n(this).val().substring(0,i));r=i-n(this).val().length;t.find(".butleft").html("剩余字数："+r.toString())});i.keydown(function(n){n.stopPropagation()});s=n(".commentStatus").attr("commentusername");t.find(".mainCommentValidCodeImg").click(function(){var t=n(this).attr("src");t.indexOf("?")>=0?n(this).attr("src",t.split("?")[0]+"?code="+u(10)):n(this).attr("src",t+"?code="+u(10))});t.find(".mainSubmitButton").click(function(){var r=n(".commentStatus").attr("commentusername"),h=n(".commentStatus").attr("commentpermissiontype"),c=t.find(".replyIsGuest"),o,s,u,l;if(h=="1"&&r=="游客")return alert("请先登录再发表评论！"),!1;if(h==0&&!c.attr("checked")&&r=="游客")return alert("请先登录或选择游客发表！"),!1;if(n.trim(i.val())=="")return alert("请输入评论内容！"),i.focus(),!1;if(c.attr("checked")&&r=="游客"&&f(i.val())<10)return alert("游客发表，评论内容不得少于5个字！"),i.focus(),!1;if(o="",s=n(".commentStatus").attr("commentvalidatecodetype"),s=="0"||s=="1"&&r=="游客"){if(u=t.find(".mainCommentValidCode"),u.length!=0&&u.val()=="")return alert("请输入验证码！"),u.focus(),!1;o=u.val()}l=i.val();e(r,l,o,0,!1,".mainCommentValidCode")})})},commentList:function(i){return this.each(function(){var r=n.extend({itemId:parseInt(n(this).attr("itemId")),nodeId:parseInt(n(this).attr("nodeId")),pageSize:parseInt(n(this).attr("pageSize"))==0?10:parseInt(n(this).attr("pageSize")),listLabelName:"游民星空评论列表",currentPage:parseInt(n(this).attr("currentPage"))==0?1:parseInt(n(this).attr("currentPage")),showCommentList:n(this).attr("showCommentList"),callback:function(){}},i),u=n(this),f={type:"updatelabel",labelname:r.listLabelName,currentpage:r.currentPage,cachetime:60,attr:{itemId:r.itemId,page:"true",pagesize:r.pageSize,currentpage:r.currentPage}};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(f)},success:function(t){r.showCommentList=="false"&&u.hide();u.html(t.body);u.attr("total",t.total);n(u.attr("pager")).commentPager();u.commentAction();r.callback()}})})},commentUserLogin:function(){return this.each(function(){var i=n(this);n(document).bind("login",function(){var r=i.parents("#commentform"),u={itemId:parseInt(r.attr("itemId")),nodeId:parseInt(r.attr("nodeId")),isShowMore:r.attr("isShowMore"),isShowHot:r.attr("isShowHot"),showListType:r.attr("showListType"),pageSize:parseInt(r.attr("pageSize"))==0?10:parseInt(r.attr("pageSize"))},f={type:"updatelabel",labelname:"游民星空评论",attr:{generalId:u.itemId,nodeId:u.nodeId,displayType:"template",isShowMore:u.isShowMore,isShowHot:u.isShowHot,commnetPageSize:u.pageSize,showListType:u.showListType}};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(f)},success:function(t){n("#replayCommentTemplate,#addCommentTemplate,.commentStatus").remove();r.append(n(t.body));n(document).trigger("updateReplay");n(".mainCommentContainer").commentAdd();n(".mainCommentContainer").commentUserLogin()}})});n(document).bind("logout",function(){var r=i.parents("#commentform"),u={itemId:parseInt(r.attr("itemId")),nodeId:parseInt(r.attr("nodeId")),isShowMore:r.attr("isShowMore"),isShowHot:r.attr("isShowHot"),showListType:r.attr("showListType"),pageSize:parseInt(r.attr("pageSize"))==0?10:parseInt(r.attr("pageSize"))},f={type:"updatelabel",labelname:"游民星空评论",attr:{generalId:u.itemId,nodeId:u.nodeId,displayType:"template",isShowMore:u.isShowMore,isShowHot:u.isShowHot,commnetPageSize:u.pageSize,showListType:u.showListType}};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(f)},success:function(t){n("#replayCommentTemplate,#addCommentTemplate,.commentStatus").remove();r.append(n(t.body));n(document).trigger("updateReplay");n(".mainCommentContainer").commentAdd();n(".mainCommentContainer").commentUserLogin()}})})})},commentAction:function(){return this.each(function(){var r=n(this);r.find(".addpkzone").click(function(i){i.preventDefault();var r=n(this),u={type:"addcommentpk",commentid:parseInt(r.attr("rel")),position:1,content:"support"};n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(u)},success:function(n){switch(n.status){case"ok":r.find(".comment_pkcount").html(parseInt(r.find(".comment_pkcount").html())+1);break;case"AnonymousAgain":case"UserAgain":alert("对不起，您已经顶过了！");break;case"NotAuthenticated":alert("登录会员才能使用！");window.location.hash&&(window.location.hash="");window.location.hash="#commentLogin";break;default:alert("提交失败！")}}})});r.find("a[rel='moreline']").click(function(t){t.preventDefault();var i=n(this).attr("refCommentId");r.find("li[rel='refCommentList'][refCommentId='"+i+"']").show();n(this).parents(".refCommentMore").hide()});r.find(".replyButtonLink,.repalyLink a").click(function(t){var l,a,v,c;t.preventDefault();r.find(".closeReplayCommentButton").click();var s=n(this),h=s.attr("rel"),o=n("");n(this).attr("refcommentid")?(l=n(this).attr("refcommentid"),o=r.find('.replyBox[rel="'+h+'"][refcommentid="'+l+'"]')):o=r.find('.newreply[rel="'+h+'"]');a=n("#replayCommentTemplate").html();v=n(a);o.html("");v.appendTo(o).show();s.attr("data-state","open");o.focus();o.find(".closeReplayCommentButton").click(function(){o.html("");s.attr("data-state","close")});o.commentUserLogin();n(document).one("updateReplay",function(){s.attr("data-state")=="open"&&s.click()});c=n.cookie(i);c&&o.find(".replyCommentContent").val(c);o.find(".replyCommentContent").keyup(function(){var t=500,r;n.cookie(i,n(this).val(),{expires:7,path:"/"});n(this).val().length>t&&n(this).val(n(this).val().substring(0,t));r=t-n(this).val().length;o.find(".replyChLeft").html(r.toString())});o.find(".replyCommentContent").keydown(function(n){n.stopPropagation()});o.find(".replyCommentValidCodeImg").click(function(){var t=n(this).attr("src");t.indexOf("?")>=0?n(this).attr("src",t.split("?")[0]+"?code="+u(10)):n(this).attr("src",t+"?code="+u(10))});o.find(".replySubmitButton").click(function(){var i=n(".commentStatus").attr("commentusername"),c=n(".commentStatus").attr("commentpermissiontype"),l=o.find(".replyIsGuest"),a,t,u,s,r,v,y;if(c=="1"&&i=="游客")return alert("请先登录再发表评论！"),!1;if(c==0&&!l.attr("checked")&&i=="游客")return alert("请先登录或选择游客发表！"),!1;if(a=h,t=o.find(".replyCommentContent"),n.trim(t.val())=="")return alert("请输入评论内容！"),t.focus(),!1;if(l.attr("checked")&&i=="游客"&&f(t.val())<10)return alert("游客发表，评论内容不得少于5个字！"),t.focus(),!1;if(u="",s=n(".commentStatus").attr("commentvalidatecodetype"),s=="0"||s=="1"&&i=="游客"){if(r=o.find(".replyCommentValidCode"),r.length!=0&&r.val()=="")return alert("请输入验证码！"),r.focus(),!1;u=r.val()}v=t.val();y=!1;e(i,v,u,a,y,".replyCommentValidCodeImg")})})})},commentPager:function(i){return this.each(function(){var r=n.extend({commentList:n(this).attr("commentList"),pageSize:10,listLabelName:"游民星空评论列表",pageLabelName:"游民评论分页",callback:function(){}},i),f=parseInt(n(r.commentList).attr("total")),o=parseInt(n(r.commentList).attr("currentPage"))==0?1:parseInt(n(r.commentList).attr("currentPage")),s=parseInt(n(r.commentList).attr("pageSize"))==0?10:parseInt(n(r.commentList).attr("pageSize")),u=n(this),e;f>0?(u.show(),e={type:"updatepage",labelname:r.pageLabelName,sourcename:r.listLabelName,pagesize:s,currentpage:o,cachetime:60,total:f},n.ajax({type:"GET",url:t,dataType:"jsonp",data:{jsondata:JSON2.stringify(e)},success:function(t){u.html(t.body);u.find("a").click(function(t){t.preventDefault();n(r.commentList).attr("currentPage",n(this).attr("page"));n(r.commentList).commentList({callback:function(){var r=window.location.hash,t,i;window.location.href=r?window.location.href.substring(0,window.location.href.indexOf("#"))+"#comment":window.location.href+"#comment";n.browser.msie&&(t=function(n){if(n.indexOf("#")!=-1){var t=n.substring(0,n.indexOf("#"));n=t}return n},i=document.title||"",document.title=t(i))}})});r.callback()}})):u.hide()})}});r(),function(){var n=function(n){if(n.indexOf("#")!=-1){var t=n.substring(0,n.indexOf("#"));n=t}return n},t=document.title||"";document.title=n(t);document.onpropertychange=function(){var i=document.title||"";window.event.propertyName==="title"&&i!==t&&(document.title=n(i))}}()}(jQuery),function(n){n.fn.ContentScore=function(){return this.each(function(){var t=n(this);t.find(".contentscore").mouseout(function(){var i=parseInt(n(".contentscore[scored='true']").attr("ranking"));t.find(".contentscore").each(function(){var t=parseInt(n(this).attr("ranking")),r=t<=i?siteSetup.sitePath+"Images/fstar.gif":siteSetup.sitePath+"Images/estar.gif";n(this).attr("src",r)})}).mouseover(function(){var i=parseInt(n(this).attr("ranking"));t.find(".contentscore").each(function(){var t=parseInt(n(this).attr("ranking")),r=t<=i?siteSetup.sitePath+"Images/fstar.gif":siteSetup.sitePath+"Images/estar.gif";n(this).attr("src",r)})}).click(function(){var t=parseInt(n(this).attr("itemId"));n.pe.ajax("contentpk",{params:{GenneralId:t,Score:parseInt(n(this).attr("ranking")),Type:0},success:function(t){var i=n(t),r=i.find("status").text(),u=i.find("result").text();switch(r){case"ok":n("#contentScoreInit").ContentScoreInit();break;case"AnonymousAgain":case"UserAgain":alert("对不起，您已经评价过了，请勿再评价！");break;case"err":alert("文章评分失败！")}}})})})};n.fn.ContentScoreInit=function(){return this.each(function(){$this=n(this);n.pe.ajax("GetContentPKResult",{params:{GenneralId:$this.attr("itemId")},success:function(t){var i=n(t),r=i.find("status").text(),u=i.find("totalCount").text(),f=i.find("averageScore").text();switch(r){case"ok":$this.find(".totalCount").html(u);$this.find(".averageScore").html(f)}}})})}}(jQuery);$(document).ready(function(){$("#contentScoreInit").ContentScoreInit();$("#contentScoreRanking").ContentScore()}),function(n){n.fn.ThunderDownLoad=function(){return this.each(function(){var t=n(this);t.click(function(i){i.preventDefault();n.ajax({type:"GET",dataType:"jsonp",url:"//click.gamersky.com/Common/GetHits.aspx",data:{id:t.attr("itemid"),script:"3",hot:"true"},success:function(){}});n.ajax({type:"GET",dataType:"jsonp",url:"//db4.gamersky.com/Common/ShowDownloadUrlJsonp.aspx",data:{urlid:t.attr("urlid"),id:t.attr("itemid")},success:function(n){if(n.status=="ok"){var i=n.body;thunderHrefAttr?t.attr(thunderHrefAttr,ThunderEncode(i)):t.attr("thunderHref",ThunderEncode(i));t.attr("thunderPid","51185");t.attr("thunderResTitle","");t.contextmenu(function(){ThunderNetwork_SetHref(this)});t.unbind("click").click(function(n){n.preventDefault();OnDownloadClick_Simple(this,2,4)});OnDownloadClick_Simple(t.get(0),2,4)}}})})})};n(document).ready(function(){n(".gsthunder").ThunderDownLoad();n(".dvurl1 li a").attr("target","_blank")})}(jQuery),function(n){Number.prototype.toFixed=function(n){var i=this+"",r;if(n||(n=0),i.indexOf(".")==-1&&(i+="."),i+=new Array(n+1).join("0"),new RegExp("^(-|\\+)?(\\d+(\\.\\d{0,"+(n+1)+"})?)\\d*$").test(i)){var i="0"+RegExp.$2,f=RegExp.$1,t=RegExp.$3.length,u=!0;if(t==n+2){if(t=i.match(/\d/g),parseInt(t[t.length-1])>4)for(r=t.length-2;r>=0;r--)if(t[r]=parseInt(t[r])+1,t[r]==10)t[r]=0,u=r!=1;else break;i=t.join("").replace(new RegExp("(\\d+)(\\d{"+n+"})\\d$"),"$1.$2")}return u&&(i=i.substr(1)),(f+i).replace(/\.$/,"")}return this+""};n.fn.KuScore=function(){return this.each(function(){var t=n(this),r,u,f,i,e,o;n(".midL1_2").bind("selectstart",function(){return!1});r="R"+t.attr("data-generalId")+"-"+t.attr("data-type");n.cookie(r)!==undefined?(t.unbind("mousemove"),u=JSON2.parse(n.cookie(r)),n(".S3_2").html(u.Sorce),f=parseInt(parseFloat(u.Sorce)*2)-1,t.find("ul").attr("class","u"+String(f)),i=0,t.find("ul li").each(function(t){t<=f&&((t&1)!=0&&(i=i+1),i=i+n(this).width())}),t.find("span").css("left",i)):(e=t.offset().left+1.5,o=t.width(),t.mousemove(function(i){var r=i.pageX-e,c=t.find("ul li").index(),u;if(r>=0&&r<=o-13){t.find("span").css("left",r);r<=0&&(n(".S3_2").html("0.0"),t.find("ul").attr("class",""));var f=0,s=0,h="";for(u=0;u<=c;u++)f=(u&1)!=0?f+7:f+6,s=s+.5,h=String(s).length==1?s.toFixed(1):s,r>f-((u&1)!=0?7:6)&&r<=f&&(n(".S3_2").html(h),t.attr("data-sorce",h),t.find("ul").attr("class","u"+u))}}))})};n.fn.Rating=function(){return this.each(function(){var t=n(this),i=t.attr("data-tips");n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/init",data:{generalId:t.attr("data-generalId"),ratingType:t.attr("data-type"),Action:"init"},success:function(r){if(r.hasOwnProperty("status"))switch(r.status){case"err":alert("提交"+i+"错误！");break;case"existuser":case"existip":alert("已"+i+"！")}else n("#"+t.attr("data-totleId")).html(r.Times),n("#"+t.attr("data-avgscore")).html(r.Average==10?"10":r.Average.toFixed(1))}});t.click(function(r){r.preventDefault();n(".S31_2").unbind("mousemove");var u="R"+t.attr("data-generalId")+"-"+t.attr("data-type");t.is("[data-group]")&&(u="R"+t.attr("data-generalId")+"-"+t.attr("data-group"));n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/rating",data:{Rating:JSON2.stringify({GenneralId:t.attr("data-generalId"),Sorce:t.attr("data-sorce"),Type:t.attr("data-type")}),Action:"rating"},success:function(r){if(r.hasOwnProperty("status"))switch(r.status){case"err":alert("提交"+i+"错误！");break;case"existuser":case"existip":alert("已"+i+"！")}else n("#"+t.attr("data-totleId")).html(r.Times),n("#"+t.attr("data-avgscore")).html(r.Average==10?"10":r.Average.toFixed(1)),n.cookie(u,JSON2.stringify({GenneralId:t.attr("data-generalId"),Sorce:t.attr("data-sorce"),Type:t.attr("data-type")}),{path:"/",expires:365}),n(".S31_2").KuScore()}})})})};n.fn.RatingGame=function(){return this.each(function(){var t=n(this),i=t.attr("data-tips");n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/init",data:{generalId:t.attr("data-generalId"),ratingType:t.attr("data-type"),Action:"init"},success:function(r){if(r.hasOwnProperty("status"))switch(r.status){case"err":alert("提交"+i+"错误！");break;case"existuser":case"existip":alert("已"+i+"！")}else n("#"+t.attr("data-totleId")).html(r.Times),n("#"+t.attr("data-avgscore")).html(r.Average==10?"10":r.Average.toFixed(1))}});t.click(function(r){r.preventDefault();n(".S31_2").unbind("mousemove");var u="R"+t.attr("data-generalId")+"-"+t.attr("data-type");t.is("[data-group]")&&(u="R"+t.attr("data-generalId")+"-"+t.attr("data-group"));n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/rating",data:{Rating:JSON2.stringify({GenneralId:t.attr("data-generalId"),Sorce:t.attr("data-sorce"),Type:t.attr("data-type")}),Action:"rating"},success:function(r){if(r.hasOwnProperty("status"))switch(r.status){case"err":alert("提交"+i+"错误！");break;case"existuser":case"existip":alert("已"+i+"！")}else n("#"+t.attr("data-totleId")).html(r.Times),n("#"+t.attr("data-avgscore")).html(r.Average==10?"10":r.Average.toFixed(1)),n.cookie(u,JSON2.stringify({GenneralId:t.attr("data-generalId"),Sorce:t.attr("data-sorce"),Type:t.attr("data-type")}),{path:"/",expires:365}),n(".S31_2").KuScore()}})})})};n.fn.RatingGamersky=function(){return this.each(function(){var t=n(this);n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/grade",data:{generalId:t.attr("data-generalId"),Action:"grade"},success:function(n){var i;n.EditorRating!=""&&n.RatingUrl!=""?(i=" <a href='"+n.RatingUrl+"'  target='_blank'><div class='PFl_num S1_2'>"+n.EditorRating+"<\/div><\/a>",t.append(i)):(i="<div class='PFl_num S1_2'>--<\/div>",t.append(i))}})})};n.fn.RatingGroup=function(){return this.each(function(){for(var i=n(this),t=i.attr("data-tips"),f=n(".ratingGroupAction").length,r="",u=0;u<f;u++)r+=n(".ratingGroupAction").eq(u).attr("data-type")+",";r!=""&&(n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/initgroup",data:{generalId:i.attr("data-generalId"),ratingGroupType:r,Action:"initGroup"},success:function(data){if(data.hasOwnProperty("status"))switch(data.status){case"err":alert("提交"+t+"错误！");break;case"existuser":case"existip":alert("已"+t+"！")}else{n(".ratingGroupAction").each(function(){for(var i,r=!1,t=0;t<data.length;t++)data[t].Type==n(this).attr("data-type")&&(i=n(this).attr("data-totleid"),n("#"+i).html(data[t].Times),r=!0);r||(i=n(this).attr("data-totleid"),n("#"+i).html(0))});var like=n("#like").html(),unlike=n("#unlike").html(),sorce=Math.round(eval(like)/(eval(like)+eval(unlike))*100),btnWidth=n(".btn12").width();isNaN(sorce)?(n(".btn12").attr("style","margin-left:-"+btnWidth/2+"px;"),n(".ZSr_m").attr("style","background-position:-74px  0;"),n("#Sorce").html(0)):(n(".btn12").attr("style","margin-left:"+Math.round(-btnWidth/2-(btnWidth/2-btnWidth*sorce/100))+"px;"),n(".ZSr_m").attr("style","background-position:"+Math.round(148*sorce/100-148)+"px 0;"),n("#Sorce").html(sorce))}}}),i.find(".ratingGroupAction").click(function(i){var r,u;i.preventDefault();r=n(this);n(".S31_2").unbind("mousemove");u="R"+r.attr("data-generalId")+"-"+r.attr("data-type");r.is("[data-group]")&&(u="R"+r.attr("data-generalId")+"-"+r.attr("data-group"));n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/rating",data:{Rating:JSON2.stringify({GenneralId:r.attr("data-generalId"),Sorce:r.attr("data-sorce"),Type:r.attr("data-type")}),Action:"rating"},success:function(data){if(data.hasOwnProperty("status"))switch(data.status){case"err":alert("提交"+t+"错误！");break;case"existuser":case"existip":alert("已"+t+"！")}else{n("#"+r.attr("data-totleId")).html(data.Times);var like=n("#like").html(),unlike=n("#unlike").html(),sorce=Math.round(eval(like)/(eval(like)+eval(unlike))*100),btnWidth=n(".btn12").width();isNaN(sorce)?(n(".btn12").attr("style","margin-left:-"+btnWidth+"px;"),n(".ZSr_m").attr("style","background-position:-74px 0;"),n("#Sorce").html(0)):(n("#Sorce").html(sorce),n(".ZSr_m").attr("style","background-position:"+Math.round(148*sorce/100-148)+"px 0;"),n(".btn12").attr("style","margin-left:"+Math.round(-btnWidth/2-(btnWidth/2-btnWidth*sorce/100))+"px;"));n("#"+r.attr("data-avgscore")).html(data.Average==10?"10":data.Average.toFixed(1));n.cookie(u,JSON2.stringify({GenneralId:r.attr("data-generalId"),Sorce:r.attr("data-sorce"),Type:r.attr("data-type")}),{path:"/",expires:365});n(".S31_2").KuScore()}}})}))})};n.fn.RatingGroupLike=function(){return this.each(function(){var t=n(this);t.find("li").each(function(t,i){var u=n(i),f=n(i).attr("data-generalid"),r=n(i).attr("data-type");r&&n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/initgroup",data:{generalId:f,ratingGroupType:r,Action:"initGroup"},success:function(n){(n.length=1)&&u.find("div a .huo").html(n[0].Times)}})})})};n.fn.Ratingmore=function(){return this.each(function(){var t=n(this),u=t.find(".gamerskyrating"),f=t.find(".userrating"),i;n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/grade",data:{generalId:t.attr("data-generalId"),Action:"grade"},success:function(t){t.EditorRating!=""&&t.EditorRating!="0"?n(u).html(t.EditorRating):n(u).html("--")}});n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/init",data:{generalId:t.attr("data-generalId"),ratingType:n(f).attr("data-type"),Action:"init"},success:function(t){t.hasOwnProperty("status")||n(f).html(t.Average==10?"10":t.Average.toFixed(1))}});var e=t.find(".ratingGroupAction").length,r="";for(i=0;i<e;i++)r+=t.find(".ratingGroupAction").eq(i).attr("data-type")+",";r!=""&&(n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/initgroup",data:{generalId:t.attr("data-generalId"),ratingGroupType:r,Action:"initGroup"},success:function(data){if(!data.hasOwnProperty("status")){n(".ratingGroupAction").each(function(){for(var r,u=!1,i=0;i<data.length;i++)data[i].Type==n(this).attr("data-type")&&(r=n(this).attr("data-totleid"),t.find("#"+r).html(data[i].Times),u=!0);u||(r=n(this).attr("data-totleid"),t.find("#"+r).html(0))});var like=t.find("#like").html(),unlike=t.find("#unlike").html(),sorce=Math.round(eval(like)/(eval(like)+eval(unlike))*100),btnWidth=n(".jindu").width();isNaN(sorce)?t.find(".tiao").attr("style","width:"+btnWidth/2+"px;"):t.find(".tiao").attr("style","width:"+Math.round(btnWidth*sorce/100)+"px;")}}}),t.find(".ratingGroupAction").click(function(i){var r,u,f;i.preventDefault();r=n(this);u=t.attr("data-tips");n(".S31_2").unbind("mousemove");f="R"+r.attr("data-generalId")+"-"+r.attr("data-type");r.is("[data-group]")&&(f="R"+r.attr("data-generalId")+"-"+r.attr("data-group"));n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/rating",data:{Rating:JSON2.stringify({GenneralId:r.attr("data-generalId"),Sorce:r.attr("data-sorce"),Type:r.attr("data-type")}),Action:"rating"},success:function(data){if(data.hasOwnProperty("status"))switch(data.status){case"err":alert("提交"+u+"错误！");break;case"existuser":case"existip":alert("已"+u+"！")}else{t.find("#"+r.attr("data-totleId")).html(data.Times);var like=t.find("#like").html(),unlike=t.find("#unlike").html(),sorce=Math.round(eval(like)/(eval(like)+eval(unlike))*100),btnWidth=n(".jindu").width();isNaN(sorce)?t.find(".tiao").attr("style","width:"+btnWidth/2+"px;"):t.find(".tiao").attr("style","width:"+Math.round(btnWidth*sorce/100)+"px;");n.cookie(f,JSON2.stringify({GenneralId:r.attr("data-generalId"),Sorce:r.attr("data-sorce"),Type:r.attr("data-type")}),{path:"/",expires:365})}}})}))})};n.fn.Ratingmores=function(){var t="";(n(this).each(function(){t!=""&&(t=t+",");t=t+n(this).attr("data-generalid")}),t!="")&&(n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/grades",data:{generalId:t,Action:"grade"},success:function(t){n.each(t.result,function(i,r){r.editorRating!=""&&r.editorRating!="0"?n(".ratingdownmore[data-generalid='"+r.Id+"']").find(".gamerskyrating").html(t.EditorRating):n(".ratingdownmore[data-generalid='"+r.Id+"']").find(".gamerskyrating").html("--")})}}),n.ajax({type:"GET",dataType:"jsonp",url:"//i.gamersky.com/apirating/inits",data:{jsondata:JSON.stringify({generalIds:t,ratingType:0,Action:"init"})},success:function(t){n.each(t,function(t,i){n(".ratingdownmore[data-generalid='"+t+"']").find(".userrating").html(i==10?"10":i.toFixed(1))})}}))};n(document).ready(function(){n(".ratingGroup").RatingGroup();n(".ratingAction").Rating();n(".gameratingAction").RatingGame();n(".S31_2").KuScore();n("#gamerskyrating").RatingGamersky();n(".ratingGroupLike").RatingGroupLike();n(".ratingmore").Ratingmore()})}(jQuery),function(n){function u(n){return n&&!o(n)?(alert("您输入的邮箱有误请重新输入"),!1):!0}function f(n){return n&&!s(n)?(alert("您输入的电话有误请重新输入"),!1):!0}function o(n){return/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(n)}function s(n){return/((\d{11})|^((\d{7,8})|(\d{4}|\d{3})-(\d{7,8})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1})|(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1}))$)/.test(n)}function c(n){let t=`
                <div class="jubao_mode"></div>
                <div class="jubao">
                    <div class="header">
                        <div class="gz">
                            <span>举报规则</span>
                            <img src="https://image.gamersky.com/webimg13/tip.png">
                        </div>
                        <div class="header_title">举报</div>
                        <div class="close">
                            ×
                        </div>
                    </div>
                    <div class="list">
            `;return n.forEach(n=>{t+=`
                        <div class="item">
                            <div class="tit">${n.title}</div>
                                <div class="select">
                    `,n.select.forEach(n=>{t+=`
                                <div class="select_item">
                                    <div class="img"></div>
                                    <span>${n}</span>
                                </div>
                                `}),t+=`       
                                </div>
                        </div>
                    `}),t+=`        <div class="dianhua input_tit">
                        <div class="title">联系电话(选填)</div>
                        <input type="text" placeholder="请输入用于联系的电话号码">
                    </div>
                    <div class="email input_tit">
                        <div class="title">联系邮箱(选填)</div>
                        <input type="text" placeholder="请输入用于联系的邮箱">
                    </div>
                    <div class="liyou input_tit">
                        <div class="title">举报理由(选填)</div>
                        <textarea placeholder="请输入举报理由"></textarea>
                        <div class="tarea-num"><span class="tarea-dnum">0</span>/<span class="tarea-bnum">300</span></div>
                    </div>
                    <ul class="cmtRt-pic">
                        <li class="add">
                            <a href="javascript:;" class="upPicBtn">
                                <p>上传图片</p>
                            </a>
                            <input id="reportUpload" data-imagesize="10" accept="image/jpg,image/jpeg,image/png,image/gif" type="file" style="display:none" name="files" method="POST" multiple="true">
                        </li>
                    </ul>
                </div>
                    <div class="footer">
                        <div class="btn_box">
                            <div class="btn qx">
                                取消
                            </div>
                            <div class="btn qr">
                                提交
                            </div>
                        </div>
                       
                    </div>
                </div>

            `}function r(){var i=n("#reportUpload"),r=parseInt(i.attr("data-imagesize")),t=1;i.trigger("click");i.fileupload({dataType:"json",url:"//i.gamersky.com/uploadpic/index",sequentialUploads:!0,xhrFields:{withCredentials:!0},autoUpload:!0,done:function(t,i){var r=i.result,e=i.originalFiles.indexOf(i.files[0]),u=i.batchTaskNumber,f;r.status=="ok"&&(f='<img imageclass="'+r.imageclass+'" src="'+r.tinysquare+'" small="'+r.small+'" tiny="'+r.tiny+'" origin="'+r.original+'" /><a href="javascript:;" class="picdel"><\/a>',n(".cmtRt-pic ."+u+":eq("+e+")").removeClass("temp").addClass("done").html(f),n(".cmtRt-pic ."+u+".done").length==i.originalFiles.length&&n(".cmtRt-pic ."+u+".temp").remove(),n(".cmtRt-pic li.pic .picdel").each(function(){n(this).off("click").on("click",function(){var t=n(this).siblings("img").attr("origin");n(this).parent(".pic").remove();n(".cmtRt-pic li.add").show();n.ajax({type:"get",dataType:"jsonp",url:"//i.gamersky.com/uploadpic/delete?origin="+t})})}))},change:function(i,u){var h=n(".cmtRt-pic li.pic").length,c=u.files.length,f=h+c,l=t-f,e,o,s;if(f>t)return alert("上传图片不能超过"+t+"张！"),!1;if(e=!1,n.each(u.files,function(n,t){t.size/1024>r*1024&&(e=!0)}),e)return alert("图片容量超过"+r+"MB，无法上传！"),!1;f==t&&n(".cmtRt-pic li.add").hide();o=function(n){for(var t="",i=0;i<n;i++)t+="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(Math.random()*36));return t};s=(new Date).getTime();u.batchTaskNumber="b"+s+o(4);n.each(u.files,function(){n(".cmtRt-pic li.add").before('<li class="pic temp '+u.batchTaskNumber+'"><img class="load" src="//image.gamersky.com/webimg15/loading.gif"><\/li>')})}})}function t(t,e){n.getScript("//j.gamersky.com/file/ajaxfileupload.js",function(){var s=n("#jcjbContentData"),l=s.attr("title"),a=s.attr("data-generalId"),v=c(h),o="";n("body").append(v);n(".jubao .header .gz").on("click",function(){var n=document.createElement("a");n.href="https://www.gamersky.com/news/202412/1858998.shtml";n.target="_blank";document.body.appendChild(n);n.click();n.remove()});n(".jubao .footer .qx").on("click",function(){n(".jubao").remove();n(".jubao_mode").remove()});n(".jubao .header .close").on("click",function(){n(".jubao").remove();n(".jubao_mode").remove()});n(".jubao .list .item .select_item").on("click",function(){n(this).find(".img").hasClass("cur")?(n(this).find(".img").removeClass("cur"),o=""):(n(this).find(".img").addClass("cur"),o=n(this).text());n(".jubao .list .item .select_item").each((t,i)=>{n(i).text()!=n(this).text()&&n(i).find(".img").removeClass("cur")})});n(".jubao .upPicBtn").on("click",function(){r()});n(".addImgBtn").unbind("click").click(function(){r()});n(".jubao .list textarea").on("keyup blur",function(){var t=i(n(this).val()).length;n(".jubao").find(".tarea-dnum").text(t)});n(".jubao .footer .qr").unbind("click").click(function(r){var p,s;r.preventDefault();var h=n(".jubao .list .liyou textarea").val()||"",c=n(".jubao .list .dianhua input").val()||"",v=n(".jubao .list .email input").val()||"",w=n(".jubao .cmtRt-pic li img").attr("src")||"",y=n(".jubao").find(".tarea-bnum").text()|0,b=n(".jubao").find(".tarea-dnum").text()|0;if(o=="")return alert("请选择举报类型！"),!1;if(u(v)!=!1&&f(c)!=!1){if(!!i(h)&&b>y)return alert("举报理由文字超过"+y+"字！"),!1;p=[];n(".jubao ").find(".cmtRt-pic li.pic").length>0&&n(".jubao ").find(".cmtRt-pic li.pic").each(function(){var t="jpg";n(this).find("img").attr("imageclass")=="gif"&&(t="gif");p.push({imageType:t,tinysquare:n(this).find("img").attr("src"),origin:n(this).find("img").attr("origin"),small:n(this).find("img").attr("small"),tiny:n(this).find("img").attr("tiny")})});s={};s.Title=l;s.GeneralId=a;s.Description=h;s.Phone=c;s.Email=v;s.PhotoUrl=w;s.IsReport=t;s.State=e;s.reportReasonType=o.trim();console.log(s);n.ajax({type:"get",dataType:"jsonp",url:"//db5.gamersky.com/CorrectReport.aspx",data:{ContentData:JSON.stringify(s)},contentType:"application/json;charset=utf-8",cache:!1,success:function(t){n(".jubao").remove();n(".jubao_mode").remove();alert(t.body)},error:function(){}});n(".newreport-mask,.newreport-popup").fadeOut(300,function(){n(this).remove()})}});n(".newreport-close,.tj2-botton").unbind("click").click(function(t){t.preventDefault();n(".newreport-mask,.newreport-popup").fadeOut(300,function(){n(this).remove()})})})}var i=function(n){return n.replace(/(^\s*)|(\s*$)/g,"")};const h=[{title:"政治敏感",select:["涉政不当内容","危害国家言论"]},{title:"违法违规",select:["淫秽色情","赌博诈骗","涉及违禁物品","包含危险链接","教唆/诱导自杀","侮辱英烈"]},{title:"观感不适",select:["血腥暴力","低俗内容","猎奇邪典"]},{title:"侵权相关",select:["侵害企业权益","侵害个人权益","侵犯隐私"]},{title:"未成年相关",select:["未成年不宜浏览","未成年不良行为"]},{title:"造谣及不实信息",select:["标题内容不符","造谣抹黑","虚假不实信息"]},{title:"不友善行为",select:["引战骚扰","制造对立","剧透"]},{title:"违反公序良俗",select:["网络暴力","其它"]},];n.fn.ContentCorrect=function(){return this.each(function(){n(this).click(function(i){i.preventDefault();n(this).UserOnline(function(){t(0,999)})})})};n.fn.ContentReport=function(){return this.each(function(){n(this).click(function(i){i.preventDefault();n(this).UserOnline(function(){t(1,1)})})})};n.fn.ContentEvaluation=function(){return this.each(function(){n(this).click(function(i){i.preventDefault();n(this).UserOnline(function(){t(0,999)})})})};n.fn.Collection=function(){return this.each(function(){var i=n(this),t=n("#jcjbContentData"),r=t.attr("data-generalId"),u=t.attr("data-gameLib");i.click(function(t){t.preventDefault();n.ajax({type:"get",dataType:"jsonp",url:"//i.gamersky.com/api/addcollect",data:{generalId:r,gameLib:u},success:function(n){n.status=="ok"?alert("收藏成功！"):alert(n.body)},error:function(){}})})})};n(document).ready(function(){n("#jcjbContentData").length>0&&n(".JCJB").show();n(".btnContentReport").ContentReport();n(".btnContentCorrect").ContentCorrect();n(".btnContentEvaluation").ContentEvaluation();n(".btnCollection").Collection()})}(jQuery),function(n){n.fn.SoftGl=function(){return this.each(function(){var i="PE_U_Soft",t;n("#jcjbContentData").attr("data-tableName")!=null&&(i=n("#jcjbContentData").attr("data-tableName"));t={GeneralId:n("#jcjbContentData").attr("data-generalId"),NodeId:n("#jcjbContentData").attr("data-nodeId"),Top:8,TableName:i};n.ajax({type:"POST",url:"//db2.gamersky.com/ContentAjaxNew.aspx",dataType:"jsonp",data:{jsondata:JSON2.stringify({type:"getcorrelation",GeneralId:t.GeneralId,NodeId:t.NodeId,Top:t.Top,TableName:t.TableName})},success:function(t){var i=t,r;if(i==undefined||i.length<=0)n("#softwenda").hide();else for(r=0;r<i.length;r++)n("#softwenda").find(".wd").append('<li class="like"><a href="'+i[r].url+'" target="_blank">'+i[r].title+"<\/a><\/li>")}})})};n.fn.SoftCorrelation=function(t){return this.each(function(){var f=n(this),e=t,r=f.find(".contt").eq(e),i=r.attr("value"),o=!1,u;f.find(".contt").each(function(t,u){var f=n(u).find(".txt .like li").length;f<=0&&(n(u).hide(),o=!0,i=parseInt(i)+parseInt(n(u).attr("value")),r.find(".tit").removeClass().addClass("tit tp"+i),r.find(".txt").removeClass().addClass("txt th"+i),r.find(".like").removeClass().addClass("like lh"+i))});u=parseInt(i)-1;e==0?n(".txtlist .tl_like.tr .contt:eq(0) .txt .like li:gt("+u+")").remove():n(".txtlist .tl_like.tl .contt:eq(1) .txt .like li:gt("+u+")").remove()})};n.fn.DownContentHot=function(){return this.each(function(){var t=n(this),i=t.attr("data-ganeralId");n.ajax({type:"POST",url:"//db2.gamersky.com/ContentAjaxNew.aspx",dataType:"jsonp",data:{jsondata:JSON2.stringify({type:"getcontenthot",GeneralId:i})},success:function(n){var i=n;i.status=="ok"&&t.html("&nbsp;"+i.body)}})})};n(document).ready(function(){n(".td_dl[itemprop='inContentHot']").DownContentHot();n("#softwenda").SoftGl();n(".txtlist .tl_like.tl").SoftCorrelation(1);n(".txtlist .tl_like.tr").SoftCorrelation(0)})}(jQuery),function(n){n.fn.ContentVote=function(){return this.each(function(){var t=n(this),i=t.attr("data-id");n.ajax({type:"GET",dataType:"jsonp",url:"//db5.gamersky.com/ContentVoteJsonp.aspx",data:{id:i,a:"0"},success:function(r){var u,f;for(t.find(".votelist .tit").text(r.vote.VoteTitle),u=0;u<r.items.length;u++)f=n('<li class="txt"><input type="radio" id="v'+u+'" name="v" value=""><label for="v'+u+'"><\/label><\/li>'),r.vote.ItemType>0&&(f=n('<li class="txt"><input type="checkbox" name="v"id="v'+u+'" value=""><label for="v'+u+'"><\/label><\/li>')),f.find("input[name='v']").attr("value",r.items[u].Title),f.find("label").text(r.items[u].Title),t.find(".votelist .btn").before(f);t.find(".toupiao-vbtn").click(function(){var r="";return t.find("input[name='v']").each(function(){n(this).attr("checked")&&(r.length>0&&(r=r+","),r=r+n(this).attr("value"))}),r.length==0&&alert("请至少选择一个选项！"),n.ajax({type:"GET",dataType:"jsonp",url:"//db5.gamersky.com/ContentVoteJsonp.aspx",data:{id:i,a:"1",v:r},success:function(n){n.status=="ok"?alert("投票成功！"):alert(n.message)}}),!1});t.show()}})})};n.fn.HotVote=function(){return this.each(function(){var t=n(this),i=n(this).attr("data-itemId");n.ajax({type:"GET",dataType:"jsonp",url:"//db5.gamersky.com/VoteJson.aspx",data:{id:i,a:"init"},success:function(n){t.find(".redPollNumber").html(n.RedPoll);t.find(".bluePollNumber").html(n.BluePoll);parseInt(n.RedPoll)+parseInt(n.BluePoll)>0&&t.find(".tiao").width(n.RedPoll/(parseInt(n.RedPoll)+parseInt(n.BluePoll))*100+"%")}});t.find(".votebtn").click(function(){$votebtn=n(this);var r="hotvote-"+i;if(n.cookie(r)!==undefined&&n.cookie(r)!==null){($votebtn.hasClass("OK")||$votebtn.hasClass("NO"))&&alert("当前主题只允许同一IP投票1次，您已经超过了投票次数");return}n.ajax({type:"GET",dataType:"jsonp",url:"//db5.gamersky.com/VoteJson.aspx",data:{id:i,a:"vote",p:$votebtn.attr("data-point")},success:function(i){if(i.status=="ok"){$votebtn.attr("data-point")=="red"?t.find(".redPollNumber").html(parseInt(t.find(".redPollNumber").html())+1):t.find(".bluePollNumber").html(parseInt(t.find(".bluePollNumber").html())+1);var u=parseInt(t.find(".redPollNumber").html()),f=parseInt(t.find(".bluePollNumber").html());u+f>0&&t.find(".tiao").width(u/(u+f)*100+"%");n.cookie(r,"1",{path:"/",expires:365})}else($votebtn.hasClass("OK")||$votebtn.hasClass("NO"))&&alert("当前主题只允许同一IP投票1次，您已经超过了投票次数")}})})})};n(document).ready(function(){n(".toupiao-Content").length>0?(n(".toupiao-Content").html(n(".toupiao-init").html()),n(".toupiao-Content").attr("data-id",n(".toupiao-init").attr("data-id")),n(".toupiao-Content").ContentVote(),n(".toupiao-init").hide()):n(".toupiao-init").ContentVote();n(".hotVote").HotVote()})}(jQuery);
(function(n){var r=location.protocol,u=dateFtt({f:5}),f=r+"//j.gamersky.com/web2015/ku/css/ku.qzcmt.css?v="+u,t,i;n("<link>").attr({rel:"stylesheet",href:f}).appendTo("head");n.cachedScript("//j.gamersky.com/web2015/ku/js/guanzhu.js?v="+dateFtt({f:10}));t=function(n){return n.filter(function(t,i){return n.indexOf(t,0)==i})};i={formatDate:function(n,t){var f,o;n=n||"yyyy-mm-dd hh:ff:ss";f=function(n){var t=function(n){return n<10?"0"+n:n};return{yyyy:n.getFullYear(),mm:t(n.getMonth()+1),dd:t(n.getDate()),hh:t(n.getHours()),ff:t(n.getMinutes()),ss:t(n.getSeconds())}};t=isNaN(t)?t.replace(/-/g,"/"):t;var h=new Date,c=new Date(t),e=f(h),i=f(c),l=i.yyyy+"/"+i.mm+"/"+i.dd,a=e.yyyy+"/"+e.mm+"/"+e.dd,r=new Date(l).getTime(),u=new Date(a).getTime(),s=new Date(u-864e5).getTime(),v=new Date(u-1728e5).getTime();for(o in i)n=n.replace(new RegExp(o,"g"),i[o]);return r>=u?"今天 "+n.split(" ")[1]:r<u&&s<=r?"昨天 "+n.split(" ")[1]:r<s&&v<=r?"前天 "+n.split(" ")[1]:n},qzcmtHtm:function(t,r){var u="",f=n("#jcjbContentData").attr("data-generalid");return n.each(t,function(n,t){var r="https://club.gamersky.com/activity/"+t.clubContextId,e=i.formatDate("yyyy-mm-dd hh:ff",t.createTime),f;u+='<div class="qzcmt-cont" cmtid="'+t.clubContextId+'">';u+='<div class="qzcmt-head">';u+='<a class="userlink" uid="'+t.userId+'" href="'+t.domain+'" target="_blank">';u+='<img src="'+t.userFace.replace("http:","")+'">';u+="<\/a>";u+="<\/div>";u+='<div class="qzcmt-cont-top">';u+='<div class="qzcmt-user">';u+='<div class="user-name">';u+='<a class="uname" href="'+t.domain+'" uid="'+t.userId+'" target="_blank">'+t.userName+"<\/a>";u+='<a class="level" href="javascript:;">Lv'+t.level+"<\/a>";t.isProve&&(u+='<a class="qzheng">',u+='<img src="'+t.proveIcon.replace("http:","")+'">',u+='<div class="zhenglayer"><div class="con"><span>'+t.proveMessage+'<\/span><\/div><div class="vvv"><\/div><\/div>',u+="<\/a>");u+="<\/div>";u+="<\/div>";u+="<\/div>";u+="<\/div>";t.imageTotal>0&&!t.isVideo&&(u+='<div class="qzcmt-picdiv">',u+='<a href="'+r+'" target="_blank">',u+='<img src="'+(t.imageInfos[0].tinysquare||t.imageInfos[0].tiny).replace("http:","")+'" />',u+="<\/a>",u+="<\/div>");t.isVideo&&(t.siteName=="youku"||t.siteName=="bilibili")&&(f=t.videoImage.replace("small","tinysquare"),u+='<div class="qzcmt-picdiv">',u+='<a href="'+r+'" target="_blank">',u+='<div class="img"><img src="'+f+'" /><i><\/i><\/div>',u+="<\/a><\/div>");u+='<div class="qzcmt-content">';u+='<div class="qzcmt-content-tit">';t.isElite&&(u+='<span class="jin">精品<\/span>');t.title!=""&&(u+='<a href="'+r+'" target="_blank">'+t.title+"<\/a>");u+="<\/div>";u+='<div class="qzcmt-content-txt">';u+='<a href="'+r+'" target="_blank">';u+=t.content;t.isVideo&&t.siteName!="youku"&&t.siteName!="bilibili"&&(u+="【视频链接】");u+="<\/a>";u+="<\/div>";u+='<div class="qzcmt-action">';u+='<div class="time">'+e+"<\/div>";u+='<div class="handle">';u+='<a href="'+r+'" target="_blank" class="evt-reply" cmtid="'+t.clubContextId+'" data-isload="false">0<\/a>';u+='<a href="javascript:;" class="evt-digg" cmtid="'+t.clubContextId+'" data-isdigg="false" data-isload="false">0<\/a>';u+="<\/div>";u+="<\/div>";u+="<\/div>";u+="<\/div>"}),u+='<div class="qzcmt-more">',u+='<a href="https://club.gamersky.com/topic/'+r+'/" target="_blank">查看更多游戏评测<\/a>',u+="<\/div>"}};n.fn.getClub=function(){return this.each(function(){var r=n(this),t=r.attr("sid");n(".lxbdr,.qzcmt").show();n(".clubBtn").attr("href","//club.gamersky.com/editor/EditorPageInPC/index.html?type=faTie&topicId="+t);n.ajax({type:"get",dataType:"jsonp",url:"//club.gamersky.com/club/api/GetKuClubContent",data:{jsondata:JSON.stringify({topicId:t,pageSize:3})},success:function(u){var f,e,o;u.status=="ok"&&(f=JSON.parse(u.body),f.total==0?n(".qzcmt").html('<div class="qzcmt-not">暂无评测<\/div>'):f.clubContextInfos.length==0?n(".loadend").show():(e=[],n.each(f.clubContextInfos,function(n,t){n<=2?e.push(t):null}),o=i.qzcmtHtm(e,t),r.attr("data-loading",!1).html(o),n(".level").level(),n(".evt-digg").likes(),n(".evt-digg,.evt-noDigg").addLikes(),n(".evt-reply").getCommentCount()))}})})};n.fn.level=function(){return this.each(function(){n(this).on("click",".level",function(){var t=n(this);t.UserOnline(function(){t.attr({href:"//i.gamersky.com/my/level",target:"_blank"})})})})};n.fn.getCommentCount=function(){var i=[];if(n(this).each(function(){n(this).attr("data-isload")=="false"&&(i.push(n(this).attr("cmtid")),n(this).attr("data-isload",!0))}),i.length==0)return!1;i=t(i);n.ajax({type:"post",dataType:"jsonp",url:"//club.gamersky.com/club/api/getcommentcount",data:{jsondata:JSON.stringify({clubContentId:String(i)})},success:function(t){if(t.status=="ok"){var i=n.parseJSON(t.body);n.each(i,function(t,i){n(".evt-reply[cmtid='"+i.Id+"']").html(i.commentCount)})}}})};n.fn.likes=function(){var i=[];if(n(this).each(function(){n(this).attr("data-isload")=="false"&&(i.push(n(this).attr("cmtid")),n(this).attr("data-isload",!0))}),i.length==0)return!1;i=t(i);n.ajax({type:"post",dataType:"jsonp",url:"//club.gamersky.com/club/api/getlike",data:{jsondata:JSON.stringify({clubContentId:String(i)})},success:function(t){if(t.status=="ok"){var i=n.parseJSON(t.body);n.each(i,function(t,i){n(".evt-digg[cmtid='"+i.Id+"']").html(i.digg)})}}})};n.fn.addLikes=function(){return this.each(function(){var t=n(this);t.unbind("click").click(function(i){i.preventDefault();var r=t.attr("cmtid"),u=t.attr("data-isdigg");n(this).UserOnline(function(){n.ajax({type:"get",dataType:"jsonp",url:"//club.gamersky.com/club/api/addlike",data:{jsondata:JSON.stringify({clubContentId:r,fromDevice:0,isTread:u})},success:function(n){n.status=="err"?alert(isTread=="true"?"你已经踩过！":n.body):isTread=="false"&&t.html(parseInt(t.html())+1)}})})})})};n(document).ready(function(){n(".qzcmt").getClub()})})(jQuery);
