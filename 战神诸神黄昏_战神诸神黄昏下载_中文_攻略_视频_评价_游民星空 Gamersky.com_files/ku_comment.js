(function ($) {
    var $kuGameScore = $(".kuGameScore");
    var gameid = $kuGameScore.attr("gameid"); //获取游戏ID
    // var gameid = 1255733; //获取游戏ID

    // var gameid = window.location.search.split('=')[1]; //获取游戏ID


    var gamename = $kuGameScore.attr("gamename"); //获取游戏名
    var userId = 0;
    var UserCookie = cookie("UserCookie");
    var commentList = []
    var pageIndex = 1
    var pageSize = 20
    var total = 0;
    var userLabelTypes = []
    var platforms = []
    if (UserCookie) {
        var response = $.parseJSON(UserCookie);
        userId = response.userid;
    }
    var Mid_comment = $('#Mid_comment');
    var duanAllCommentCount = ''
    var changAllCommentCount = ''
    var order = 'tuiJian'
    var c_order = 'tuiJian'

    var currentPlatform = '全部'

    var isGamePublished = false

    // getGameInfo()
    //获取评论列表

    $.fn.getCommentList = function (e) {
        if (e) {

            $.ajax({
                type: "POST",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/gameScoreDetailPage/index/6.0.0/" + userId,
                // url: "https://router-test.gamersky.com/@133_8084/gameScoreDetailPage/index/6.0.0/" + userId,
                contentType: 'application/json',
                data: JSON.stringify({
                    commentFiltrateParams: {
                        userLabelTypes: userLabelTypes
                    },
                    contentUrl: window.location.href + '?gsGameId=' + gameid,
                    exposeTimes: 0,
                    isFilterConditionShow: true,
                    listName: '评论列表',
                    order: order,
                    pageIndex: pageIndex - 1,
                    pageSize: pageSize,
                    projectId: 0,
                    turn: 0,
                    version: "2.0"
                }),
                success: function (res) {
                    duanAllCommentCount = res.allListElementsCount
                    isGamePublished = res.isGamePublished

                    $('#Mid_comment .comment_header .top .duanNum').text(duanAllCommentCount)
                    commentList = res.listElements;
                    total = parseInt(res.allListElementsCount / 20) > 0 ? parseInt(res.allListElementsCount / 20) : 1;
                    platforms = res.platforms
                    if ($('#Mid_comment .comment_header .top .left .duan').hasClass('cur') && commentList.length > 0) {
                        commentHtml.creatComment(e)
                        createPagination(total, pageIndex)
                    }

                }
            });
        } else {
            setTimeout(function () {

                $.ajax({
                    type: "POST",
                    dataType: 'json',
                    url: "https://router5.gamersky.com/@/subjectPage/index/6.0.0/" + userId,
                    contentType: 'application/json',
                    data: JSON.stringify({
                        contentType: '',
                        contentUrl: 'https://club.gamersky.com/topic/' + $('#Mid_comment .comment_header .top .left .chang').attr('reviewSubjectId'),
                        exposeTimes: 0,
                        listName: '游戏长评列表',
                        order: c_order,
                        pageIndex: pageIndex - 1,
                        pageSize: pageSize,
                        projectId: 0,
                        turn: 0,
                        version: "2.0"
                    }),
                    success: function (res) {
                        changAllCommentCount = res.allCommentCount
                        $('#Mid_comment .comment_header .top .changNum').text(changAllCommentCount)
                        commentList = res.listElements
                        total = Math.ceil(res.allListElementsCount / 20) > 0 ? Math.ceil(res.allListElementsCount / 20) : 1;
                        console.log(total);

                        if ($('#Mid_comment .comment_header .top .left .chang').hasClass('cur') && commentList.length > 0) {
                            commentHtml.creatComment(e)
                            createPagination(total, pageIndex)
                        }
                    }
                });

            }, 500)

        }

    }
    var commentHtml = {



        getCommentHeader() {

            let htm =
                `<div class="comment_header">
                    <div class="top">
                        <div class="left">
                            <div class="btn duan cur"> 
                                <div class="txt">玩家短评</div>
                                <span class="number duanNum">${duanAllCommentCount}</span>
                                <div class="line"></div>
                            </div>
                            <div class="btn chang">
                                <span class="txt">玩家长评</span>
                                <span class="number changNum">${changAllCommentCount}</span>
                                  <div class="line"></div>
                            </div>
                        </div>
                        <div class="right">
                            <div class="write_comment">
                                <img class="icon" src="https://image.gamersky.com/webimg13/www/newPc/ku/guanzhu-bai.svg">
                                <span class="txt">写短评</span>
                            </div>
                        </div>
                    </div>
                    <div class="bottom">
                        <div class="left">
                            <div class="l1 duan_l1">
                                <div class="cur" data-id="">全部</div>
                                <div data-id="20">好评</div>
                                <div data-id="21">差评</div>
                            </div>
                             <div class="l1 chang_l1">
                                <div class="cur">全部</div>
                                <div>热门</div>
                            </div>
                            <div class="l2" data-id="10">
                                <img class="radio" isclick="false" src="https://image.gamersky.com/webimg13/radio.svg">
                                <span>筛选玩过认证玩家</span>
                                <img class="rz" src="https://image.gamersky.com/webimg13/www/newPc/ku/zongping.svg">
                            </div>
                        </div>
                        <div class="right">
                            <div class="r1">
                                <img src="https://image.gamersky.com/webimg13/www/newPc/ku/sort.svg">
                                <span>推荐</span>
                            </div>
                             <div class="r1_chang">
                                <img src="https://image.gamersky.com/webimg13/www/newPc/ku/sort.svg">
                                <span>推荐</span>
                            </div>
                            <div class="r2">
                                <div class="iconBox">
                                    <img src="https://image.gamersky.com/webimg13/www/newPc/ku/filter-bai.svg">
                                    <i class="yuan"></i>
                                </div>
                                <span>更多筛选</span>
                                
                            </div>
                        </div>
                    </div>
                </div>
                `
            htm +=
                `
                    <div class="commentList"></div>
                    <div id="pagination"></div>
                `


            $(Mid_comment).append(htm)
            $('#Mid_comment .comment_header .btn').click(function () {


                $(this).addClass('cur')
                $(this).siblings().removeClass('cur')
                $(this).find('.line').css('opacity', 1)
                $(this).siblings().find('.line').css('opacity', 0)
                $('.write_comment .txt').text($(this).text().indexOf('短评') != -1 ? '写短评' : '写长评')

                if ($(this).text().indexOf('短评') != -1) {
                    $('#Mid_comment .comment_header .bottom .right .r1').css('display', 'flex')
                    $('#Mid_comment .comment_header .bottom .right .r1_chang').css('display', 'none')
                } else {
                    $('#Mid_comment .comment_header .bottom .right .r1').css('display', 'none')
                    $('#Mid_comment .comment_header .bottom .right .r1_chang').css('display', 'flex')
                }
                $('#sort').hide()
                $('#filter').hide()
                $('#chang_sort').hide()

                commentList = []
                let loading =
                    `
                        <div class="comment_loading">
                            <img src="https://image.gamersky.com/webimg13/loading.svg">
                        </div>
                    `
                $(Mid_comment).find('.commentList').html(loading)
                $('#pagination').html('')

                pageIndex = 1
                pageSize = 20

                $.fn.getCommentList($(this).text().indexOf('短评') != -1)



                if ($(this).text().indexOf('短评') == -1) {
                    $('#Mid_comment .comment_header .bottom .right .r2').hide()
                    $('#Mid_comment .comment_header .bottom .left .l2').hide()
                    $('#Mid_comment .comment_header .bottom .left .duan_l1').hide()
                    $('#Mid_comment .comment_header .bottom .left .chang_l1').css('display', 'flex')

                } else {
                    $('#Mid_comment .comment_header .bottom .right .r2').show()
                    $('#Mid_comment .comment_header .bottom .left .l2').show()
                    $('#Mid_comment .comment_header .bottom .left .duan_l1').show()
                    $('#Mid_comment .comment_header .bottom .left .chang_l1').css('display', 'none')
                }


            })
            let sort =
                `<div id="sort">
                    <ul>
                        <li class="cur" data-order="tuiJian">推荐</li>
                        <li data-order="faBuShiJianJiangXu">最新</li>
                        <li data-order="zuiZao">最早</li>
                    </ul>    
                </div>`
            let chang_sort =
                `<div id="chang_sort">
                    <ul>
                        <li class="cur" data-order="tuiJian">推荐</li>
                        <li data-order="zuiXinFaBu">最新发布</li>
                        <li data-order="zuiXinHuiFu">最新回复</li>
                    </ul>    
                </div>`

            let filter =
                `<div id="filter">
                    <div class="ul">
                       <div class="filter_pt">
                             <span class="txt">平台</span>
                             <div class="filter_pt_btn"></div>
                       </div>
                       <div class="filter_bj">
                            <span class="txt">标记</span>
                             <div class="filter_bj_btn">
                                <div  data-id="11">想玩</div>
                                <div  data-id="12">玩过</div>
                            </div>
                       </div>
                    </div>    
                </div>`
            $('#Mid_comment .comment_header .bottom .right .r1').append(sort)
            $('#Mid_comment .comment_header .bottom .right .r1_chang').append(chang_sort)
            $('#Mid_comment .comment_header .bottom .right .r2').append(filter)

            let explain_comment =
                `
                <div id="explain_comment">
                    <div class="box">
                         <div class="l">
                            <div class="title">
                                <img src="https://image.gamersky.com/webimg13/www/newPc/ku/zongping.svg">
                                <span>玩过认证玩家说明</span>
                            </div>
                            <div class="txt">
                                <p>
                                    绑定了游戏账号（比如Steam、PSN），并且拥有这款游戏的用户会显示此标识；
                                </p>
                                <p>
                                    如果您通过共享或者是购买后又退款等形式拥有过此游戏，仍然算作玩过认证玩家!
                                </p>
                            </div>
                        </div>
                        <div class="r">
                            <img class="ewm" src="http://image.gamersky.com/webimg13/ewm.png">
                            <span>用游民App认证玩过玩家</span>
                        </div>
                    </div>
                    
                </div>
            `
            $('#Mid_comment .comment_header .bottom .left .l2').append(explain_comment)


            $('#Mid_comment .comment_header .bottom .right .r1').mouseenter(function (e) {
                e.stopPropagation()
                $('#chang_sort').hide()
                $('#sort').show()
                $('#filter').hide()
            })

            $('#Mid_comment .comment_header .bottom .right .r1_chang').mouseenter(function (e) {
                e.stopPropagation()
                $('#chang_sort').show()
                $('#sort').hide()
                $('#filter').hide()

            })

            $('#sort').mouseleave(function () {
                $(this).hide()
            })
            $('#chang_sort').mouseleave(function () {
                $(this).hide()
            })
            $('#filter').mouseleave(function () {
                $(this).hide()
            })

            $('#Mid_comment .comment_header .bottom').mouseleave(function () {
                $('#chang_sort').hide()
                $('#sort').hide()
                $('#filter').hide()
            })

            $('#sort ul li').click(function (e) {
                e.stopPropagation()
                order = $(this).attr('data-order')
                $('#Mid_comment .comment_header .bottom .right .r1 span').text($(this).text())
                $(this).addClass('cur')
                $(this).siblings().removeClass('cur')
                pageIndex = 1
                $.fn.getCommentList(true)
                $('#sort').hide()
            })
            $('#chang_sort ul li').click(function (e) {
                e.stopPropagation()
                c_order = $(this).attr('data-order')
                $('#Mid_comment .comment_header .bottom .right .r1_chang span').text($(this).text())
                $(this).addClass('cur')
                $(this).siblings().removeClass('cur')
                pageIndex = 1
                $.fn.getCommentList(false)
                $('#chang_sort').hide()
            })

            $('#Mid_comment .comment_header .bottom .left .l1 div').click(function () {
                $(this).addClass('cur')
                $(this).siblings().removeClass('cur')

                if (!$(this).attr('data-id')) {
                    $(this).siblings().each((index, item) => {
                        if (userLabelTypes.includes($(item).attr('data-id'))) {
                            let index = userLabelTypes.indexOf($(item).attr('data-id'));
                            userLabelTypes.splice(index, 1);
                        }
                    })
                } else {
                    if (userLabelTypes.includes($(this).attr('data-id'))) {
                        // let index = userLabelTypes.indexOf($(this).attr('data-id'));
                        // userLabelTypes.splice(index, 1);
                    } else {
                        userLabelTypes.push($(this).attr('data-id'));
                        $(this).siblings().each((index, item) => {
                            if (userLabelTypes.includes($(item).attr('data-id'))) {
                                let i = userLabelTypes.indexOf($(item).attr('data-id'));
                                userLabelTypes.splice(i, 1);
                            }
                        })
                    }
                }
                pageIndex = 1
                $.fn.getCommentList(true)
            })


            $('#Mid_comment .comment_header .bottom .right .r2').mouseenter(function (e) {
                e.stopPropagation()
                let ptFilter = ``
                platforms.forEach(item => {
                    ptFilter +=
                        `
                     <div>${item}</div>
                    `
                })


                $('#filter .filter_pt .filter_pt_btn').html(ptFilter)


                $('#filter .filter_pt .filter_pt_btn div').each(function () {
                    var text = $(this).text();  // 获取文本内容，避免重复查询
                    if (currentPlatform == text) {
                        $(this).addClass('cur');
                    }

                    // 根据文本内容设置对应的属性和类
                    switch (text) {
                        case '全部':
                            $(this).attr('data-id', '').addClass('');  // 清除所有类
                            break;
                        case 'PC':
                            $(this).attr('data-id', '31').addClass('PC');
                            break;
                        case 'PS':
                            $(this).attr('data-id', '40').addClass('PS');
                            break;
                        case 'Xbox':
                            $(this).attr('data-id', '41').addClass('Xbox');
                            break;
                        default:
                            break;
                    }
                });

                $('#filter').show()
                $('#sort').hide()


                $('#filter .filter_pt .filter_pt_btn div').click(function () {
                    $(this).addClass('cur')
                    if (isGamePublished) {
                        if ($(this).text() != '全部' || $('#filter .filter_bj .filter_bj_btn .cur').text() != '玩过') {
                            $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').show()
                        } else {
                            $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').hide()
                        }
                    } else {
                        if ($(this).text() != '全部' || $('#filter .filter_bj .filter_bj_btn .cur').text() != '想玩') {
                            $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').show()
                        } else {
                            $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').hide()
                        }
                    }


                    currentPlatform = $(this).text()
                    $(this).siblings().removeClass('cur')
                    if (!$(this).attr('data-id')) {
                        $(this).siblings().each((index, item) => {
                            if (userLabelTypes.includes($(item).attr('data-id'))) {
                                let index = userLabelTypes.indexOf($(item).attr('data-id'));
                                userLabelTypes.splice(index, 1);
                            }
                        })
                    } else {
                        if (userLabelTypes.includes($(this).attr('data-id'))) {
                            // let index = userLabelTypes.indexOf($(this).attr('data-id'));
                            // userLabelTypes.splice(index, 1);
                        } else {
                            userLabelTypes.push($(this).attr('data-id'));
                            $(this).siblings().each((index, item) => {
                                if (userLabelTypes.includes($(item).attr('data-id'))) {
                                    let i = userLabelTypes.indexOf($(item).attr('data-id'));
                                    userLabelTypes.splice(i, 1);
                                }
                            })
                        }
                    }
                    pageIndex = 1
                    $.fn.getCommentList(true)
                    $('#filter').hide()
                })

            })

            $('#filter .filter_bj .filter_bj_btn div').click(function () {

                $(this).addClass('cur')
                $(this).siblings().removeClass('cur')
                if (userLabelTypes.includes($(this).attr('data-id'))) {
                    // let index = userLabelTypes.indexOf($(this).attr('data-id'));
                    // userLabelTypes.splice(index, 1);
                } else {
                    userLabelTypes.push($(this).attr('data-id'));
                    $(this).siblings().each((index, item) => {
                        if (userLabelTypes.includes($(item).attr('data-id'))) {
                            let i = userLabelTypes.indexOf($(item).attr('data-id'));
                            userLabelTypes.splice(i, 1);
                        }
                    })
                }
                if (isGamePublished) {
                    if ($(this).text() != '玩过' || $('#filter .filter_pt .filter_pt_btn .cur').text() != '全部') {
                        $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').show()
                    } else {
                        $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').hide()
                    }
                } else {
                    if ($(this).text() != '想玩' || $('#filter .filter_pt .filter_pt_btn .cur').text() != '全部') {
                        $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').show()
                    } else {
                        $('#Mid_comment .comment_header .bottom .right .r2 .iconBox .yuan').hide()
                    }
                }


                pageIndex = 1
                $.fn.getCommentList(true)
            })

            $('#Mid_comment .comment_header .bottom .left .l2').mouseenter(function () {

                $('#explain_comment').show()

            })
            $('#Mid_comment .comment_header .bottom .left').mouseleave(function () {
                $('#explain_comment').hide()
            })

            $('#Mid_comment .comment_header .bottom .left .l2').click(function () {
                let isclick = $(this).find('.radio').attr('isclick') == 'false' ? false : true
                $(this).find('.radio').attr('isclick', !isclick)
                isclick ? $(this).find('.radio').attr('src', 'https://image.gamersky.com/webimg13/radio.svg') : $(this).find('.radio').attr('src', 'https://image.gamersky.com/webimg13/radio_cur.svg')

                if (userLabelTypes.includes($(this).attr('data-id'))) {
                    let index = userLabelTypes.indexOf($(this).attr('data-id'));
                    userLabelTypes.splice(index, 1);
                } else {
                    userLabelTypes.push($(this).attr('data-id'));
                }
                pageIndex = 1
                $.fn.getCommentList(true)
            })

            $('#Mid_comment .comment_header .top .right .write_comment').click(function () {
                if ($(this).find('.txt').text() == '写短评') {


                    if ($('#ym_zhongping .zp_bottom .r').attr('iswantplay') == 'true') {
                        $('#ym_zhongping .zp_bottom .r .wantplay').click()
                    } else {
                        $('#ym_zhongping .zp_bottom .r .yiwanguo').click()
                    }

                    $.ajax({
                        type: "GET",
                        dataType: "jsonp",
                        url: "//cm1.gamersky.com/api/exists",
                        data: { "GenneralId": gameid },
                        success: function (response) {
                            if (response.status != 'err') {
                                $("#textarea-code").html(response.content);
                                $('#textarea .pt .ptList .ptItem').each(function () {
                                    if ($(this).find('span').text().trim() == response.platform.trim()) {
                                        $(this).addClass('cur');
                                    }
                                });
                                if ($('#ym_zhongping .zp_bottom .r').attr('iswantplay') == 'true') {
                                    $('#textarea .header .btn div:first-child').addClass('cur')
                                    $('#textarea .header .btn div:last-child').removeClass('cur')
                                } else {
                                    $('#textarea .header .btn div:last-child').addClass('cur')
                                    $('#textarea .header .btn div:first-child').removeClass('cur')
                                }
                            }
                        }
                    });
                    $.ajax({
                        type: "GET",
                        dataType: "jsonp",
                        url: "//cm1.gamersky.com/apirating/getuserrating",
                        data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Type": "0" }) },
                        success: function (data) {

                            if (data.status == "ok") {
                                var sorce = data.sorce;
                                currentScore = data.sorce;
                                var i = parseInt(sorce / 2);


                                Array.from($('#textarea .score .star div')).forEach((item, index) => {
                                    if (index <= i - 1) {
                                        $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                                    }
                                })
                            }
                        }
                    });
                } else {
                    let a = document.createElement("a")
                    a.href = "https://club.gamersky.com/editor/EditorPageInPC/index.html?type=faTie&topicId=" + $('#Mid_comment .comment_header .top .left .chang').attr('reviewSubjectId');
                    a.target = '_blank';
                    a.click();

                }
            })

            $.ajax({
                type: 'get',
                dataType: 'json',
                contentType: 'application/json',
                url: '//router.gamersky.com/@/gameScoreDetailPage/scoreInfo/6.0.0/' + userId + "/App_iOS",
                data: {
                    gameId: gameid,
                    platform: '全部'
                },
                success: function (res) {
                    isGamePublished = res.isGamePublished
                    if (pageIndex == 1) {
                        if (isGamePublished) {
                            userLabelTypes = ['12']
                            $('#filter .filter_bj .filter_bj_btn div:last-child').addClass('cur')
                            $('#filter .filter_bj .filter_bj_btn div:last-child').siblings().removeClass('cur')
                        } else {
                            userLabelTypes = ['11']
                            $('#filter .filter_bj .filter_bj_btn div:first-child').addClass('cur')
                            $('#filter .filter_bj .filter_bj_btn div:first-child').siblings().removeClass('cur')
                        }
                    }
                }
            })



            $.fn.getCommentList(true)
        },

        creatComment(e) {
            $('.commentList').html('')
            var htm = ``
            if (e) {

                if (duanAllCommentCount > 0) {
                    commentList.forEach(item => {

                        if (item.type == 'pingLun_YouXiDuanPing') {
                            htm +=
                                `
                            <div class="comment" contentUrl="${item.contentUrl}">
                                <div class="userInfo">
                                    <div class="tx" userId="${item.commentInfo.user.id}">
                                        <img src="${item.commentInfo.user.headImageUrl}">
                                    </div>
                                    <div class="info">
                                        <div class="name" userId="${item.commentInfo.user.id}">
                                            <span>${item.commentInfo.user.name}</span>
                                            <div class="commontHeadImagevip" commontHeadImagevip="${item.commentInfo.user && item.commentInfo.user.userGroupIconUrl ? item.commentInfo.user.userGroupIconUrl : ''}">
                                                <div class="commontHeadImagevip_tip">游民原创作者</div>
                                            </div>
                                            <div class="isRealPlayer" isRealPlayer="${item.commentInfo.user.isRealPlayer}">
                                                <div class="isRealPlayer_tip">
                                                    <div class="t">
                                                        <img src="https://image.gamersky.com/app/icons6.0/isRealPlayer.png">
                                                        <span>玩过认证</span>
                                                    </div>
                                                    <div class="b">
                                                        绑过游戏平台后得以认证
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="lv">Lv${item.commentInfo.user.level}</div>
                                        </div>
                                        <div class="pf">
                                            <div class="starBox">
                                                <div class="star" data-score="2" contentScore="${item.commentInfo.contentScore}"></div>
                                                <div class="star" data-score="4" contentScore="${item.commentInfo.contentScore}"></div>
                                                <div class="star" data-score="6" contentScore="${item.commentInfo.contentScore}"></div>
                                                <div class="star" data-score="8" contentScore="${item.commentInfo.contentScore}"></div>
                                                <div class="star" data-score="10" contentScore="${item.commentInfo.contentScore}"></div>
                                            </div>
                                            <div class="pt">${item.commentInfo.gameLabelCaption}</div>
                                            <div class="time">${item.commentInfo.publishTimeCaption}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="content" commentId="${item.commentInfo.id}">
                                    <div class="user-select">${item.commentInfo.contentInHtml}</div>
                                </div>
                                <div class="content_mode"><span class="zhankai">展开查看全部<img src="https://image.gamersky.com/webimg13/www/newPc/ku/arrow-down.svg"></span></div>
                                <div class="bottom_cz"> 
                                    <div class="cz_l">
                                        <div class="zan btn">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/zan.svg">
                                            <div class="txt">有用</div>
                                            <div class="num">${item.commentInfo.praisesCount}</div>  
                                        </div>
                                        <div class="cai btn">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/cai.svg">
                                            <div class="txt">没有</div>
                                            <div class="num">${item.commentInfo.treadsCount}</div>
                                        </div>
                                        <div class="huanle btn">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/like.svg">
                                            <div class="txt">欢乐</div>
                                            <div class="num">${item.commentInfo.happyCount}</div>
                                        </div>
                                        <div class="report-btn report btn" cmtid="${item.commentInfo.id}" data-userid="${item.commentInfo.user.id}" data-contenttype="zhongPing">
                                            <img src="https://image.gamersky.com/webimg13/jubao.svg">
                                            <span class="txt">举报</span>
                                        </div>
                                    </div>
                                    <div class="cz_r">
                                        <div class="tl btn" danyeId="${item.danyeId}">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/comment.svg">
                                            <div class="txt">讨论</div>
                                            <div class="num">${item.commentInfo.repliesCount}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                        }


                    });
                } else {
                    htm += `
                        <div class="kong">
                            <img src="https://image.gamersky.com/webimg13/image_Wuneirong.png"></img>
                            <div>暂无相关内容<div>
                        </div>
                    `
                    $('#pagination').hide()

                }


            } else {

                if (changAllCommentCount > 0) {
                    commentList.forEach(item => {
                        if (item.type == 'tieZi') {
                            htm +=
                                `
                            <div class="comment" contentUrl="${item.contentUrl}">
                                <div class="userInfo">
                                    <div class="tx" userId="${item.commentInfo.user.id}">
                                        <img src="${item.commentInfo.user.headImageUrl}">
                                    </div>
                                    <div class="info">
                                        <div class="name" userId="${item.commentInfo.user.id}">
                                            <span>${item.commentInfo.user.name}</span>
                                         `
                            if (item.commentInfo.user.userGroupIconUrl) {
                                htm +=
                                    `       
                                            <div class="userGroupIconUrl">
                                                <img src="${item.commentInfo.user.userGroupIconUrl}">
                                                 <div class="userGroupIconUrl_tip">知名游戏博主</div>
                                            </div>
                                        `
                            }

                            htm +=
                                `
                                            <div class="lv">Lv${item.commentInfo.user.level}</div>
                                        </div>
                                       
                                        <div class="pf">
                                            <div class="time">${item.commentInfo.publishTimeCaption}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="content_title">${item.commentInfo.title}</div>
                                <div class="content" src="${item.commentInfo.sourceUrl}">
                                    ${item.commentInfo.contentInHtml}
                                       `

                            if (item.commentInfo.imageInfes && item.commentInfo.imageInfes.length > 0) {

                                htm +=
                                    `    
                                <div class="imgList">
                                `
                                if (item.commentInfo.imageInfes.length > 3) {
                                    htm +=
                                        `
                                        <span class="num">共${item.commentInfo.imageInfes.length}张图</span>
                                    `
                                }

                                item.commentInfo.imageInfes.forEach((v, i) => {
                                    if (i <= 2) {
                                        htm +=

                                            `
                                        <div class="imgItem">
                                            <img src="${v.contentImageUrl}">
                                            
                                        `
                                        if (v.imageTags && v.imageTags.length > 0) {
                                            if (v.imageTags[0] == '长图') {
                                                htm +=
                                                    `
                                                    <i class="changtu"></i>
                                                `
                                            } else if (v.imageTags[0] == 'gif') {
                                                htm +=
                                                    `
                                                    <i class="gif"></i>
                                                `
                                            }

                                        }

                                        htm +=
                                            `
                                        </div>
                                        `
                                    }

                                })
                                htm +=
                                    `  </div>
                                `
                            }

                            htm +=
                                `  
                                </div>
                                <div class="bottom_cz"> 
                                    <div class="cz_l">
                                    `
                            if (item.postInfo.clubName) {
                                htm +=
                                    ` 
                                            <div class="club btn"  clubId="${item.clubInfo.id}">
                                                <img src="https://image.gamersky.com/webimg15/user/club2/pc/icon_SheQu.png">
                                                <div class="txt">${item.postInfo.clubName}</div>
                                                 <img src="https://image.gamersky.com/webimg13/www/newPc/ku/arrow-down.svg">
                                            </div>
                                        `
                            }

                            htm +=
                                ` 
                                    </div>
                                    <div class="cz_r">
                                        <div class="zan btn">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/zan.svg">
                                            <div class="txt">点赞</div>
                                            <div class="num">${item.commentInfo.praisesCount}</div>  
                                        </div>
                                        <div class="tl btn" src="${item.commentInfo.sourceUrl}">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/comment.svg">
                                            <div class="txt">讨论</div>
                                            <div class="num">${item.commentInfo.repliesCount}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                        }

                    });
                } else {
                    htm += `
                        <div class="kong">
                            <img src="https://image.gamersky.com/webimg13/image_Wuneirong.png"></img>
                            <div>暂无相关内容<div>
                        </div>
                    `
                    $('#pagination').hide()
                }

            }
            $('.commentList').html(htm)



            $('#Mid_comment .comment .userInfo .info .name .lv').each(function () {
                let lv = Number($(this).text().split('Lv')[1]);
                if (lv <= 9) {
                    $(this).css('background', '#F5F7FA');
                    $(this).css('color', '#999');
                } else if (lv > 9 && lv <= 19) {
                    $(this).css('background', 'rgba(255, 162, 11, 0.05)');
                    $(this).css('color', '#FF9F1D');
                } else if (lv > 19 && lv <= 29) {
                    $(this).css('background', 'rgba(255, 51, 0, 0.05)');
                    $(this).css('color', '#FF3300');
                } else if (lv > 29) {
                    $(this).css('background', '#444444');
                    $(this).css('color', '#FFC117');
                }
            });

            $('.isRealPlayer').each(function () {
                $(this).attr('isRealPlayer') == 'true' ? $(this).show() : $(this).hide();
            });

            $('.commontHeadImagevip').each(function () {
                // $(this).attr('commontHeadImagevip') !== 'undefined' ? $(this).show() : $(this).hide();
                if ($(this).attr('commontHeadImagevip') != 'undefined' && $(this).attr('commontHeadImagevip') != '') {
                    $(this).css('background', `url(${$(this).attr('commontHeadImagevip')}) no-repeat 50% / 16px`);
                    $(this).show()
                } else {
                    $(this).hide()
                }

            });

            $('#Mid_comment .comment .userInfo .pf .starBox .star').each(function () {
                if (Number($(this).attr('data-score')) <= Number($(this).attr('contentScore'))) {
                    $(this).addClass('starCur');
                }
            });

            $('.commontHeadImagevip').on('mouseenter', function () {
                $(this).find('.commontHeadImagevip_tip').show()
            })
            $('.commontHeadImagevip').on('mouseleave', function () {
                $(this).find('.commontHeadImagevip_tip').hide()
            })
            $('.isRealPlayer').on('mouseenter', function () {
                $(this).find('.isRealPlayer_tip').show()

            })
            $('.isRealPlayer').on('mouseleave', function () {
                $(this).find('.isRealPlayer_tip').hide()
            })

            $('.userGroupIconUrl').on('mouseenter', function () {
                $(this).find('.userGroupIconUrl_tip').show()
            })
            $('.userGroupIconUrl').on('mouseleave', function () {
                $(this).find('.userGroupIconUrl_tip').hide()
            })


            $('#Mid_comment .comment').on('mouseenter', function () {
                $(this).find('.report-btn').css('display', 'flex')
            })

            $('#Mid_comment .comment').on('mouseleave', function () {
                $(this).find('.report-btn').hide()
            })

            $('#Mid_comment .comment .bottom_cz .cz_l .btn, #Mid_comment .comment .bottom_cz .cz_r .zan').click(function () {
                let contentUrl = $(this).closest('.comment').attr('contentUrl')
                if ($(this)[0].className.indexOf('zan') != -1) {
                    praiseContent($(this), contentUrl)
                } else if ($(this)[0].className.indexOf('cai') != -1) {
                    treadContent($(this), contentUrl)
                } else if ($(this)[0].className.indexOf('huanle') != -1) {
                    huanleComment($(this), contentUrl)
                }

            })

            // $('#Mid_comment .comment .bottom_cz .cz_l .report-btn').click(function () {
            //     if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
            //         // $("body").append(PLhtml.login_layer(''));
            //         // console.log(PLhtml.login_layer(''));

            //         // $('.QZshade').show()
            //         // $('.QZlogin').show()

            //         $.fn.UserOnline(() => { });
            //         return false;
            //     }

            //     var $this = $(this);
            //     if ($("script[src$='ajaxfileupload.js']").length == 0) {
            //         //举报插件
            //         if ($("#ajaxfileupload_js").length == 0) {
            //             var reportjs = document.createElement("script");
            //             reportjs.id = 'ajaxfileupload_js'; reportjs.src = '//j.gamersky.com/file/ajaxfileupload.js';
            //             document.body.appendChild(reportjs);
            //         }

            //     }
            //     console.log($this);

            //     $this.UserOnline(function () {
            //         $this.reportfileupload($this);
            //     });

            // })


            $('#Mid_comment .comment .content').each(function () {
                if ($(this)[0].clientHeight <= 200) {
                    $(this).siblings('.content_mode').hide();
                }
            });


            $('#Mid_comment .comment .content_mode').click(function () {
                $(this).hide()
                $(this).siblings('.content').css('max-height', '100%')
            })
            $('#Mid_comment .comment .bottom_cz .cz_l .club').click(function () {
                let a = document.createElement("a")
                a.href = "https://club.gamersky.com/forum/" + $(this).attr('clubId');
                a.target = '_blank';
                a.click();
            })

            $('#Mid_comment .comment .bottom_cz .cz_r .tl, #Mid_comment .comment .content').click(function () {
                if ($('#Mid_comment .comment_header .top .left .cur .txt').text() == '玩家短评') {
                    if ($(this).attr('danyeId') && $(this).attr('danyeId') != 'undefined' && $(this).attr('danyeId') != 0) {
                        let a = document.createElement("a")
                        a.href = "http://ku.gamersky.com/activity/n/" + $(this).attr('danyeId');
                        a.target = '_blank';
                        a.click();
                    }

                } else {
                    let a = document.createElement("a")
                    a.href = $(this).attr('src');
                    a.target = '_blank';
                    a.click();
                }
            })

            $('#Mid_comment .comment .userInfo .tx, #Mid_comment .comment .userInfo .info .name').click(function () {
                let a = document.createElement("a")
                a.href = 'https://i.gamersky.com/u/' + $(this).attr('userId');
                a.target = '_blank';
                a.click();
            })

            $('#Mid_comment .comment .userInfo .info .name .lv').click(function () {
                let a = document.createElement("a")
                a.href = 'https://i.gamersky.com/my/level';
                a.target = '_blank';
                a.click();
            })

        },
    }
    commentHtml.getCommentHeader()


    // document.body.addEventListener('click', function () {
    //     $('#filter').hide()
    //     $('#sort').hide()
    //     $('#chang_sort').hide()
    // })

    // 分页功能
    function createPagination(totalPages, currentPage) {
        const $paginationContainer = $('#pagination');
        $paginationContainer.empty();  // 清空容器
        // 创建页码按钮
        function createPageButton(pageNumber, isDisabled = false, isActive = false) {
            const $button = $('<div></div>', {
                text: pageNumber,
                class: 'page-btn',
                click: function () {
                    if (!isDisabled) {
                        createPagination(totalPages, pageNumber);  // 更新分页
                        pageIndex = pageNumber;
                        let loading =
                            `
                                <div class="comment_loading">
                                    <img src="https://image.gamersky.com/webimg13/loading.svg">
                                </div>
                            `
                        $(Mid_comment).find('.commentList').html(loading)
                        if ($('#Mid_comment .comment_header .top .left .cur .txt').text() == "玩家短评") {
                            $.fn.getCommentList(true)
                        } else {
                            $.fn.getCommentList(false);
                        }
                    }
                }
            });
            if (isDisabled) {
                $button.addClass('disabled');
            }
            if (isActive) {
                $button.addClass('active');
            }
            return $button;
        }

        if (totalPages === 1) {
            $paginationContainer.append(createPageButton(1, true, true));
            return;  // 只显示一页时不需要进一步处理分页
        }

        // 上一页按钮
        const $prevButton = $('<div></div>', {
            text: '上一页',
            class: 'page-btn prev-btn',
            click: function () {
                if (currentPage > 1) {
                    createPagination(totalPages, currentPage - 1);  // 更新分页到上一页
                    pageIndex = pageIndex - 1;
                    let loading =
                        `
                        <div class="comment_loading">
                            <img src="https://image.gamersky.com/webimg13/loading.svg">
                        </div>
                    `
                    $(Mid_comment).find('.commentList').html(loading)
                    if ($('#Mid_comment .comment_header .top .left .cur .txt').text() == "玩家短评") {
                        $.fn.getCommentList(true)
                    } else {
                        $.fn.getCommentList(false);
                    }
                }
            }
        });
        // 下一页按钮
        const $nextButton = $('<div></div>', {
            text: '下一页',
            class: 'page-btn next-btn',
            click: function () {
                if (currentPage < totalPages) {
                    createPagination(totalPages, currentPage + 1);  // 更新分页到下一页
                    pageIndex = pageIndex + 1;
                    let loading =
                        `
                        <div class="comment_loading">
                            <img src="https://image.gamersky.com/webimg13/loading.svg">
                        </div>
                    `
                    $(Mid_comment).find('.commentList').html(loading)
                    if ($('#Mid_comment .comment_header .top .left .cur .txt').text() == "玩家短评") {
                        $.fn.getCommentList(true)
                    } else {
                        $.fn.getCommentList(false);
                    }
                }
            }
        });
        // 添加上一页按钮
        if (currentPage > 1) {
            $paginationContainer.append($prevButton);
        } else {
            $prevButton.hide();
            $paginationContainer.append($prevButton);
        }
        // 首页
        $paginationContainer.append(createPageButton(1, currentPage === 1, currentPage === 1));
        // 如果当前页大于 3，显示省略号
        if (currentPage > 3) {
            $paginationContainer.append(createPageButton('...', true));
        }
        // 页码 2 到 4 页
        const start = Math.max(2, currentPage - 2);
        const end = Math.min(totalPages - 1, currentPage + 2);
        for (let i = start; i <= end; i++) {
            $paginationContainer.append(createPageButton(i, false, i === currentPage));
        }
        // 如果当前页小于 totalPages - 2，显示省略号
        if (currentPage < totalPages - 2) {
            $paginationContainer.append(createPageButton('...', true));
        }
        // 最后一页
        $paginationContainer.append(createPageButton(totalPages, currentPage === totalPages, currentPage === totalPages));
        // 添加下一页按钮
        if (currentPage < totalPages) {
            $paginationContainer.append($nextButton);
        } else {
            $nextButton.hide();
            $paginationContainer.append($nextButton);
        }
    }

    function praiseContent(item, contentUrl) {
        if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
            // $("body").append(PLhtml.login_layer(''));
            // console.log(PLhtml.login_layer(''));

            // $('.QZshade').show()
            // $('.QZlogin').show()

            $.fn.UserOnline(() => { });
            return false;
        }


        if (!item.attr('cur')) {
            let that = this
            $.ajax({
                type: "get",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/userData/praiseContent/6.0.0/" + userId,
                data: {
                    contentUrl: contentUrl
                },
                xhrFields: {
                    withCredentials: true
                },
                success: function (res) {
                    item.attr('cur', true)
                    item.find('.num').text(Number(item.find('.num').text()) + 1)
                    item.find('img').attr('src', 'https://image.gamersky.com/webimg13/zan_cur.svg')
                    if (item.siblings('.cai').attr('cur')) {
                        item.siblings('.cai').find('.num').text(Number(item.siblings('.cai').find('.num').text()) - 1)
                        item.siblings('.cai').find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/cai.svg')
                        item.siblings('.cai').removeAttr('cur')
                    }
                    if (item.siblings('.huanle').attr('cur')) {
                        item.siblings('.huanle').find('.num').text(Number(item.siblings('.huanle').find('.num').text()) - 1)
                        item.siblings('.huanle').find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/like.svg')
                        item.siblings('.huanle').removeAttr('cur')
                    }

                }
            })
        } else {
            let that = this
            $.ajax({
                type: "get",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/userData/cancelPraiseContent/6.0.0/" + userId,

                data: {
                    contentUrl: contentUrl
                },
                success: function (res) {
                    item.removeAttr('cur')
                    item.find('.num').text(Number(item.find('.num').text()) - 1)
                    item.find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/zan.svg')
                }
            })
        }
    }

    function treadContent(item, contentUrl) {
        if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
            // $("body").append(PLhtml.login_layer(''));
            // console.log(PLhtml.login_layer(''));

            // $('.QZshade').show()
            // $('.QZlogin').show()

            $.fn.UserOnline(() => { });
            return false;
        }

        if (!item.attr('cur')) {
            $.ajax({
                type: "get",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/userData/treadContent/6.0.0/" + userId,
                xhrFields: {
                    withCredentials: true
                },
                data: {
                    contentUrl: contentUrl
                },
                success: function (res) {
                    item.attr('cur', true)
                    item.find('.num').text(Number(item.find('.num').text()) + 1)
                    item.find('img').attr('src', 'https://image.gamersky.com/webimg13/cai_cur.svg')
                    if (item.siblings('.zan').attr('cur')) {
                        item.siblings('.zan').find('.num').text(Number(item.siblings('.zan').find('.num').text()) - 1)
                        item.siblings('.zan').find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/zan.svg')
                        item.siblings('.zan').removeAttr('cur')
                    }
                    if (item.siblings('.huanle').attr('cur')) {
                        item.siblings('.huanle').find('.num').text(Number(item.siblings('.huanle').find('.num').text()) - 1)
                        item.siblings('.huanle').find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/like.svg')
                        item.siblings('.huanle').removeAttr('cur')
                    }

                }
            })
        } else {
            $.ajax({
                type: "get",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/userData/cancelTreadContent/6.0.0/" + userId,

                data: {
                    contentUrl: contentUrl
                },
                success: function (res) {
                    item.removeAttr('cur')
                    item.find('.num').text(Number(item.find('.num').text()) - 1)
                    item.find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/cai.svg')
                }
            })
        }
    }

    function huanleComment(item, contentUrl) {
        if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
            // $("body").append(PLhtml.login_layer(''));
            // console.log(PLhtml.login_layer(''));

            // $('.QZshade').show()
            // $('.QZlogin').show()

            $.fn.UserOnline(() => { });
            return false;
        }
        if (!item.attr('cur')) {
            $.ajax({
                type: "get",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/userData/markHappy/6.0.0/" + userId,
                xhrFields: {
                    withCredentials: true
                },
                data: {
                    contentUrl: contentUrl
                },
                success: function (res) {
                    item.attr('cur', true)
                    item.find('.num').text(Number(item.find('.num').text()) + 1)
                    item.find('img').attr('src', 'https://image.gamersky.com/webimg13/like_cur.svg')
                    if (item.siblings('.zan').attr('cur')) {
                        item.siblings('.zan').find('.num').text(Number(item.siblings('.zan').find('.num').text()) - 1)
                        item.siblings('.zan').find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/zan.svg')
                        item.siblings('.zan').removeAttr('cur')
                    }
                    if (item.siblings('.cai').attr('cur')) {
                        item.siblings('.cai').find('.num').text(Number(item.siblings('.cai').find('.num').text()) - 1)
                        item.siblings('.cai').find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/cai.svg')
                        item.siblings('.cai').removeAttr('cur')
                    }

                }
            })
        } else {
            $.ajax({
                type: "get",
                dataType: 'json',
                url: "https://router5.gamersky.com/@/userData/cancelMarkHappy/6.0.0/" + userId,

                data: {
                    contentUrl: contentUrl
                },
                success: function (res) {
                    item.removeAttr('cur')
                    item.find('.num').text(Number(item.find('.num').text()) - 1)
                    item.find('img').attr('src', 'https://image.gamersky.com/webimg13/www/newPc/ku/cai.svg')
                }
            })
        }
    }

})(jQuery);