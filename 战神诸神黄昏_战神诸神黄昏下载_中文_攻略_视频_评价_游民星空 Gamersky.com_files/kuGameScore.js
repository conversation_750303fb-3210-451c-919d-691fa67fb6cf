(function ($) {
    var protocol = location.protocol;

    function IEVersion() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串  
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器  
        var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器  
        var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                return 7;
            } else if (fIEVersion == 8) {
                return 8;
            } else if (fIEVersion == 9) {
                return 9;
            } else if (fIEVersion == 10) {
                return 10;
            } else {
                return 6;//IE版本<=7
            }
        } else if (isEdge) {
            return 'edge';//edge
        } else if (isIE11) {
            return 11; //IE11  
        } else {
            return -1;//不是ie浏览器
        }
    }
    var css = protocol + '//j.gamersky.com/web2015/v1/ku/css/kuGameScore.css?v=' + dateFtt({ f: 1 });
    // var css = protocol + './j.gamersky.com/web2015/v1/ku/css/kuGameScore.css?v=' + dateFtt({ f: 1 });
    $("<link>").attr({ rel: "stylesheet", href: css }).appendTo("head");
    var $kuGameScore = $(".kuGameScore");
    var gameid = $kuGameScore.attr("gameid"); //获取游戏ID
    var gamename = $kuGameScore.attr("gamename"); //获取游戏名
    var kuHtm = {
        LayerHtml: function (htm) {
            htm += '<div class="ku_pop_mask"></div>';
            htm += '<div class="ku_pop_layer">';
            htm += '  <div class="ku-pop-top">';
            htm += '    <div class="ku-pop-top-img"><img class="userimg" src="//image.gamersky.com/webimg15/comment/anonymous.jpg" /></div>';
            htm += '    <div class="ku-pop-top-tit">简评：<span class="gamename">' + gamename + '</span></div>';
            htm += '    <a href="javascript:;" class="ku_pop_close"></a>';
            htm += '  </div>';
            htm += '  <div class="ku-pop-select" data-click="false">';
            htm += '    <div class="ku-pop-nav">';
            htm += '      <a href="javascript:;" class="a1" data-type="7" data-click="false">想玩</a>';
            htm += '      <a href="javascript:;" class="a2" data-type="8" data-click="false">玩过</a>';
            htm += '    </div>';
            htm += '    <div class="ku-pop-con">';
            htm += '      <div class="dafen">';
            htm += '        <div class="dfen" id="layerdfen">';
            htm += '          <span class="dftit">评分：</span>';
            htm += '          <a href="javascript:;" fromdevice="2" data-txt="讨厌"></a>';
            htm += '          <a href="javascript:;" fromdevice="4" data-txt="不喜欢"></a>';
            htm += '          <a href="javascript:;" fromdevice="6" data-txt="还可以"></a>';
            htm += '          <a href="javascript:;" fromdevice="8" data-txt="很不错"></a>';
            htm += '          <a href="javascript:;" fromdevice="10" data-txt="棒极了"></a>';
            htm += '          <span class="dftxt"></span>';
            htm += '        </div>';
            htm += '      </div>';
            htm += '      <div class="fuxuan">';
            htm += '        <div class="tit">平台：</div>';
            htm += '        <div class="fxuanlist" id="fxuan"></div>';
            htm += '      </div>';
            htm += '    </div>';
            htm += '  </div>';
            htm += '  <div class="ku-pop-textarea">';
            htm += '    <div class="tit"><h4>评语：</h4></div>';
            htm += '    <div class="con">';
            htm += '      <div class="textarea-code" id="textarea-code" contenteditable="true" spellcheck="false"></div>';
            htm += '      <a href="javascript:;" class="space"></a>';
            htm += '    </div>';
            htm += '    <div class="bot">';
            htm += '      <div class="kuEditor">';
            htm += '        <a href="javascript:;" class="a1" data-role="Bold">粗体</a>';
            htm += '        <a href="javascript:;" class="a2" data-role="Underline">下划线</a>';
            htm += '        <a href="javascript:;" class="a3" data-role="Italic">斜体</a>';
            htm += '        <a href="javascript:;" class="a4" data-role="StrikeThrough">删除线</a>';
            htm += '        <a href="javascript:;" class="a5" data-role="CreateLink">隐藏文本</a>';
            htm += '      </div>';
            htm += '      <div class="kuAlert"><span class="Anum">0</span>/<span class="Bnum">348</span></div>';
            htm += '    </div>';
            htm += '  </div>';
            htm += '  <div class="ku-pop-label">';
            htm += '    <div class="title">填写标签：</div>';
            htm += '    <div class="label">';
            htm += '      <div class="label-con" tit="点击本输入框，或点选下方标签，即可添加标签">';
            htm += '        <span>点击本输入框，或点选下方标签，即可添加标签</span>';
            htm += '      </div>';
            htm += '    </div>';
            htm += '    <div class="label-my">';
            htm += '      <div class="tit">我的标签：</div>';
            htm += '      <div class="con cur"><div id="myLabel"></div></div>';
            htm += '      <a href="javascript:;" style="display:none" class="zhanbtn"></a>';
            htm += '    </div>';
            htm += '    <div class="label-used">';
            htm += '      <div class="tit">常用标签：</div>';
            htm += '      <div class="con cur"><div id="usedLabel"></div></div>';
            htm += '      <a href="javascript:;" style="display:none" class="zhanbtn"></a>';
            htm += '    </div>';
            htm += '  </div>';
            htm += '  <div class="ku-pop-btn">';
            htm += '    <div class="btn"><a href="javascript:;" class="cmt-btn" id="submitbtn" data-click="false">发布</a></div>'; //点保存后把 “正在保存...” 文字录入替换保存按钮
            htm += '    <div class="con"></div>';
            htm += '  </div>';
            htm += '</div>';
            return htm;
        },
        confirmHtml: function (htm) {
            htm += '<div class="ppp_mask"></div>';
            htm += '<div class="ppp_layer">';
            htm += '  <div class="con"><center>是否删除您的记录？<br><span>（本操作将删除您在该游戏中的所有数据）</span></center></div>';
            htm += '  <div class="btn">';
            htm += '    <a href="javascript:;" class="determinebtn" data-click="false">确定</a>';
            htm += '    <a href="javascript:;" class="cancelbtn">取消</a>';
            htm += '  </div>';
            htm += '</div>';
            return htm;
        },
        //意见反馈层
        popHtml: function (htm) {
            htm += '<div class="pop_mask"></div>';
            htm += '<div class="pop_layer">';
            htm += '  <div class="pop_top"><a href="javascript:;" class="pop_close"></a></div>';
            htm += '  <div class="pop_tit">欢迎您为游民众评提交使用感受和宝贵建议！</div>';
            htm += '  <div class="pop_con">';
            htm += '    <textarea class="pop_textarea"></textarea>';
            htm += '    <div class="pop_int">';
            htm += '      <div class="tit">QQ号：</div>';
            htm += '      <div class="int"><input type="text" name="pop_QQ" class="pop_QQ"></div>';
            htm += '    </div>';
            htm += '  </div>';
            htm += '  <div class="pop_btn"><a href="javascript:;" class="popBtn" id="popBtn">提交</a></div>';
            htm += '  <div class="pop_infor">';
            htm += '    <h3>加入游民众评种子用户群</h3>';
            htm += '    <div class="infor">';
            htm += '      <p>如果你经常逛游民，对互联网产品有深刻的认识，并且喜欢第一时间尝试新产品，愿意为我们分享你的见解和想法，恭喜你，你就是我们想要的精英用户。</p>';
            htm += '      <p>在这里你可以体验到我们最新的产品，了解到第一手产品信息动态；我们会不定期送出神秘惊喜礼物。</p>';
            htm += '    </div>';
            htm += '    <a href="//shang.qq.com/wpa/qunwpa?idkey=2107df04a4b2b8c74c7f42163e32a2e63f26403911c72c191dc6c9604156accd" target="_blank" title="游民众评种子用户群"><span>加入QQ群</span></a>';
            htm += '  </div>';
            htm += '</div>';
            return htm;
        },
        //问题弹窗
        whHtml: function (htm) {
            htm += '<div class="wh_layer">';
            htm += '<div class="wh_con">';
            htm += '  <h3>玩过认证玩家说明</h3>';
            htm += '  <p>绑定了游戏账号（比如Steam、PSN），并且拥有这款游戏的用户会显示此标识；</p>';
            htm += '  <p>如果您通过共享或者是购买后又退款等形式拥有过此游戏，仍然算作玩过认证玩家！</p>';
            htm += '</div>';
            htm += '<div class="wh_vvv"></div>';
            htm += '</div>';
            return htm;
        }
    };

    var script2 = '//j.gamersky.com/web2015/ku/js/echarts.min.js?v=' + dateFtt({ f: 1 });
    $("<script>").attr({ src: script2 }).appendTo("head");

    var script1 = '//j.gamersky.com/web2015/v1/ku/js/kuSteamRelatedInfo.js?v=' + dateFtt({ f: 1 });
    // $("<script>").attr({ src: script1 }).appendTo("head");
    // var script1 = '//project-test.gamersky.com/zhongping2_test/j.gamersky.com/web2015/ku/js/2.0/kuSteamRelatedInfo.js?v=' + dateFtt({ f: 1 });
    // var script1 = './j.gamersky.com/web2015/ku/js/2.0/kuSteamRelatedInfo.js?v=' + dateFtt({ f: 1 });
    $("<script>").attr({ src: script1 }).appendTo("head");

    // var css1 = 'style_kucontent.css?v=' + dateFtt({ f: 1 });
    // $("<link>").attr({ rel: "stylesheet", href: css1 }).appendTo("head");
    var isTrim = function (s) { if (typeof (s) == "undefined") { return false; } return s.replace(/(^\s*)|(\s*$)/g, ""); }; //清除空格
    //数组去重处理
    var dedupe = function (arr) { return arr.filter(function (item, index) { return arr.indexOf(item, 0) == index; }); }
    //json数组去重
    var dedupes = function (arr, type) { var hash = {}; return arr.reduce(function (item, next) { hash[next[type]] ? '' : hash[next[type]] = true && item.push(next); return item }, []); };
    function addThousandthSign(numStr) {
        var regForm = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g;
        return numStr.toString().replace(regForm, "$1,");
    }
    var NumW = function (n, l) { return n = String(n).length >= l ? parseFloat(parseFloat(n / 10000).toFixed(1)) + "w" : addThousandthSign(n); };
    //比较日期大小
    function judgeTime(startTime) {
        var reg = new RegExp("[\\u4E00-\\u9FFF]+");
        if (typeof (startTime) == "undefined" || reg.test(startTime)) {
            return false;
        } else {
            var add = function (n) { return n < 10 ? '0' + n : n };
            var myDate = new Date();
            var yyyy = myDate.getFullYear();
            var mm = add(myDate.getMonth() + 1);
            var dd = add(myDate.getDate());
            var endTime = yyyy + '-' + mm + "-" + dd;
            var start = new Date(startTime.replace(/-/g, '/')).getTime(); //指定时间
            var end = new Date(endTime.replace(/-/g, '/')).getTime(); //当前时间
            if (start - end > 0) {
                return false;
            }
        }
        return true;
    }
    $.fn.checkMarketTime = function () {
        return this.each(function () {
            var timeList = []
            $(this).find("a").each(function () {
                timeList.push($(this).attr("data-time"))
            });
            //加入最早玩过时间
            var TheEarliestTimeToPlay = $('.TheEarliestTimeToPlay').eq(0).html()
            if (TheEarliestTimeToPlay) {
                //处理格式
                var clData = new Date(TheEarliestTimeToPlay);
                var year = clData.getFullYear()
                var Month = clData.getMonth() + 1
                var clMoth = Month < 10 ? "0" + Month : Month
                var day = clData.getDate() < 10 ? "0" + clData.getDate() : clData.getDate()
                var downData = year + "-" + clMoth + "-" + day;
                timeList.push(downData)
            }
            $.each(timeList, function (index, item) {
                var isTime = judgeTime(item);
                if (isTime) {
                    $kuGameScore.attr("selltime", '已上市');
                    return false
                } else {
                    $kuGameScore.attr("selltime", '未上市');
                }
            })
        });
    };

    //调用初始模板
    $.fn.judgeListedState = function () {
        return this.each(function () {
            var $this = $(this), htm1 = '', htm2 = '';
            var sellTime = $this.attr("selltime");
            if (sellTime == '已上市') {
                htm1 += '<div class="wjscore-num">';
                htm1 += '<div class="num" id="scoreAvg"></div>';
                htm1 += '<div class="pin"><div class="xin"><div id="xin" class="x0"></div></div></div>';
                htm1 += '<div class="txt">玩家评分<span>(<span id="scoreTimes">0</span>人)</span></div>';
                htm1 += '</div>';
                htm1 += '<div class="wjscore-con">';
                htm1 += '<div class="li-n"><div class="num">5星</div><div class="jindu"><div class="tiao"></div></div><div class="bili">0%</div></div>';
                htm1 += '<div class="li-n"><div class="num">4星</div><div class="jindu"><div class="tiao"></div></div><div class="bili">0%</div></div>';
                htm1 += '<div class="li-n"><div class="num">3星</div><div class="jindu"><div class="tiao"></div></div><div class="bili">0%</div></div>';
                htm1 += '<div class="li-n"><div class="num">2星</div><div class="jindu"><div class="tiao"></div></div><div class="bili">0%</div></div>';
                htm1 += '<div class="li-n"><div class="num">1星</div><div class="jindu"><div class="tiao"></div></div><div class="bili">0%</div></div>';
                htm1 += '<div class="li-c Itoscore"></div>';
                htm1 += '</div>';
            } else {
                htm1 += '<div class="wjscore-qdpf">';
                htm1 += '<div class="qdnum">';
                htm1 += '<div class="num"></div>';
                htm1 += '<div class="txt">期待人数</div>';
                htm1 += '</div>';
                htm1 += '<div class="qdcon Itoscore"></div>';
                htm1 += '</div>';
            }
            htm2 += '<li class="img"><img class="userimg" src="//image.gamersky.com/webimg15/comment/anonymous.jpg" /></li>';
            htm2 += '<li class="fen">';
            htm2 += ' <div class="ftxt" style="display:none;">';
            htm2 += '  <div class="wtxt"></div>';
            htm2 += '  <div class="wbtn">';
            htm2 += '   <a href="javascript:;" class="editbtn">修改</a>';
            htm2 += '   <a href="javascript:;" class="delbtn">删除</a>';
            htm2 += '  </div>';
            htm2 += ' </div>';
            htm2 += ' <div class="fxin">';
            htm2 += '  <div class="datetime" date-sellTime="' + sellTime + '" tit="未评分" style="display:none;">';
            if (sellTime == '已上市') {
                htm2 += '<a href="javascript:;" class="tpbtn" data-i="0" data-sorce="1" data-type="7" data-click="false">想玩</a>';
                htm2 += '<a href="javascript:;" class="tpbtn" data-i="1" data-sorce="1" data-type="8" data-click="false">玩过</a>';
            } else {
                htm2 += '<a href="javascript:;" class="tpbtn not" data-i="0" data-sorce="1" data-type="7" data-click="false">期 待</a>';
            }
            htm2 += '  </div>';
            htm2 += '  <div class="dfen" id="dfen" style="display:none;">';
            htm2 += '   <span class="dftit">评分：</span>';
            htm2 += '   <a class="mybtn" href="javascript:;" fromdevice="2" data-txt="讨厌"></a>';
            htm2 += '   <a class="mybtn" href="javascript:;" fromdevice="4" data-txt="不喜欢"></a>';
            htm2 += '   <a class="mybtn" href="javascript:;" fromdevice="6" data-txt="还可以"></a>';
            htm2 += '   <a class="mybtn" href="javascript:;" fromdevice="8" data-txt="很不错"></a>';
            htm2 += '   <a class="mybtn" href="javascript:;" fromdevice="10" data-txt="棒极了"></a>';
            htm2 += '   <span class="dftxt"></span>';
            htm2 += '  </div>';
            htm2 += ' </div>';
            htm2 += ' <div class="fbtn"><a href="javascript:;" class="pybtn">写评语</a></div>';
            htm2 += '</li>';
            htm2 += '<li class="doneTxt" style="display:none;"></li>';
            htm2 += '<li class="myTag" style="display:none;"></li>';
            htm2 += '<li class="myremark"></li>';
            $(".wjscore").html(htm1);
            $(".myscore").html(htm2);

        });
    };

    $.fn.extend({
        //右侧展开评论内容
        openBtnFun: function () {
            var $this = $(this), $myremark = $this.parents(".myremark");
            var height = $myremark.find(".con").text().length;
            if (height >= 66) {
                $myremark.find(".btn").show();
            } else {
                $myremark.find(".con").css("height", "auto");
            }
            $this.off("click").on("click", function (event) {
                event.preventDefault();
                var that = $(this);
                if (that.attr("tit") == "展开") {
                    that.attr("tit", "收起").addClass("cur");
                    $myremark.find(".con").addClass("cur");
                } else {
                    that.attr("tit", "展开").removeClass("cur");
                    $myremark.find(".con").removeClass("cur");
                }
            });
        },
        //想玩和玩过btn按钮
        judgewanFun: function () {
            $.ajax({
                type: "post",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/apirating/Judge",
                data: { "Idlist": gameid },
                success: function (data) {
                    if (data.status == "ok") {
                        $.each(data.result, function (index, value) {
                            if (value.wantPlay == true) {
                                idx = 0;
                                $("#dfen").hide();
                                $(".myscore .ftxt").show().find(".wtxt").html('我想玩这个游戏');

                            }
                            if (value.played == true) {
                                idx = 1;
                                $("#dfen").show();
                                $(".myscore .ftxt").show().find(".wtxt").html('我玩过这个游戏');
                            }

                            if (value.played == false && value.wantPlay == false) {
                                var selltime = $kuGameScore.attr("selltime");
                                if (selltime == "已上市") {
                                    idx = 1;
                                } else {
                                    idx = 0;
                                }
                                $("#dfen").hide().addClass("wss");
                                $(".datetime").show();
                            } else {
                                $(".datetime").hide();
                            }
                        });
                    } else {
                        $(".datetime").show();
                    }
                }
            });
        },
        Rating: function (selltime) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/apirating/getplayersscore",
                data: { "jsondata": JSON.stringify({ genneralId: gameid, num: "10" }) },
                success: function (data) {
                    if (data.status == 'ok') {
                        var sorce = parseFloat(data.sorce), totalnumber = data.totalnumber, htm = '';
                        var timesRandom = addThousandthSign(data.timesRandom);
                        if (selltime == "已上市") {
                            if (sorce == "10.0") {
                                htm = '<i class="n1"></i><i class="nm"></i><i class="n0"></i>';
                            } else {
                                var sorceStr = sorce > 10 ? '9.9' : sorce.toString();
                                htm = '<i class="n' + sorceStr.split(".")[0] + '"></i><i class="nn"></i><i class="n' + sorceStr.split(".")[1] + '"></i>';
                            }
                            $("#scoreAvg").html(totalnumber >= 10 ? htm : "<span>人数不足</span>"); //玩家评分数
                            $("#scoreTimes").html(timesRandom); //玩家评分人数
                            $("#xin").attr("class", "x" + parseInt((sorce > 10 ? 10 : sorce) * 5)); //星数

                            if (totalnumber < 10) {
                                $(".wjscore-num .pin").html("");
                            }
                        }
                    }
                }
            });
        },
        //想玩和玩过统计
        ItoscoreFun: function (selltime) {
            return this.each(function () {
                var $this = $(this);
                var moreNumber = selltime == "未上市" ? 0 : 10;
                $.ajax({
                    type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/gamemore",
                    data: { "jsondata": JSON.stringify({ "GenneralId": gameid, "number": moreNumber }) },
                    success: function (data) {
                        var licHtm = '', botHtm2 = '', wanNumber = data.wanNumber | 0, playedNumber = data.playedNumber | 0;
                        if (data.status == "ok") {

                            if (data.isListed == true) {
                                licHtm += '<div class="n">';
                                licHtm += '<span class="num1">' + NumW(wanNumber, 6) + '</span>人想玩';
                                licHtm += '&nbsp;&nbsp;/&nbsp;&nbsp;';
                                licHtm += '<span class="num2">' + NumW(playedNumber, 6) + '</span>人玩过';
                                licHtm += '</div>';
                                $this.html(licHtm);
                            }

                            var len = data.result.length > 3 ? 3 : data.result.length;
                            for (var i = 0; i < len; i++) {
                                var percentAge = parseInt(data.result[i].percentAge);
                                if (percentAge > 0) {
                                    var nodeName = data.result[i].nodeName;
                                    var url = "https://ku.gamersky.com/sp/" + data.result[i].nodeDir + "/";
                                    if (data.isListed == true) {
                                        botHtm2 += '<div class="p">好于<a href="' + url + '" target="_blank">' + percentAge + '%的' + nodeName + '</a></div>';
                                    } else {
                                        if (data.result[i].nodeDir == 'ku') {
                                            nodeName = "所有游戏";
                                            url = "https://ku.gamersky.com/sp/";
                                        }
                                        botHtm2 += '<div class="p">超过了<a href="' + url + '" target="_blank">' + percentAge + '%的' + nodeName + '</a></div>';
                                    }
                                }
                            }

                            if (selltime == "已上市") {
                                setTimeout(function () {
                                    if ($("#scoreAvg").text() != '人数不足') {
                                        var realPlayerScore = data.realPlayerScore;
                                        var realPlayersRate = data.realPlayersRate * 100;
                                        var realPlayersRates = parseFloat(realPlayersRate.toFixed(1));
                                        var score = realPlayerScore || '--', rate = isNaN(realPlayersRates) ? '--' : realPlayersRates;
                                        var botHtm1 = '<div class="n">玩过认证玩家<i class="rz"></i>评分' + score + '分，占总评分人数的' + rate + '% <i class="wh"></i></div>';
                                        $(".wjscore").append('<div class="wjscore-bot">' + botHtm1 + botHtm2 + '</div>');
                                    }
                                }, 200);
                            } else {
                                console.log(botHtm2);
                                $(".wjscore-qdpf .num").html(wanNumber);
                                if (botHtm2 && botHtm2 != '') {
                                    $('.wjscore-qdpf').css('display', 'flex')
                                    $(".wjscore-qdpf .Itoscore").html(botHtm2);
                                    $(".wjscore-qdpf .Itoscore").css('float', 'none');
                                    $(".wjscore-qdpf .Itoscore p").css('overflow', 'visible')
                                    $(".wjscore-qdpf .qdcon .p").css('height', 'auto')
                                    $(".wjscore-qdpf .qdcon .p").css('line-height', '28px')
                                    $(".wjscore-qdpf .qdcon").css('height', 'auto')
                                    $(".wjscore-qdpf .qdnum").css('width', 'auto');
                                    $(".wjscore-qdpf .qdnum").css('float', 'none');
                                    $(".wjscore-qdpf .qdnum").css('margin-right', '15px');
                                    $(".wjscore-qdpf .qdnum").css('overflow', 'visible')


                                } else {
                                    $(".wjscore-qdpf .Itoscore").hide()
                                    $(".wjscore-qdpf .qdnum").css('width', 'auto');
                                }

                            }
                        }
                    }
                });
            })
        },

        //星级进度条
        starStatistics: function () {
            return this.each(function () {
                var $this = $(this);
                var $con = $this.find(".wjscore-con");
                $.ajax({
                    type: "GET",
                    dataType: "jsonp",
                    url: "//cm1.gamersky.com/apirating/starstatistics",
                    data: { "GenneralId": gameid },
                    success: function (data) {
                        if (data.status == "ok") {
                            var n = 0, maxNum = 0;
                            maxNum = parseFloat(data.result[0].percentAge);
                            for (var i = 1; i < data.result.length; i++) {
                                var h = parseFloat(data.result[i].percentAge);
                                if (h > maxNum) {
                                    maxNum = h;
                                }
                            }
                            for (var i = data.result.length - 1; i >= 0; i--) {
                                n++;
                                var m = data.result[i].percentAge;
                                var w = m == maxNum ? 110 : (m / maxNum) * 110;
                                w = maxNum == 0 ? maxNum : w;
                                $con.find(".tiao").eq(n - 1).attr("style", "width:" + w.toFixed(1) + "px;");
                                $con.find(".bili").eq(n - 1).html("" + m + "%");
                            }
                        }
                    }
                });
            });
        }
    });

    $.fn.kuInitial = function (options) {
        return this.each(function () {
            var $div = $(this);
            $div.attr("selltime", "未上市").html('<img style="margin:30px auto;display:block;" width="70" height="70" src="//image.gamersky.com/webimg15/zp/loading0.gif" />');
            //检查上市时间
            $(".pingtai").checkMarketTime();
            setTimeout(function () {
                var selltime = $div.attr("selltime");
                $div.html('<div class="wjscore" tit="玩家评分"></div><div class="myscore" tit="我的评分"></div>');
                $kuGameScore.judgeListedState(); //调用初始模板
                $kuGameScore.starStatistics(); //星级进度条

                $(".datetime").judgewanFun();  //想玩和玩过btn按钮
                $(".Itoscore").ItoscoreFun(selltime); //想玩和玩过统计

                $(".wjscore").Rating(selltime); //玩家评分

                /**************************************************/
                kuLayer.starScore("#dfen"); //打分
                kuLayer.GetRating("#dfen"); //获取打分
                kuLayer.userImg(".userimg"); //获取用户登录头像

                kuLayer.getCommnet(".pybtn"); //获取评论内容
                kuLayer.getPlatform(".myscore"); //获取玩过的平台

                kuLayer.GameTagFun(); //常用标签
                kuLayer.UserTagFun(); //我的标签
            }, 500);

            $div.on("click", ".datetime .tpbtn", function (event) {
                event.preventDefault();
                var $this = $(this);
                var currentState = $this[0].innerHTML
                $this.UserOnline(function () {
                    idx = $this.attr("data-i");
                    $this.addClass("cur").siblings().removeClass("cur");
                    kuLayer.mybtnClick(currentState);
                });
            }).on("mouseenter", ".wjscore-bot .wh", function () {
                var $this = $(this);
                $this.html(kuHtm.whHtml('')); //插入问题弹窗

                var stop = $this.offset().top + $this.height();
                var topH = $(document).scrollTop(), winH = $(window).height();
                var sH = winH - (stop - topH), cH = $this.find(".wh_con").outerHeight() + 14;
                $this.find(".wh_layer").toggleClass("cur", sH < cH ? true : false);
                $this.find(".wh_con").attr("style", sH < cH ? 'margin-top:-' + cH + 'px' : '');
            }).on("mouseleave", ".wjscore-bot .wh", function () {
                $(this).html('');
            });

            //打开弹窗
            $(document).on("click", ".pybtn,.itobtn,.editbtn,.mybtn", function (event) {
                event.preventDefault();
                var $this = $(this);
                var currentState = $('.wtxt')[0].innerHTML.indexOf('想玩') != 1 ? '玩过' : '想玩'
                $this.UserOnline(function () {
                    kuLayer.mybtnClick(currentState);

                });
                kuLayer.GetRating("#layerdfen");
            }).on("click", ".delbtn", function (event) { //打开确定弹窗
                event.preventDefault();
                $("body").append(kuHtm.confirmHtml('')); //插入确定弹窗



            }).on("click", ".determinebtn", function (event) {
                event.preventDefault();
                var $this = $(this);
                if ($this.attr("data-click") == 'false') {
                    $this.attr("data-click", true);
                    kuLayer.deleteComment();
                }
            }).on("click", ".ku_pop_close,.ku_pop_mask,.cancelbtn,.pop_close,.pop_mask", function (event) { //关闭弹窗
                kuLayer.GetRating("#layerdfen");
                event.preventDefault();
                $(".ku_pop_mask,.ku_pop_layer,.ppp_mask,.ppp_layer,.pop_mask,.pop_layer").hide();
                $('.dlc_data_pop_layer').remove();
                $('.price_data_pop_layer').remove();
                $('.steam_data_pop_layer').remove();

            }).on("click", ".viewBtn", function (event) { //打开意见弹窗
                event.preventDefault();
                $("body").append(kuHtm.popHtml('')); //插入意见弹窗
                $(".popBtn").FeedBack();
            });

            //玩家常用标签
            $(".WJCYtag").on("click", "a.tagbtn", function () {
                let htm = $(this).siblings(".tag-con").html();

                var $this = $(this), con = $this.siblings(".tag-hide").html();
                $this.siblings(".tag-con").html(con);
                $this.siblings('.tag-hide').html(htm)
                if (!$this.hasClass('show')) {
                    $this.css('transform', 'rotate(180deg)')
                    $this.addClass('show')
                } else {
                    $this.css('transform', 'rotate(0deg)')
                    $this.removeClass('show')


                }
                // $this.remove();
            });
        })
    };

    $kuGameScore.kuInitial();



    $.fn.FeedBack = function () {
        function checkQQ(value) {
            if (value) {
                if (!isQQ(value)) {
                    alert("您输入的QQ号有误请从新输入");
                    return false;
                }
            }
            return true;
        }

        function isQQ(str) {
            var reg = /^[1-9][0-9]{4,}$/;
            return reg.test(str);
        }

        function checkIsNullOrEmpty(value) {
            if (!value || value == "") {
                alert("描述不能为空！");
                return false;
            }
            return true;
        }
        return this.on("click", function (event) {
            event.preventDefault();
            var CorrectInfo = {};
            CorrectInfo.Title = $("#jcjbContentData").attr("title");
            CorrectInfo.GeneralId = $(".viewBtn").attr("data-generalId");
            CorrectInfo.Description = $(".pop_textarea").val();
            CorrectInfo.Phone = $(".pop_QQ").val();
            CorrectInfo.IsReport = 0;
            CorrectInfo.State = 999;

            if (checkQQ(CorrectInfo.Phone) == false || checkIsNullOrEmpty(CorrectInfo.Description) == false) {
                return false;
            }
            $.ajax({
                type: "get",
                dataType: "jsonp",
                url: "//db5.gamersky.com/CorrectReport.aspx",
                data: { "ContentData": JSON.stringify(CorrectInfo) },
                contentType: "application/json;charset=utf-8",
                cache: false,
                success: function (data) {
                    $(".pop_mask,.pop_layer").remove();
                },
                error: function (xhr) { }
            });
        })
    };

    // 可编辑div设置光标位置插入html代码(插入图片)
    $.fn.pasteHtmlAtCaret = function (html) {
        return this.each(function () {
            if (window.getSelection) {
                var selection = lastEditRange.elem ? lastEditRange.sel : window.getSelection();
                if (selection.getRangeAt && selection.rangeCount) {
                    var range = lastEditRange.elem ? lastEditRange.Range : selection.getRangeAt(0);
                    range.deleteContents();
                    var el = document.createElement("div");
                    el.innerHTML = html;
                    var frag = document.createDocumentFragment(), node, lastNode;
                    while ((node = el.firstChild)) {
                        lastNode = frag.appendChild(node);
                    }
                    range.insertNode(frag);
                    if (lastNode) {
                        range = range.cloneRange();
                        range.setStartAfter(lastNode);
                        range.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            } else if (document.selection && document.selection.type != "Control") { // IE < 9
                var selection = lastEditRange.elem ? lastEditRange.sel : document.selection.createRange();
                selection.pasteHTML(html);
            }
        });
    };

    var idx = 0;
    var $cmtTop = $(".ku-pop-btn");
    var $cmtList = $(".remark-list");

    var userId = 0;
    var UserCookie = cookie("UserCookie");
    if (UserCookie) {
        var response = $.parseJSON(UserCookie);
        userId = response.userid;
    }
    var starindex = null
    var kuLayer = {
        kuEditor: function () { //编辑器操作
            var Select = true,
                $textareacode = $("#textarea-code"),
                $kuEditor = $(".kuEditor"),
                p = document.all ? "<p></p>" : "<p><br/></p>";
            var getSelectionText = function () {
                return window.getSelection ? window.getSelection().toString() : document.selection.createRange().text;
            }
            var setTimer;
            var fun = function (str, len1, len2) {
                clearTimeout(setTimer);
                if (len1 == len2) {
                    if (!isTrim(getSelectionText())) {
                        document.execCommand(str);
                    } else {
                        setTimer = setTimeout(function () {
                            fun(str, len1, len2)
                        }, 100);
                    }
                } else {
                    $kuEditor.find("[data-role='" + (str == 'Unlink' ? 'CreateLink' : str) + "']").addClass("cur");
                }
            }

            $textareacode.on("click", function (event) {
                if (!isTrim($textareacode.html())) {
                    $textareacode.html(p).find("p").focus();
                }
                var $this = $(this), evt = event.target;
                var obj = window.getSelection ? window.getSelection() : document.selection.createRange();

                var name = -[1,] ? obj.focusNode.parentNode.tagName.toLowerCase() : evt.nodeName.toLowerCase();
                var len1 = -[1,] ? obj.focusNode.length : evt.innerText.length;
                var len2 = obj.focusOffset;

                $kuEditor.find("a").removeClass("cur");
                switch (name) {
                    case "b":
                    case "strong": fun('Bold', len1, len2); break;
                    case "u": fun('Underline', len1, len2); break;
                    case "i":
                    case "em": fun('Italic', len1, len2); break;
                    case "strike": fun('StrikeThrough', len1, len2); break;
                    case "a": fun('Unlink', len1, len2); break;
                }
                if (isTrim(getSelectionText())) {
                    Select = true;
                }

            }).on("focus keyup blur", function () {
                var $this = $(this), txt = isTrim($this.text());
                if (txt == '') {
                    $this.html(p).find("p").focus();
                }
                kuLayer.textareaNum(txt);
            }).on("paste", function (event) {
                event.preventDefault();
                kuLayer.paste($(this), event);
            });

            $kuEditor.on("click", "a", function (event) {
                event.preventDefault();
                if (!isTrim(getSelectionText())) {
                    return false;
                }
                var $this = $(this), role = $this.attr("data-role");
                if (role == "CreateLink") {
                    document.execCommand(!Select ? "Unlink" : role, false, "javascript:void(0);");
                } else {
                    document.execCommand(role);
                }
                $this.removeClass("cur");
                Select = Select ? false : true;
            });

            var $con = $(".ku-pop-textarea .con");
            $con.attr("data-height", $con.height()).attr("data-i", 1);
            $(".ku-pop-textarea").on("click", ".space", function (event) {
                event.preventDefault();
                var h = parseInt($con.attr("data-height")),
                    i = parseInt($con.attr("data-i")) + 1;
                if (i == 3) {
                    $(this).remove();
                }
                if (i >= 4) {
                    return false;
                }
                $con.height(h * (i == 2 ? i : i + 1)).attr("data-i", i);
                var $layer = $(".ku_pop_layer"), height = $layer.height();
                $layer.css("marginTop", - (height / 2) + 'px'); //定位点评窗口上下剧中
            });
        },
        paste: function (that, evt) {
            var text = '', clp = (evt.originalEvent || evt).clipboardData;
            if (clp == undefined || clp == null) {
                text = window.clipboardData.getData("text") || "";
                if (isTrim(text) != "") {
                    text = that.find("p").length > 0 ? text.replace(/\n/g, '</p><p>') : '<p>' + text.replace(/\n/g, '</p><p>') + '</p>';
                    that.pasteHtmlAtCaret(emojibBatch(text)).focus();
                }
            } else {
                text = clp.getData('text/plain');
                text = text.replace(/       /ig, '　　'); //空格替换
                text = text.replace(/[\r|\t]/ig, ''); //换行清除
                text = text.replace(/[\n][\n]/ig, '</p><p>'); //两个换行转p标签
                text = text.replace(/[\n]/ig, '<br>'); //一个换行转br
                text = text.replace(/<p><br>/ig, '<p>'); //换行清除
                text = text.replace(/<br><\/p>/ig, '</p>'); //换行清除
                text = (text.substr(0, 2) != '<p' ? '<p>' : '') + text + (text.substr(text.length - 2, text.length) != 'p>' ? '</p>' : '');
                text = text.replace(/<p><\/p>/ig, ''); //空格清除

                document.execCommand('insertHTML', false, emojibBatch(text));
            }
        },
        textareaNum: function (txt) {
            var len = txt.length, Bnum = parseInt($(".kuAlert .Bnum").text());
            $(".kuAlert .Anum").text(len).toggleClass("cur", len > Bnum ? true : false);
            $("#submitbtn").attr("data-click", len > Bnum ? true : false);
        },
        kuLabel: function () { //标签操作
            var bqHtml = '<div class="bq"><div class="txt" contenteditable="true"></div><div class="gb">×</div></div>';
            var focusTag = function () {
                $("#myLabel,#usedLabel").find("a").removeClass("cur");
                $(".label-con .txt").each(function () {
                    var txt = $(this).text();
                    $("#myLabel,#usedLabel").find("a").each(function () {
                        if ($(this).text() == txt) {
                            $(this).addClass("cur");
                        }
                    });
                });
            };

            //展开标签
            $(".ku-pop-label").on("click", ".zhanbtn", function (event) {
                event.preventDefault();
                $(this).hide().parent().find(".con").addClass("cur");
                var $layer = $(".ku_pop_layer"), height = $layer.height();
                $layer.css("marginTop", - (height / 2) + 'px'); //定位点评窗口上下剧中
            }).on("click", ".label-con", function () { //点空白添加空白标签
                var e = window.event || e;
                var obj = e.srcElement || e.target;
                if ($(obj).hasClass('txt')) return false;
                var $labelcon = $(".label-con");
                var len1 = $labelcon.find(".bq").length, len2 = 0;
                if (len1 == 0) {
                    $labelcon.html(bqHtml).find(".txt").focus();
                } else {
                    $labelcon.find(".txt").each(function () {
                        if (isTrim($(this).text())) {
                            len2 += 1;
                        }
                    });
                    if (len1 == len2) {
                        $labelcon.append(bqHtml).find(".txt").focus();
                    } else {
                        $labelcon.find(".txt").eq(len2).focus();
                    }
                }
            });
            $(".label-con").on("click", "span", function (event) { //点提示文字添加空白标签
                event.preventDefault();
                $(this).parent().html(bqHtml).find(".txt").focus();
            }).on("click", ".gb", function () { //关闭输入标签
                var $this = $(this), $labelcon = $this.parents(".label-con");
                var tit = $labelcon.attr("tit");
                $this.parents(".bq").remove();
                focusTag();
                if ($labelcon.find(".bq").length == 0) {
                    $labelcon.html('<span>' + tit + '</span>');
                }
            }).on("keyup", ".txt", function () { //输入标签
                focusTag();
            });
            $(".ku-pop-label .con").on("click", "a[class!='cur']", function (event) { //添加文字标签
                event.preventDefault();
                var len = $(".label-con .bq").length;
                var txt = $(this).text();
                if (len == 0) {
                    $(".label-con").html(bqHtml).find(".txt").focus().html(txt);
                } else {
                    $(".label-con").append(bqHtml).find(".bq").eq(len).find(".txt").focus().html(txt);
                    $(".label-con .txt").each(function () {
                        if (!isTrim($(this).text())) {
                            $(this).parent().remove();
                        }
                    });
                }
                focusTag();
            }).on("click", "a.cur", function (event) { //取消文字标签
                event.preventDefault();
                var txt = $(this).text();
                $(".label-con .txt").each(function () {
                    if ($(this).text() == txt) {
                        $(this).parent().remove();
                    }
                });
                $("#myLabel,#usedLabel").find("a").each(function () {
                    if ($(this).text() == txt) {
                        $(this).removeClass("cur");
                    }
                });
            });
        },
        danXuan: function (nav) { //想玩*玩过切换
            var $nav = $(nav);
            var selltime = $kuGameScore.attr("selltime");
            $nav.find("a").removeClass("cur").eq(idx).addClass("cur");
            if (selltime == "未上市") {
                $nav.find("a").eq(1).removeClass("cur").addClass("stop").attr("title", "该游戏未上市");
            } else {
                $("#dfen").removeClass("wss");
            }
            $("#layerdfen,#dfen").css("display", idx == 0 ? "none" : "block");
            // kuLayer.addwanFun($nav.find("a.cur"));
            $nav.on("click", "a", function (event) {
                event.preventDefault();
                var $fxuan = $("#fxuan");
                var strhtml = $(".pingtai").html().replace(/<li>(.*?)<\/li>/g, '$1');
                if (event.target.innerHTML == '想玩') {
                    $fxuan.html(strhtml).find("a").each(function () {
                        var $this = $(this);
                        $this.removeAttr("class");
                    });
                } else {
                    $fxuan.html(strhtml).find("a").each(function () {
                        var $this = $(this),
                            time = $this.attr("data-time");
                        $this.removeAttr("class");
                        if (!judgeTime(time)) {
                            $this.addClass("stop").attr("title", "该平台未上市");
                        }
                    });
                    // if ($fxuan.find("a[title!='该平台未上市']").length == 1) {
                    //     $fxuan.find("a").addClass("cur");
                    // }
                }

                var $this = $(this);
                if ($this.hasClass("cur") || $this.hasClass("stop")) {
                    return false;
                }
                idx = $this.index();
                $this.addClass("cur").siblings().removeClass("cur");
                $("#layerdfen,#dfen").css("display", idx == 0 ? "none" : "block");
                if (idx == 0) {
                    $("#layerdfen,#dfen").find("a").removeClass("cur");
                    $("#layerdfen,#dfen").find(".dftxt").html('');
                }
                kuLayer.addwanFun($this);
                $.ajax({
                    type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addplatform",
                    data: { "jsondata": JSON.stringify({ "GenneralId": gameid, "platform": '' }) },
                    success: function (response) {
                        if (response.status == "ok") {
                            kuLayer.updateIndex();
                        }
                    }
                });
                $(".doneTxt").hide()
            });
        },
        starScore: function (div) { //✩✩✩✩✩打分
            var $div = $(div);
            $div.on("mouseenter", "a", function () {
                var $this = $(this), i = $div.find("a").index($this) + 1;
                if ($("#dfen").hasClass("wss")) { return false; }
                kuLayer.fnShow("#dfen", i);
                kuLayer.fnShow("#layerdfen", i);
            }).on("mouseleave", function () {
                if ($("#dfen").hasClass("wss")) { return false; }
                var sorce = $("#layerdfen,#dfen").attr("data-sorce");
                if (!cookie("UserCookie") && !sorce) {
                    kuLayer.fnShow("#layerdfen", -1);
                    kuLayer.fnShow("#dfen", -1);
                } else {
                    var i = parseInt(sorce / 2);
                    kuLayer.fnShow("#dfen", i);
                    kuLayer.fnShow("#layerdfen", i);
                }
            }).on("click", "a", function (event) {
                event.preventDefault();
                var $this = $(this), sorce = $this.attr("fromdevice");
                if ($("#dfen").hasClass("wss") || !cookie("UserCookie")) {
                    return false;
                }
                if (idx == 1) {
                    $("#layerdfen,#dfen").attr("data-sorce", sorce);
                    starindex = $this.parent()
                }
            });
        },
        gouXuan: function (div) { //单选玩过的平台
            $(div).off("click").on("click", "a", function (event) {
                event.preventDefault();
                if ($(this).hasClass("stop") == false) {
                    $(this).addClass("cur").siblings().removeClass("cur");
                }
            });
        },
        userImg: function (img) {
            var UserCookie = cookie("UserCookie");
            if (UserCookie !== undefined && UserCookie !== null) {
                var response = $.parseJSON(UserCookie);
                var userface = response.userface;
                if (userface == '') {
                    userface = 'https://image.gamersky.com/webimg15/comment/anonymous.jpg';
                }
                $(img).attr("src", userface);
            } else {
                $.ajax({
                    type: "GET",
                    dataType: "jsonp",
                    url: "//i.gamersky.com/api/logincheck",
                    success: function (response) {
                        if (response.status == "ok") {
                            $(img).attr("src", response.userface);
                        }
                    }
                });
            }
        },
        mybtnClick: function (options) {

            var selltime = $kuGameScore.attr("selltime");
            if ($(".ku_pop_layer").length == 0) {
                $("body").append(kuHtm.LayerHtml('')); //插入弹窗

                if (selltime == "未上市") {
                    $(".fuxuan").hide();
                } else {

                    if (options == '想玩') {
                        var $fxuan = $("#fxuan");
                        var strhtml = $(".pingtai").html().replace(/<li>(.*?)<\/li>/g, '$1');
                        $fxuan.html(strhtml).find("a").each(function () {
                            var $this = $(this),
                                time = $this.attr("data-time");
                            $this.removeAttr("class");
                        });
                        // if ($fxuan.find("a[title!='该平台未上市']").length == 1) {
                        //     $fxuan.find("a").addClass("cur");
                        // }
                    } else {
                        var $fxuan = $("#fxuan");
                        var strhtml = $(".pingtai").html().replace(/<li>(.*?)<\/li>/g, '$1');
                        $fxuan.html(strhtml).find("a").each(function () {
                            var $this = $(this),
                                time = $this.attr("data-time");
                            $this.removeAttr("class");
                            if (!judgeTime(time)) {
                                $this.addClass("stop").attr("title", "该平台未上市");
                            }
                        });
                        // if ($fxuan.find("a[title!='该平台未上市']").length == 1) {
                        //     $fxuan.find("a").addClass("cur");
                        // }
                    }
                }


                $("#textarea-code").html($(".myremark .con").html());

                kuLayer.textareaNum(isTrim($("#textarea-code").text()));

                kuLayer.GameTagFun(); //常用标签
                kuLayer.UserTagFun(); //我的标签

            } else {
                var html = $(".ku_pop_layer").html();
                $(".ku_pop_mask,.ku_pop_layer").remove();
                $("body").append('<div class="ku_pop_mask"></div><div class="ku_pop_layer">' + html + '</div>');

                if (selltime == "未上市") {
                    $(".fuxuan").hide();
                } else {

                    if (options == '想玩') {

                        var $fxuan = $("#fxuan");
                        var strhtml = $(".pingtai").html().replace(/<li>(.*?)<\/li>/g, '$1');
                        $fxuan.html(strhtml).find("a").each(function () {
                            var $this = $(this),
                                time = $this.attr("data-time");
                            $this.removeAttr("class");
                        });
                        // if ($fxuan.find("a[title!='该平台未上市']").length == 1) {
                        //     $fxuan.find("a").addClass("cur");
                        // }
                    } else {
                        var $fxuan = $("#fxuan");
                        var strhtml = $(".pingtai").html().replace(/<li>(.*?)<\/li>/g, '$1');
                        $fxuan.html(strhtml).find("a").each(function () {
                            var $this = $(this),
                                time = $this.attr("data-time");
                            $this.removeAttr("class");
                            if (!judgeTime(time)) {
                                $this.addClass("stop").attr("title", "该平台未上市");
                            }
                        });
                        // if ($fxuan.find("a[title!='该平台未上市']").length == 1) {
                        //     $fxuan.find("a").addClass("cur");
                        // }
                    }
                }
            }

            kuLayer.Submit(".ku_pop_layer"); //弹窗发布
            kuLayer.userImg(".userimg") //获取用户登录头像
            kuLayer.danXuan(".ku-pop-nav"); //想玩玩过切换
            kuLayer.starScore("#layerdfen"); //打分
            kuLayer.GetRating("#layerdfen"); //获取打分

            kuLayer.gouXuan("#fxuan"); //单选
            kuLayer.getPlatform(".myscore"); //获取玩过的平台
            kuLayer.kuEditor(); //调用编辑器
            kuLayer.kuLabel(); //调用标签

            var cmtId = $(".myremark").attr("cmtid");
            if (cmtId > 0) {
                $("#submitbtn").attr("class", "submitbtn");
                $(".submitbtn").on("click", function (event) {
                    event.preventDefault();
                    if ($(this).attr("data-click") == 'false') {
                        kuLayer.editComment($(this));
                    }
                });
                $(".ku_pop_layer").on("mouseenter", "#textarea-code", function () {
                    if (isTrim($(this).text()) != '' && $("#submitbtn").length > 0) {
                        $(".ku-pop-btn .con").html("修改评价会重置当前评价的顶/踩数").show();
                    }
                });
            }
        },
        GetRating: function (div) {
            $(div).each(function () {
                $.ajax({
                    type: "GET",
                    dataType: "jsonp",
                    url: "//cm1.gamersky.com/apirating/getuserrating",
                    data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Type": "0" }) },
                    success: function (data) {
                        console.log(data);
                        if (data.status == "ok") {
                            var sorce = data.sorce;
                            var i = parseInt(sorce / 2);
                            kuLayer.fnShow("#dfen", i);
                            kuLayer.fnShow("#layerdfen", i);
                            $(".myscore .ftxt").show();
                            $("#layerdfen,#dfen").attr("data-sorce", sorce).find(".dftit").html('我的评分：');
                            //$("#layerdfen,#dfen").attr("data-sorce",i)
                        } else {
                            kuLayer.fnShow("#layerdfen", -1);
                            kuLayer.fnShow("#dfen", -1);
                        }
                    }
                });
            });
        },
        //打分
        AddRating: function (div) {
            $(div).each(function () {
                var $this = $(this);
                var sorce = $this.attr("data-sorce");
                if (!sorce || sorce == 0) {
                    return false
                }
                $.ajax({
                    type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/AddRating",
                    data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": sorce, "Type": 0, "fromDevice": 0 }) },
                    success: function (data) {
                        var cmtid = $(".myremark").attr("cmtid") | 0;
                        var i = parseInt(data.Sorce / 2);
                        if (i > 0) {
                            kuLayer.updateIndex();
                            kuLayer.fnShow("#dfen", i);
                            kuLayer.fnShow("#layerdfen", i);
                            kuLayer.GetRating("#layerdfen"); //获取打分

                            if (cmtid > 0) {
                                var $lisfloor = $('.remark-list-floor[cmtid=' + cmtid + ']');
                                if ($lisfloor.length > 0) {
                                    $lisfloor.find(".user-xin .xin div").attr("class", "x" + i);
                                }
                            }
                        }
                    }
                });
            })
        },
        fnShow: function (div, num) {
            $(div).each(function () {
                var $this = $(this), $ida = $this.find("a");
                for (var i = 1; i <= $ida.length; i++) {
                    num >= i ? $ida.eq(i - 1).addClass("cur") : $ida.eq(i - 1).removeClass("cur");
                }
                var dftxt = $ida.eq(num - 1).attr("data-txt");
                $this.find(".dftxt").html(num > 0 ? dftxt : "");
            });
        },
        addwanFun: function (div) {
            $(div).each(function () {
                var $this = $(this);
                if ($this.attr("data-click") == "false") {
                    $this.attr("data-click", true);
                    var type = $this.attr("data-type");
                    var sorce = $this.attr("data-sorce");
                    $.ajax({
                        type: "get", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addwanRating",
                        data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": sorce, "Type": type, "FromDevice": 0 }) },
                        success: function (data) {
                            $this.attr("data-click", false);
                            if (data.status == 'ok') {
                                $(".datetime").hide();
                                $(".myscore .ftxt").show().find(".wtxt").html('我' + (idx == 0 ? '想玩' : '玩过') + '这个游戏');
                            }
                            if (type == 7) {
                                $("#dfen,#fxuan").find("a").removeClass("cur");
                                if ($("#fxuan a").length == 1) {
                                    $("#fxuan a").addClass("cur");
                                }
                            }
                        }
                    });
                }
            });
        },
        remarkCommnet: function (content, cmtId) {
            if (content != '') {
                var html = '<div class="con">' + content + '</div><div class="btn" style="display:none"><a href="javascript:;" class="openBtn" tit="展开"></a></div>';
                $(".myremark").show().attr("cmtId", cmtId).html(html);
            }
            $(".openBtn").openBtnFun();
            $(".myscore .ftxt").show();
        },
        //获取评论内容
        getCommnet: function (div) {
            var $this = $(div);
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/api/exists",
                data: { "GenneralId": gameid },
                success: function (response) {
                    if (response.status != 'err') {
                        var isExist = response.isExist;
                        var cmtId = response.cmtId;
                        var content = response.content;
                        var status = response.status;
                        if (isExist == true) {
                            if (status != '') {
                                if ($(".inAudit").length == 0) {
                                    $(".myscore .fbtn").append('<span class="inAudit">审核中</span>');
                                }
                                $this.hide();
                            } else {
                                $this.show();
                            }
                            kuLayer.remarkCommnet(content, cmtId);
                        }
                    }
                }
            });
        },
        editComment: function (div) {
            var $this = $(div);
            var cmtId = $(".myremark").attr("cmtId");
            var content = $("#textarea-code").html();

            var con = encodeURIComponent(content);
            var playText = $('.ku-pop-nav').eq(0).find('.cur').text()
            kuLayer.AddGameTagFun();
            var pfhtml = "";
            $(".doneTxt").hide()
            $("#fxuan").find(".cur").each(function () {
                pfhtml += $(this).text() + "、";
            });
            if (pfhtml != '') {
                pfhtml = pfhtml.substring(0, pfhtml.length - 1);
            }
            if (pfhtml != '') {
                $(".doneTxt").show().html(playText + '的平台：<span>' + pfhtml + '</span>');
            }
            $('.wtxt').eq(0).html('我' + playText + '这个游戏')

            var brow = $.browser.msie ? "jsonp" : "json";

            var score = $("#layerdfen,#dfen").attr("data-sorce") || 0;
            var type = $(".ku-pop-nav").find("a.cur").attr("data-type");

            var score = $("#layerdfen,#dfen").attr("data-sorce");
            var $atext = $(".ku-pop-nav a.cur").text();

            if (pfhtml == '' && score && score > 0 && $atext == '玩过') {
                alert('请选择评分平台!')
                return false
            }
            $(".ku_pop_mask,.ku_pop_layer").remove();

            $.ajax({
                type: "POST", dataType: brow, url: "//cm1.gamersky.com/api/updatecomment",
                data: { "jsondata": JSON.stringify({ "cmtId": cmtId, "content": con, "platform": pfhtml, "score": score, "type": type }) },
                xhrFields: { withCredentials: true },
                success: function (response) {
                    if (response.status == "ok") {
                        var data = $.parseJSON(response.body);

                        kuLayer.remarkCommnet(content, data.cmtId); //获取评论内容

                        var $lisfloor = $('.remark-list-floor[cmtid=' + cmtId + ']');
                        if ($lisfloor.length > 0) {
                            $lisfloor.find(".remark-content").html(content);
                            $lisfloor.find(".user-xin .xin div").attr("class", "x" + data.start.replace('.0', ""));
                            $lisfloor.find(".user-xin .txt").text(data.platformthtml);
                            if (!isTrim(data.lastTime)) {
                                $lisfloor.find(".user-time").html("最后修改" + data.lastTime);
                            }
                            $lisfloor.find(".remark-issue").zhankai();
                        }

                    } else {
                        alert(response.body);
                    }
                }
            });
        },
        deleteComment: function () {
            var cmtId = $(".myremark").attr("cmtId");
            $(".myremark,.doneTxt").hide().html("").removeAttr("cmtid");
            kuLayer.fnShow("#dfen", -1);
            $("#dfen").hide();
            $(".myscore .ftxt").hide().find(".wtxt").html("");
            $(".myscore .myTag").hide().html("");
            $(".myscore .tpbtn").removeClass("cur");
            $(".datetime,.pybtn").show();
            $(".ppp_mask,.ppp_layer,.inAudit").remove();
            $.removeCookie("MYscore" + gameid + userId, { path: "/" });
            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/api/deletecomment",
                data: { "jsondata": JSON.stringify({ "sid": gameid, "cmtid": cmtId }) },
                success: function (response) {
                    if (response.body == "ok") {
                        var $lisfloor = $('.remark-list-floor[cmtid=' + cmtId + ']');
                        if ($lisfloor.length > 0) {
                            $lisfloor.remove();
                            if ($(".remark-list .remark-list-floor").length == 0) {
                                $(".remark-list").html('<div class="remark-loading">没有内容</div>');
                                $(".remark-page").html('');
                            }
                        }
                    }
                }
            });
        },
        //添加玩过的平台
        addPlatform: function (pfhtml) {
            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addplatform",
                data: { "jsondata": JSON.stringify({ "GenneralId": gameid, "platform": pfhtml }) },
                success: function (response) {
                    if (response.status == "ok") {
                        $(".myscore .ftxt").show();
                        kuLayer.updateIndex();
                        $(".ku_pop_mask,.ku_pop_layer").remove();
                    }
                }
            });
        },
        //获取玩过的平台
        getPlatform: function (div) {

            var $this = $(div);
            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/getplatform",
                data: { "jsondata": JSON.stringify({ "GenneralId": gameid }) },
                success: function (response) {
                    if (response.status == 'ok') {
                        var platform = response.platform;
                        if (platform != '') {
                            var platformArrays = platform.split("、");
                            if (!String.prototype.trim) {
                                String.prototype.trim = function () { return this.replace(/^\s+|\s+$/g, ''); };
                            }
                            for (var i = 0; i < platformArrays.length; i++) {
                                $("#fxuan").find("a").each(function () {
                                    if ($(this).html().toLocaleLowerCase().trim() == platformArrays[i].toLocaleLowerCase().trim()) {
                                        $(this).addClass("cur");
                                    }
                                });
                            }
                        }

                        if (platform != '') {
                            if (response.wanplay) {
                                $this.find(".doneTxt").show().html('想玩的平台：<span>' + platform + '</span>');
                            } else {

                                $this.find(".doneTxt").show().html('玩过的平台：<span>' + platform + '</span>');

                            }
                        } else {
                            $this.find(".doneTxt").hide().html('');
                        }
                    }
                }
            });
        },
        //点击发布按钮
        Submit: function (div) {
            var $this = $(div);
            $this.on("click", "a.cmt-btn[data-click='false']", function (event) {
                event.preventDefault();
                var that = $(this);
                var text = $("#textarea-code").text();
                var con = $("#textarea-code").html();
                var $cmttextarea = $this.find(".remark-textarea");
                var cmtid = $cmttextarea.attr("cmtid");
                var pfhtml = "";
                $(".doneTxt").hide()
                var playText = $('.ku-pop-nav').eq(0).find('.cur').text()
                $("#fxuan").find(".cur").each(function () {
                    pfhtml += $(this).text() + "、";
                });
                if (pfhtml != '') {
                    pfhtml = pfhtml.substring(0, pfhtml.length - 1);
                }
                if (pfhtml != '') {
                    $(".doneTxt").show().html(playText + '的平台：<span>' + pfhtml + '</span>');
                }
                $('.wtxt').eq(0).html('我' + playText + '这个游戏')

                if (cmtid == undefined) {

                    var score = $("#layerdfen,#dfen").attr("data-sorce") || 0;
                    var $atext = $(".ku-pop-nav a.cur").text();



                    if (pfhtml == '' && score && score > 0 && $atext == '玩过') {
                        alert('请选择评分平台!')
                        return false
                    }

                    kuLayer.AddwanRating(function (response) {
                        if (response.status == "ok") {
                            kuLayer.updateIndex();
                            $(".datetime").hide();
                        }
                        kuLayer.addPlatform(pfhtml);
                        kuLayer.AddGameTagFun();
                        if (!isTrim(text)) {
                            $(".ku_pop_mask,.ku_pop_layer").remove();
                            return false;
                        }


                        kuLayer.SubmitAjax(con, pfhtml, playText, function (response) {
                            that.attr("data-click", false);
                            if (response.status == "ok") {
                                var data = $.parseJSON(response.body);

                                if (data.Comment != null) {

                                    kuLayer.remarkCommnet(con, data.cmtId); //获取评论内容

                                    if ($("#remark-nav1 a[loadtype='1'].cur").length > 0 && $("#remark-nav3 a[nametype='steam'].cur").length > 0) {
                                        $(".ku_pop_mask,.ku_pop_layer").remove();
                                        return false;
                                    }
                                    if ($(".remark-list .remark-list-floor").length > 0) {
                                        $(".remark-list").prepend(data.Comment);
                                    } else {
                                        $(".remark-list").html(data.Comment);
                                    }
                                    $("a.remark-support,a.remark-notsupport").addLike();
                                    $(".remark-issue,.floor-user").zhankai();
                                }
                            } else {
                                alert(response.body);
                            }
                        });
                    });

                } else {
                    kuLayer.SubmitAjax(con, pfhtml, playText, function (response) {
                        that.attr("data-click", false);
                        if (response.status == "ok") {
                            var data = $.parseJSON(response.body);
                            if (data.Comment != null) {
                                kuLayer.remarkCommnet(con, data.cmtId); //获取评论内容
                            }
                        } else {
                            alert(response.body);
                        }
                    })
                }
                kuLayer.AddRating(starindex); //打分
            })
        },
        SubmitAjax: function (con, platform, typeString, fun) {
            var sid = $("#Remark").attr("sid"); //文章ID
            var nodeId = $("#Remark").attr("nodeId"); //节点ID
            var url = window.location.href;
            var title = document.title;
            var content = encodeURIComponent(con);
            var cmtid = $(".remark-textarea").attr("cmtid");
            var brow = $.browser.msie ? "jsonp" : "json";
            var score = $("#layerdfen,#dfen").attr("data-sorce") || '';
            var type = "7";//$(".ku-pop-nav").find("a.cur").attr("data-type");
            var $atext = $(".ku-pop-nav a.cur").text();
            if (typeString == "玩过") {
                type = "8";
            }
            var pfhtml = platform;

            if (score != "" && pfhtml == "" && $atext == '玩过') {
                alert("请选择平台。");
                return;
            }

            var jsondata = { sid: sid, content: content, cmtid: cmtid, topicTitle: title, topicUrl: url, nodeId: nodeId, score: score, type: type, platform: pfhtml };
            $.ajax({
                type: "POST", dataType: brow, url: "//cm1.gamersky.com/api/addcommnet",
                data: { "jsondata": JSON.stringify(jsondata) },
                xhrFields: { withCredentials: true },
                success: function (response) {
                    // var data = $.parseJSON(response.body);
                    // kuLayer.remarkCommnet(decodeURIComponent(content), data.cmtId); //获取评论内容



                    fun(response);
                }
            });
        },
        AddwanRating: function (fun) {
            var $nava = $(".ku-pop-nav a.cur");
            if ($nava.attr("data-click") == "false") {
                $nava.attr("data-click", true);
                var type = $nava.attr("data-type");
                $.ajax({
                    type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addwanRating",
                    data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": 1, "Type": type, "FromDevice": 0 }) },
                    success: function (response) {
                        $nava.attr("data-click", false);
                        fun(response);
                    }
                });
            }
        },
        updateIndex: function (options) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/api/updateindex",
                data: { sid: gameid },
                success: function (response) { }
            });
        },
        //添加我的标签
        AddGameTagFun: function (options) {
            var tagText = "";
            $(".label-con .txt").each(function () {
                var txt = isTrim($(this).text());
                if (txt != "") {
                    tagText += txt + "、";
                }
            });
            if (tagText.indexOf("、") > 0) {
                tagText = tagText.substring(0, tagText.length - 1);
            }
            if (tagText != '') {
                $(".myTag").show().html("我的标签：<span>" + tagText + "</span>");
            } else {
                $(".myTag").hide().html("");
            }
            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/gameapi/AddGameTag",
                data: { "jsondata": JSON.stringify({ gameId: gameid, tagText: tagText }) },
                success: function (response) {
                    if (response.status == "ok") {
                        $(".ku_pop_mask,.ku_pop_layer").hide();
                    }
                }
            });
        },
        //常用标签
        GameTagFun: function (options) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/GameApi/GetGameTag",
                data: { "gameId": gameid, "pageSize": 20, "quote": 0 },
                success: function (response) {
                    if (response.result != undefined && response.result.length > 0) {
                        var result = IEVersion() <= 8 ? response.result : dedupes(response.result, "tagName"); //去重处理
                        var length = result.length;

                        var usedLabelHtml = '', userTagHide = '', userTagHtml = '', userTagCon = '';
                        $.each(result, function (index, value) {
                            usedLabelHtml += '<a href="javascript:;">' + value.tagName + '</a>';
                            userTagHide += '<a class="userTagHide" target="_blank" href="https://ku.gamersky.com/sp/0-0-0-' + value.tagId + '-0-0.html">' + value.tagName + '</a>';
                        });
                        $.each(result, function (index, value) {
                            if (index < 10) {
                                userTagCon += '<a target="_blank" href="https://ku.gamersky.com/sp/0-0-0-' + value.tagId + '-0-0.html">' + value.tagName + '</a>';
                            }
                        });
                        userTagHtml += '<div class="YXBQ_tit">游戏标签</div><span class="tag-con">' + userTagCon + '</span>';
                        if (length > 10) {
                            userTagHtml += '<a href="javascript:;" class="tagbtn"></a>';
                            userTagHtml += '<div class="tag-hide">' + userTagHide + '</div>';
                        }

                        $(".WJCYtag").html(userTagHtml).siblings(".useratg").show();

                        $("#usedLabel").html(usedLabelHtml);
                        if ($("#usedLabel").height() > 30) {
                            $("#usedLabel").parent().removeClass("cur").next("a.zhanbtn").show();
                        }
                    } else {
                        $(".WJCYtag").hide()
                    }
                }
            });
        },
        //我的标签
        UserTagFun: function (options) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/GameApi/GetUserTag",
                data: { "pageSize": 20, "gameId": gameid },
                success: function (response) {
                    if (response.result != undefined || response.userTagText != undefined) {
                        var userTagText = response.userTagText;
                        var result = IEVersion() <= 8 ? response.result : dedupes(response.result, "tagName"); //去重处理
                        var myLabelHtml = "", LabelHtml = "";

                        if (userTagText != "") {
                            var userLabelList = userTagText.split('、');
                            for (var i = 0; i < userLabelList.length; i++) {
                                LabelHtml += '<div class="bq"><div class="txt" contenteditable="true">' + userLabelList[i] + '</div><div class="gb">×</div></div>';
                            }
                            $(".label-con").html(LabelHtml);
                            $(".myTag").show().html("我的标签：<span>" + userTagText + "</span>");
                        } else {
                            $(".myTag").hide().html("");
                        }

                        for (var i = 0; i < result.length; i++) {
                            if (result[i].tagName != "") {
                                myLabelHtml += '<a href="javascript:;">' + result[i].tagName + '</a>';
                            }
                        }
                        $("#myLabel").html(myLabelHtml);
                        if ($("#myLabel").height() > 30) {
                            $("#myLabel").parent().removeClass("cur").next("a.zhanbtn").show();
                        }
                        $(".label-con .txt").each(function () {
                            var txt = $(this).text();
                            $("#myLabel,#usedLabel").find("a").each(function () {
                                if ($(this).text() == txt) {
                                    $(this).addClass("cur");
                                }
                            });
                        });
                    }
                }
            });
        }
    };

})(jQuery);