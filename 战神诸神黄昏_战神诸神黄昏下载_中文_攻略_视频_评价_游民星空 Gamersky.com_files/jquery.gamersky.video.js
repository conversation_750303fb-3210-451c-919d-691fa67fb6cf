if (typeof deconcept == "undefined") {
    var deconcept = new Object;
}
typeof deconcept.util == "undefined" && (deconcept.util = new Object),
    typeof deconcept.SWFObjectUtil == "undefined" && (deconcept.SWFObjectUtil = new Object),
    deconcept.SWFObject = function (e, t, n, r, i, s, o, u, a, f) {
        if (!document.getElementById) {
            return;
        }
        this.DETECT_KEY = f ? f : "detectflash",
            this.skipDetect = deconcept.util.getRequestParameter(this.DETECT_KEY),
            this.params = new Object,
            this.variables = new Object,
            this.attributes = new Array,
            e && this.setAttribute("swf", e),
            t && this.setAttribute("id", t),
            n && this.setAttribute("width", n),
            r && this.setAttribute("height", r),
            i && this.setAttribute("version", new deconcept.PlayerVersion(i.toString().split("."))),
            this.installedVer = deconcept.SWFObjectUtil.getPlayerVersion(),
            !window.opera && document.all && this.installedVer.major > 7 && (deconcept.SWFObject.doPrepUnload = !0),
            s && this.addParam("bgcolor", s);
        var l = o ? o : "high";
        this.addParam("quality", l),
            this.setAttribute("useExpressInstall", !1),
            this.setAttribute("doExpressInstall", !1);
        var c = u ? u : window.location;
        this.setAttribute("xiRedirectUrl", c),
            this.setAttribute("redirectUrl", ""),
            a && this.setAttribute("redirectUrl", a)
    }, deconcept.SWFObject.prototype = {
        useExpressInstall: function (e) {
            this.xiSWFPath = e ? e : "expressinstall.swf",
                this.setAttribute("useExpressInstall", !0)
        },
        setAttribute: function (e, t) {
            this.attributes[e] = t
        },
        getAttribute: function (e) {
            return this.attributes[e]
        },
        addParam: function (e, t) {
            this.params[e] = t
        },
        getParams: function () {
            return this.params
        },
        addVariable: function (e, t) {
            this.variables[e] = t
        },
        getVariable: function (e) {
            return this.variables[e]
        },
        getVariables: function () {
            return this.variables
        },
        getVariablePairs: function () {
            var e = new Array, t, n = this.getVariables();
            for (t in n) {
                e[e.length] = t + "=" + n[t];
            }
            return e
        },
        getSWFHTML: function () {
            var e = "";
            if (navigator.plugins && navigator.mimeTypes && navigator.mimeTypes.length) {
                this.getAttribute("doExpressInstall") && (this.addVariable("MMplayerType", "PlugIn"), this.setAttribute("swf", this.xiSWFPath)),
                    e = '<embed type="application/x-shockwave-flash" src="' + this.getAttribute("swf") + '" width="' + this.getAttribute("width") + '" height="' + this.getAttribute("height") + '" style="' + this.getAttribute("style") + '"',
                    e += ' id="' + this.getAttribute("id") + '" wmode="transparent" name="' + this.getAttribute("id") + '" ';
                var t = this.getParams();
                for (var n in t) {
                    e += [n] + '="' + t[n] + '" ';
                }
                var r = this.getVariablePairs().join("&");
                r.length > 0 && (e += 'flashvars="' + r + '"'),
                    e += "/>"
            } else {
                this.getAttribute("doExpressInstall") && (this.addVariable("MMplayerType", "ActiveX"), this.setAttribute("swf", this.xiSWFPath)),
                    e = '<object id="' + this.getAttribute("id") + '" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="' + this.getAttribute("width") + '" height="' + this.getAttribute("height") + '" style="' + this.getAttribute("style") + '">',
                    e += '<param name="movie" value="' + this.getAttribute("swf") + '" />',
                    e += '<param name="wmode" value="transparent" />';
                var i = this.getParams();
                for (var n in i) {
                    e += '<param name="' + n + '" value="' + i[n] + '" />';
                }
                var s = this.getVariablePairs().join("&");
                s.length > 0 && (e += '<param name="flashvars" value="' + s + '" />'),
                    e += "</object>"
            }
            return e
        },
        write: function (e) {
            if (this.getAttribute("useExpressInstall")) {
                var t = new deconcept.PlayerVersion([6, 0, 65]);
                this.installedVer.versionIsValid(t) && !this.installedVer.versionIsValid(this.getAttribute("version")) && (this.setAttribute("doExpressInstall", !0), this.addVariable("MMredirectURL", escape(this.getAttribute("xiRedirectUrl"))), document.title = document.title.slice(0, 47) + " - Flash Player Installation", this.addVariable("MMdoctitle", document.title))
            }
            if (this.skipDetect || this.getAttribute("doExpressInstall") || this.installedVer.versionIsValid(this.getAttribute("version"))) {
                var n = typeof e == "string" ? document.getElementById(e) : e;
                return n.innerHTML = this.getSWFHTML(),
                    !0;
            }
            return this.getAttribute("redirectUrl") != "" && document.location.replace(this.getAttribute("redirectUrl")),
                !1
        }
    }, deconcept.SWFObjectUtil.getPlayerVersion = function () {
        var e = new deconcept.PlayerVersion([0, 0, 0]);
        if (navigator.plugins && navigator.mimeTypes.length) {
            var t = navigator.plugins["Shockwave Flash"];
            t && t.description && (e = new deconcept.PlayerVersion(t.description.replace(/([a-zA-Z]|\s)+/, "").replace(/(\s+r|\s+b[0-9]+)/, ".").split(".")))
        } else if (navigator.userAgent && navigator.userAgent.indexOf("Windows CE") >= 0) {
            var n = 1, r = 3;
            while (n)
                try {
                    r++,
                        n = new ActiveXObject("ShockwaveFlash.ShockwaveFlash." + r),
                        e = new deconcept.PlayerVersion([r, 0, 0])
                } catch (i) {
                    n = null
                }
        } else {
            try {
                var n = new ActiveXObject("ShockwaveFlash.ShockwaveFlash.7")
            } catch (i) {
                try {
                    var n = new ActiveXObject("ShockwaveFlash.ShockwaveFlash.6");
                    e = new deconcept.PlayerVersion([6, 0, 21]),
                        n.AllowScriptAccess = "always"
                } catch (i) {
                    if (e.major == 6) {
                        return e;
                    }
                }
                try {
                    n = new ActiveXObject("ShockwaveFlash.ShockwaveFlash")
                } catch (i) { }
            }
            n != null && (e = new deconcept.PlayerVersion(n.GetVariable("$version").split(" ")[1].split(",")));
        }
        return e;
    }, deconcept.PlayerVersion = function (e) {
        this.major = e[0] != null ? parseInt(e[0]) : 0,
            this.minor = e[1] != null ? parseInt(e[1]) : 0,
            this.rev = e[2] != null ? parseInt(e[2]) : 0
    }, deconcept.PlayerVersion.prototype.versionIsValid = function (e) {
        return this.major < e.major ? !1 : this.major > e.major ? !0 : this.minor < e.minor ? !1 : this.minor > e.minor ? !0 : this.rev < e.rev ? !1 : !0
    }, deconcept.util = {
        getRequestParameter: function (e) {
            var t = document.location.search || document.location.hash;
            if (e == null) {
                return t;
            }
            if (t) {
                var n = t.substring(1).split("&");
                for (var r = 0; r < n.length; r++) {
                    if (n[r].substring(0, n[r].indexOf("=")) == e) {
                        return n[r].substring(n[r].indexOf("=") + 1)
                    }
                }
            }
            return "";
        }
    }, deconcept.SWFObjectUtil.cleanupSWFs = function () {
        var e = document.getElementsByTagName("OBJECT");
        for (var t = e.length - 1; t >= 0; t--) {
            e[t].style.display = "none";
            for (var n in e[t]) {
                typeof e[t][n] == "function" && (e[t][n] = function () { })
            }
        }
    }, deconcept.SWFObject.doPrepUnload && (deconcept.unloadSet || (deconcept.SWFObjectUtil.prepUnload = function () {
        __flash_unloadHandler = function () { },
            __flash_savedUnloadHandler = function () { },
            window.attachEvent("onunload", deconcept.SWFObjectUtil.cleanupSWFs)
    }, window.attachEvent("onbeforeunload", deconcept.SWFObjectUtil.prepUnload), deconcept.unloadSet = !0)), !document.getElementById && document.all && (document.getElementById = function (e) {
        return document.all[e];
    });

var getQueryParamValue = deconcept.util.getRequestParameter, FlashObject = deconcept.SWFObject, SWFObject = deconcept.SWFObject;
(function ($) {
    function t() {
        var e = navigator.userAgent.toLowerCase(),
            t = e.match(/ipad/i) == "ipad",
            n = e.match(/iphone os/i) == "iphone os",
            r = e.match(/midp/i) == "midp",
            i = e.match(/rv:*******/i) == "rv:*******",
            s = e.match(/ucweb/i) == "ucweb",
            o = e.match(/android/i) == "android",
            u = e.match(/windows ce/i) == "windows ce",
            a = e.match(/windows mobile/i) == "windows mobile",
            f = e.match(/webview/i) == "webview";
        return t || n || r || i || s || o || u || a;
    }

    $.fn.GamerSkyPlayer = function (n) {
        return this.each(function () {
            console.log(123);

            $(n.videoSource).click(function () {
                var $this = $(this);
                $this.addClass("cur").siblings().removeClass("cur");
                var i = $this.attr("data-sitename");
                var s = $this.attr("data-vid");
                var o = $this.attr("data-source") || '';
                var u = "", a = "", f = !1, l = "";

                console.log(i, s, o);

                switch (i) {
                    case "youku":
                        a = "//static.youku.com/v1.0.0222/v/swf/player.swf?VideoIDS=" + s;
                        u += "&isShowRelatedVideo=false&amp;showAd=0&amp;show_pre=1&amp;show_next=1&amp;VideoIDS=" + s + "&amp;isAutoPlay=true&amp;isDebug=false&amp;UserID=&amp;winType=interior&amp;playMovie=true&amp;RecordCode=1001,1002,1003,1004,1005,1006,2001,3001,3002,3003,3004,3005,3007,3008,9999";
                        l = "//player.youku.com/embed/" + s;
                        f = !0;
                        break;
                    case "tudou":
                        var c = /^\d+$/;
                        u = "tvcCode=-1&hd=2",
                            a = "//tudou.com/v/" + s + "/&autoPlay=true",
                            s.indexOf("code:") >= 0 && ($.browser.msie ? $.browser.version == "6.0" || $.browser.version == "7.0" || $.browser.version == "8.0" || $.browser.version == "9.0" ? a = "//tudou.com/v/" + s.replace("code:", "") + "/&autoPlay=true" : (f = !0, l = "//www.tudou.com/programs/view/html5embed.action?code=" + s.replace("code:", "") + "&autoPlay=true") : (f = !0, l = "//www.tudou.com/programs/view/html5embed.action?code=" + s.replace("code:", "") + "&autoPlay=true"));
                        break;
                    case "tudou2":
                        u = "tvcCode=-1&hd=2",
                            a = "//js.tudouui.com/bin/lingtong/PortalPlayer_60.swf?tvcCode=-1&hd=2&iid=" + s;
                        break;
                    case "ku6":
                        a = "//player.ku6cdn.com/default/out/pv201109151705.swf?ver=108&vid=" + s + "&type=v&referer=";
                        break;
                    case "sina":
                        a = "//p.you.video.sina.com.cn/swf/bokePlayer20130723_V4_1_42_21.swf?vid=" + s + "&clip_id=&imgurl=&auto=1&vblog=1&type=0&tabad=1&autoLoad=1&autoPlay=1&as=0&tjAD=0&tj=0&casualPlay=1&head=0&logo=0&share=0";
                        break;
                    case "qq":
                        a = "//mat1.qq.com/news/act3/js/QQPlayer3.swf?vid=" + s + "&skin=//mat1.qq.com/news/act3/js/skins/QQPlayerSkin.swf&autoplay=1";
                        break;
                    case "qq2":
                        a = "//imgcache.qq.com/tencentvideo_v1/player/TencentPlayer.swf?_v=20110829&vid=" + s + "&autoplay=1";
                        break;
                    case "pptv":
                        a = s.length > 13 ? "//player.pptv.com/v/" + s + ".swf" : "//player.pptv.com/cid/" + s + ".swf";
                        break;
                    case "sohu":
                    case "sohuvid":
                        a = "//share.vrs.sohu.com/" + s + "/v.swf&skinNum=1&topBar=0&showRecommend=0&autoplay=true&api_key=e68e42f2beae6ba9ad6bd25e2653632f&fbarad=";
                        break;
                    case "sohuid":
                        a = "//share.vrs.sohu.com/my/v.swf&topBar=1&id=" + s + "&autoplay=true&from=page";
                        break;
                    case "letv":
                        a = "//i7.imgs.letv.com/player/swfPlayer.swf?id=" + s + "&autoplay=1&isPlayerAd=0";
                        break;
                    case "letv1":
                        a = s.replace(/&width=\d+/g, "").replace(/&height=\d+/g, ""),
                            f = !0,
                            t() && (n.width = $(window).width(), n.height = $(window).width() * (9 / 16)),
                            l = a + "&width=" + n.width + "&height=" + n.height;
                    case "qingkong":
                        a = "//donghua.dmzj.com/flvplayer.swf?file=//v.qingkong.net/bp/a.php/" + s + ".mp4&autostart=true";
                        break;
                    case "cntv":
                        a = "//player.cntv.cn/standard/cntvOutSidePlayer.swf?videoId=VIDE100165778382&videoCenterId=" + s;
                        break;
                    case "56":
                        a = "//player.56.com/v_" + s + ".swf/1030_ycc20060631.swf";
                        break;
                    case "iqiyi":
                        a = s.replace("&coop=测试&cid=", "") + "&cid=qc_105082_300395&bd=1&autoplay=1&coop=coop_1010_ymxk" + "&source=" + o;
                        l = a;
                        f = !0;
                        break;
                    case "17173":
                        a = "//f.v.17173cdn.com/flash/PreloaderFileFirstpage.swf?cid=" + s + "&refer=";
                        break;
                    case "ac":
                        a = "//static.acfun.mm111.net/player/ACFlashPlayer.out.swf?type=page&url=//www.acfun.tv/v/" + s;
                        break;
                    case "bi":
                        a = "//static.hdslb.com/miniloader.swf?aid=" + s + "&page=1";
                        l = "//player.bilibili.com/player.html?aid=" + s + "&page=1";
                        f = !0;
                        break;
                    case "steam":
                        f = !0;
                        break;
                    default:
                        a = s;
                }
                if (f) {
                    $("#gamersky_player_box").html("");
                    if (i == 'youku') {
                        $.ajaxSetup({ cache: !0 });
                        $.getScript("//player.youku.com/jsapi", function () {
                            player = new YKU.Player("gamersky_player_box", { client_id: "6bfe5b183f11e7d9", vid: s, show_related: !1 });
                        });
                    } else if (i == 'steam') {
                        //	var isMac = navigator.userAgent.indexOf("Intel Mac OS X")!=-1;
                        //	var ss = isMac?s.replace(".webm", '.mp4'):s;
                        $("#gamersky_player_box").html('<video width="' + n.width + '" height="' + n.height + '" controls="" autoplay="" name="media" src="' + s + '"></video>');
                    } else {
                        $("#gamersky_player_box").html('<iframe width="' + n.width + '" height="' + n.height + '" src="' + l + '" frameborder="0" allowfullscreen></iframe>');
                    }
                } else {
                    var p = new SWFObject(a, "gsvobject", n.width, n.height, "9.0.0", "#000000");
                    p.addParam("allowfullscreen", "true");
                    p.addParam("allownetworking", "all");
                    p.addParam("allowscriptaccess", "always");
                    p.addParam("wmode", "opaque");
                    p.addParam("quality", "high");
                    p.addParam("flashvars", u);
                    p.write("gamersky_player_box");
                }
            });
            $(n.videoSource + ".cur").click();
        });
    };

    $.fn.GamerSkyPlayerDefault = function (n) {
        return this.each(function () {
            var $this = $(n.videoSource);
            console.log($this);

            // var $this = $(this);
            $this.addClass("cur").siblings().removeClass("cur");
            var i = n.sitename;
            var s = n.vid;
            var o = $this.attr("data-source") || '';
            var u = "", a = "", f = !1, l = "";
            switch (i) {
                case "youku":
                    a = "//static.youku.com/v1.0.0222/v/swf/player.swf?VideoIDS=" + s;
                    u += "&isShowRelatedVideo=false&amp;showAd=0&amp;show_pre=1&amp;show_next=1&amp;VideoIDS=" + s + "&amp;isAutoPlay=true&amp;isDebug=false&amp;UserID=&amp;winType=interior&amp;playMovie=true&amp;RecordCode=1001,1002,1003,1004,1005,1006,2001,3001,3002,3003,3004,3005,3007,3008,9999";
                    l = "//player.youku.com/embed/" + s;
                    f = !0;
                    break;
                case "tudou":
                    var c = /^\d+$/;
                    u = "tvcCode=-1&hd=2",
                        a = "//tudou.com/v/" + s + "/&autoPlay=true",
                        s.indexOf("code:") >= 0 && ($.browser.msie ? $.browser.version == "6.0" || $.browser.version == "7.0" || $.browser.version == "8.0" || $.browser.version == "9.0" ? a = "//tudou.com/v/" + s.replace("code:", "") + "/&autoPlay=true" : (f = !0, l = "//www.tudou.com/programs/view/html5embed.action?code=" + s.replace("code:", "") + "&autoPlay=true") : (f = !0, l = "//www.tudou.com/programs/view/html5embed.action?code=" + s.replace("code:", "") + "&autoPlay=true"));
                    break;
                case "tudou2":
                    u = "tvcCode=-1&hd=2",
                        a = "//js.tudouui.com/bin/lingtong/PortalPlayer_60.swf?tvcCode=-1&hd=2&iid=" + s;
                    break;
                case "ku6":
                    a = "//player.ku6cdn.com/default/out/pv201109151705.swf?ver=108&vid=" + s + "&type=v&referer=";
                    break;
                case "sina":
                    a = "//p.you.video.sina.com.cn/swf/bokePlayer20130723_V4_1_42_21.swf?vid=" + s + "&clip_id=&imgurl=&auto=1&vblog=1&type=0&tabad=1&autoLoad=1&autoPlay=1&as=0&tjAD=0&tj=0&casualPlay=1&head=0&logo=0&share=0";
                    break;
                case "qq":
                    a = "//mat1.qq.com/news/act3/js/QQPlayer3.swf?vid=" + s + "&skin=//mat1.qq.com/news/act3/js/skins/QQPlayerSkin.swf&autoplay=1";
                    break;
                case "qq2":
                    a = "//imgcache.qq.com/tencentvideo_v1/player/TencentPlayer.swf?_v=20110829&vid=" + s + "&autoplay=1";
                    break;
                case "pptv":
                    a = s.length > 13 ? "//player.pptv.com/v/" + s + ".swf" : "//player.pptv.com/cid/" + s + ".swf";
                    break;
                case "sohu":
                case "sohuvid":
                    a = "//share.vrs.sohu.com/" + s + "/v.swf&skinNum=1&topBar=0&showRecommend=0&autoplay=true&api_key=e68e42f2beae6ba9ad6bd25e2653632f&fbarad=";
                    break;
                case "sohuid":
                    a = "//share.vrs.sohu.com/my/v.swf&topBar=1&id=" + s + "&autoplay=true&from=page";
                    break;
                case "letv":
                    a = "//i7.imgs.letv.com/player/swfPlayer.swf?id=" + s + "&autoplay=1&isPlayerAd=0";
                    break;
                case "letv1":
                    a = s.replace(/&width=\d+/g, "").replace(/&height=\d+/g, ""),
                        f = !0,
                        t() && (n.width = $(window).width(), n.height = $(window).width() * (9 / 16)),
                        l = a + "&width=" + n.width + "&height=" + n.height;
                case "qingkong":
                    a = "//donghua.dmzj.com/flvplayer.swf?file=//v.qingkong.net/bp/a.php/" + s + ".mp4&autostart=true";
                    break;
                case "cntv":
                    a = "//player.cntv.cn/standard/cntvOutSidePlayer.swf?videoId=VIDE100165778382&videoCenterId=" + s;
                    break;
                case "56":
                    a = "//player.56.com/v_" + s + ".swf/1030_ycc20060631.swf";
                    break;
                case "iqiyi":
                    a = s.replace("&coop=测试&cid=", "") + "&cid=qc_105082_300395&bd=1&autoplay=1&coop=coop_1010_ymxk" + "&source=" + o;
                    l = a;
                    f = !0;
                    break;
                case "17173":
                    a = "//f.v.17173cdn.com/flash/PreloaderFileFirstpage.swf?cid=" + s + "&refer=";
                    break;
                case "ac":
                    a = "//static.acfun.mm111.net/player/ACFlashPlayer.out.swf?type=page&url=//www.acfun.tv/v/" + s;
                    break;
                case "bi":
                    a = "//static.hdslb.com/miniloader.swf?aid=" + s + "&page=1";
                    l = "//player.bilibili.com/player.html?aid=" + s + "&page=1";
                    f = !0;
                    break;
                case "steam":
                    f = !0;
                    break;
                default:
                    a = s;
            }

            if (f) {
                $("#" + n.videoContainerId).html("");
                if (i == 'youku') {
                    $.ajaxSetup({ cache: !0 });
                    $.getScript("//player.youku.com/jsapi", function () {
                        player = new YKU.Player(n.videoContainerId, { client_id: "6bfe5b183f11e7d9", vid: s, show_related: !1 });
                    });
                } else if (i == 'steam') {
                    //	var isMac = navigator.userAgent.indexOf("Intel Mac OS X")!=-1;
                    //	var ss = isMac?s.replace(".webm", '.mp4'):s;
                    $("#" + n.videoContainerId).html('<video width="' + n.width + '" height="' + n.height + '" controls="" autoplay="" name="media" src="' + s + '"></video>');
                } else {
                    $("#" + n.videoContainerId).html('<iframe width="' + n.width + '" height="' + n.height + '" src="' + l + '" frameborder="0" allowfullscreen></iframe>');
                }
            } else {
                var p = new SWFObject(a, "gsvobject", n.width, n.height, "9.0.0", "#000000");
                p.addParam("allowfullscreen", "true");
                p.addParam("allownetworking", "all");
                p.addParam("allowscriptaccess", "always");
                p.addParam("wmode", "opaque");
                p.addParam("quality", "high");
                p.addParam("flashvars", u);
                p.write(n.videoContainerId);
            }
        });
    }


})(jQuery);