(function ($) {
    var isTrim = function (s) { return s.replace(/(^\s*)|(\s*$)/g, ""); }; //清除空格



    let pHtm = `
        <div id="conTip">
            ${$('.YXXX-B .div4 .con p').text()}
        </div>
    `
    $('.YXXX-B .div4 .con').append(pHtm)


    if ($('.YXXX-B .div4 .con p')[0].scrollHeight > $('.YXXX-B .div4 .con p')[0].clientHeight) {
        $('.YXXX-B .div4 .con p').css('overflow', 'hidden')
        $('.YXXX-B .div4 .con p').addClass('Ptar')
        $('.YXXX-B .div4 .con p').addClass('Ptar')
    } else {
        $('.YXXX-B .div4 .con p').removeClass('Ptar')
        $('.YXXX-B .div4 .con p').removeClass('Ptar')
    }
    $('.YXXX-B .div4 .con p').mouseenter(function () {
        if ($('.YXXX-B .div4 .con p')[0].scrollHeight == $('.YXXX-B .div4 .con p')[0].clientHeight) {
            return
        }
        $('#conTip').show()
        $('#conTip').css('top', $('.YXXX-B .div4 .con').height() + 10 + 'px')
    })
    $('#conTip').mouseleave(function () {
        $(this).hide()
    })

    if ($('.YXXX-B .div3 .tt1 .titv.issuance')[0].scrollHeight > $('.YXXX-B .div3 .tt1 .titv.issuance')[0].clientHeight) {
        $('.YXXX-B .div3 .tt1 .titv.issuance').css('overflow', 'hidden')
        $('.YXXX-B .div3 .tt1 .titv.issuance').addClass('issuanceTar')
        $('.YXXX-B .div3 .tt1 .titv.issuance').addClass('issuanceTar')
    } else {
        $('.YXXX-B .div3 .tt1 .titv.issuance').removeClass('issuanceTar')
        $('.YXXX-B .div3 .tt1 .titv.issuance').removeClass('issuanceTar')
    }



    let fxHtm = `
        <div id="fxTip">
            ${$('.YXXX-B .div3 .tt1 .titv.issuance').text()}
        </div>
    `
    $('.YXXX-B .div3 .tt1 .titv.issuance').append(fxHtm)
    $('.YXXX-B .div3 .tt1 .titv.issuance').mouseenter(function () {
        if ($('.YXXX-B .div3 .tt1 .titv.issuance')[0].scrollHeight == $('.YXXX-B .div3 .tt1 .titv.issuance')[0].clientHeight) {
            return
        }
        $('#fxTip').show()
        $('#fxTip').css('top', $('.YXXX-B .div3 .tt1 .titv.issuance').height() + 10 + 'px')
    })
    $('#fxTip').mouseleave(function () {

        $(this).hide()
    })

    $('.Mid_T_R').mouseleave(function () {
        $('#conTip').hide()
        $('#fxTip').hide()
    })

    function picswitch(div, nav, con) {
        var $div = $(div),
            arr = [0],
            img = new Image();
        var contains = function (arr, obj) { var i = arr.length; while (i--) { if (arr[i] === obj) { return true; } } return false; } //判断数组相同字符
        var url = $div.find(nav).find("a").eq(0).attr("data-url");
        var src = $div.find(nav).find("a").eq(0).attr("data-pic");
        $div.find(con).html('<a target="_blank" data-src="' + src + '" data-url="' + url + '"  data-index="' + 1 + '"><img src="' + src + '" width="760" height="426" /></a>');
        $div.find(nav).on("click", "a", function (event) {

            event.preventDefault();
            var $this = $(this),
                pic = $this.attr("data-pic"),
                i = $this.parents(nav).find("a").index($this),
                dataurl = $this.attr("data-url");
            if ($this.hasClass("cur")) {
                return;
            }
            $this.addClass("cur").siblings().removeClass("cur");

            if ($this.hasClass('vd')) {
                let vid = $this.attr('data-vid');
                // $('#gamersky_player_box>a>img').hide()
                $('#gamersky_player_box a').GamerSkyPlayer({ width: 760, height: 426, videoSource: ".vd" })

                // $('#gamersky_player_box a').append(`<video width="760" height="426" controls="" autoplay="" name="media"><source src="${vid}" type="video/mp4"> </video>`)
                return

            }

            if (!contains(arr, i)) {
                $div.find(con).html('<img class="loading" src="//image.gamersky.com/webimg15/loading.gif" alt="图片加载中..." />');
            }
            img.onload = function () {
                $div.find(con).html('<a target="_blank" data-src="' + pic + '" data-url="' + dataurl + '" data-index="' + Number(i) + '"><img src="' + pic + '" width="760" height="426" /></a>');
                if (!contains(arr, i)) {
                    arr.push(i);
                }
            };
            img.src = pic;
        });
    }
    if ($(".piclist").length > 0) {
        picswitch(".Mid_T", ".piclist", ".Mid_T_L"); //播放图片
    }

    function cutstr(str, len) {
        var j = 0,
            str_cut = new String(),
            str_len = str.length;
        for (var i = 0; i < str_len; i++) {
            var a = str.charAt(i);
            j++;
            if (escape(a).length > 4) {
                j++;
            }
            str_cut = str_cut.concat(a);
            if (j >= len) {
                return str_cut.concat("...");
            }
        }
        if (j < len) {
            return str;
        }
    }

    function profile(div) {
        var $div = $(div);
        var txt1 = $div.find(".con-hide").text().replace(/　/g, "");
        var txt2 = $div.find(".con-hide").html();
        var a1 = cutstr(txt1, 190).indexOf("...") !== -1 ? '<a href="javascript:;" class="zhanbtn">展开</a>' : '';
        var a2 = '<a href="javascript:;" class="zhanbtn">收起</a>';
        var htm1 = "<p>　　" + cutstr(txt1, 190) + a1 + "</p>",
            htm2 = txt2 + a2;
        $div.find(".con").html(htm1).on("click", ".zhanbtn", function (event) {
            event.preventDefault();
            $div.find(".con").html($(this).html() == "展开" ? htm2 : htm1);
        });
    }
    profile(".YXXX-R .div4");

    function lihover(li) {
        console.log('li')
        var speed = 200;
        $(li).each(function () {
            var $this = $(this),
                hoverTimer,
                outTimer;
            $this.hover(function () {
                console.log('hover')
                clearTimeout(outTimer);
                hoverTimer = setTimeout(function () {
                    $this.addClass("cur");
                }, speed);
            }, function () {
                clearTimeout(hoverTimer);
                outTimer = setTimeout(function () {
                    $this.removeClass("cur");
                }, speed);
            });
        });
    }
    lihover(".Midfx"); //hover分享


    $.fn.extend({
        jscroll: function () {
            return this.each(function () {
                var $this = $(this);
                var dh = $this.height();
                var $jscrollc = $this.find(".jscroll-c");
                var $jscrolle = $this.find(".jscroll-e");
                var $jscrollh = $this.find(".jscroll-h");
                var sch = $jscrollc.height();
                var sh = Math.pow(dh, 2) / sch;
                var wh = sh / 6;
                var curT = 0;
                var allowS = false;
                $jscrollh.height(sh);
                if (sch <= dh) {
                    $jscrolle.hide();
                } else {
                    $jscrolle.show();
                    allowS = true;
                }

                function setT() {
                    if (curT < 0) {
                        curT = 0;
                    }
                    if (curT > dh - sh) {
                        curT = dh - sh;
                    }
                    $jscrollh.css({ "marginTop": curT == 0 ? "" : curT });
                    var scT = -(curT * sch / dh);
                    scT = scT > 0 ? 0 : scT;
                    $jscrollc.css({ "marginTop": scT });
                };
                $jscrollh.on("mousedown", function (e) {
                    var pageY = e.pageY,
                        t = parseInt($(this).css("marginTop"));
                    $(document).mousemove(function (e) {
                        curT = t + e.pageY - pageY;
                        setT();
                    }).mouseup(function () {
                        $(this).unbind();
                    });
                    return false
                });
                $this.mousewheell(function () {
                    if (allowS != true) {
                        return
                    }
                    if (this.D > 0) {
                        curT -= wh
                    } else {
                        curT += wh
                    }
                    setT()
                })
            })
        },
        mousewheell: function (Func) {
            return this.each(function (ev) {
                this.D = 0;
                if (navigator.userAgent.indexOf("Firefox") > 0) {
                    this.addEventListener("DOMMouseScroll", function (ev) {
                        this.D = ev.detail > 0 ? -1 : 1;
                        ev.preventDefault();
                        Func && Func.call(this)
                    }, false)
                } else {
                    this.onmousewheel = function (ev) {
                        ev = window.event || ev;
                        this.D = ev.wheelDelta;
                        ev.returnValue = false;
                        Func && Func.call(this)
                    }
                }
            })
        }
    });

    function SwipingMouseover(div, nav, conli) { //通用一级切换

        var $div = $(div),
            setTimer, speed = 0; //延迟0.2秒
        $div.find(conli).eq(0).show();
        $("#demo0").jscroll(".jscroll-c", ".jscroll-e", ".jscroll-h");
        $div.find(nav).on("click", "a", function (event) {
            event.preventDefault();
            var $this = $(this),
                i = $this.parents(nav).find("a").index($this);
            setTimer = setTimeout(function () {
                $this.parents(nav).find("a").removeClass("cur").eq(i).addClass("cur");
                $div.find(conli).removeClass("block").addClass("none").removeAttr("style").eq(i).show().toggleClass("none block");
                if ($(conli).eq(i).hasClass("pic")) {
                    $("#demo" + i).jscroll(".jscroll-c", ".jscroll-e", ".jscroll-h");
                    var dataurl = $(".Mid_T .piclist a.cur").attr("data-url");
                    $("#gamersky_player_box").html('<a target="_blank" data-src="' + dataurl + '" data-index="' + i + '"><img src="' + $(".Mid_T .piclist a.cur").attr("data-pic") + '" width="760" height="426" /></a>');
                }
                if ($(conli).eq(i).hasClass("video")) {
                    $("#demo1").jscroll(".jscroll-c", ".jscroll-e", ".jscroll-h");
                    $("#gamersky_player_box").GamerSkyPlayer({ width: 760, height: 426, videoSource: ".vd" });
                }
            }, speed);
        }).on("mouseout", "a", function () {
            clearTimeout(setTimer);
        });
    }
    SwipingMouseover(".Mid_T_R", ".PVnav", ".PVcon"); //图片视频 切换
    SwipingMouseover(".PZcont", ".PZnav", ".PZcon .PZ"); //配置需求 切换

    function changeBgHeight(flag) {
        let hei = $('.tit_CH').innerHeight()
        if (flag) { // 展开
            $('.Mid_T').show();
            $('.Top-bg').css('height', 616 + hei + 'px')
            $('.Top-bg-mask').css('height', 616 + hei + 'px')


        } else { // 收起
            $('.Mid_T').hide();
            $('.Top-bg').css('height', 170 + hei + 'px')
            $('.Top-bg-mask').css('height', 170 + hei + 'px')
        }
    }

    function clickOnline(nav, a, con) {
        var $nav = $(nav),
            $con = $(con);
        if (a == "a.dp") {
            $nav.find("a.dp").removeAttr("onclick");
        }
        if (a == "a.hd" && $("div").hasClass("activity")) {
            if ($('.tgFenxiaoMini').length > 1) {
                $('<a href="javascript:;" class="hd"></a>').insertBefore($('.tgFenxiaoMini'));
            } else {
                $nav.append('<a href="javascript:;" class="hd"></a>');
            }

        }
        $nav.on("click", a, function (event) {

            event.preventDefault();
            $nav.find("a").removeClass("cur").eq(0).addClass("cur");
            $con.removeClass("block").addClass("none").eq(0).toggleClass("none block");

            if ($(event.target).text().indexOf('综合') > -1) {
                changeBgHeight(true)

            } else {
                changeBgHeight(false)

            }
            let text = $(event.target).text().trim()
            console.log(text)

            if ($(`.MidLcon[tit="${text}"]`).length > 0 && $(`.MidLcon[tit="${text}"]`).html().indexOf('没有任何记录') > -1) {
                $(`.MidLcon[tit="${text}"]`).addClass('noData')
                $(`.MidLcon[tit="${text}"] .pictxt`).html('')
            }
            // $('html,body').animate({
            //     scrollTop: $('.lxbdr').offset().top
            // }, 400);

        });
    }
    clickOnline(".Midnav", "a", ".MidLcon"); //点评切换
    // clickOnline(".Midnav", "a", ".MidLcon"); //活动切换

    function SwipingClick(nav, con) { //通用一级切换
        var $nav = $(nav),
            $con = $(con);
        $nav.on("click", ".a", function (event) {
            event.preventDefault();
            var $this = $(this),
                i = $nav.find(".a").index($this);
            $nav.find(".a").removeClass("cur").eq(i).addClass("cur");
            $con.removeClass("block").addClass("none").eq(i).toggleClass("none block");
        });
    }
    SwipingClick(".Midnav", ".MidLcon"); //图片视频 切换

    $.fn.Slide = function (a) {
        var g = this,
            F = $.extend(a),
            D = F.effect,
            h = F.delayTime,
            x = F.autoPlay,
            d = F.interTime,
            p = F.prevCell,
            q = F.nextCell,
            G = F.bigPic,
            j = F.smallPic,
            l,
            w = "",
            A = g.find(p),
            E = g.find(q),
            b = g.find(G),
            C = b.find("li"),
            u = g.find(j),
            f = C.outerWidth(true),
            z = C.length,
            y = 0;
        for (var o = 0; o < z; o++) {
            w += "<a href='javascript:;' class='" + (o == 0 ? "cur" : "") + "'></a>"
        }
        g.find(j).html(w);
        switch (D) {
            case "fold":
                for (var r = 0; r < z; r++) {
                    C.eq(r).css({
                        display: r == 0 ? "list-item" : "none",
                        position: "absolute",
                        top: 0,
                        left: 0
                    })
                }
                break;
            case "leftLoop":
                var v = b.find("li:first").clone().addClass("clone"),
                    k = b.find("li:last-child").clone().addClass("clone");
                b.css({
                    width: f * z + f * 2,
                    position: "absolute",
                    top: 0,
                    left: -f
                }).append(v).prepend(k);
                break
        }
        var B = function (c) {
            switch (D) {
                case "fold":
                    C.stop(!1, !0).fadeOut(h).eq(y).fadeIn(h);
                    u.find("a").removeClass("cur").eq(y).addClass("cur");
                    break;
                case "leftLoop":
                    ("" == c) && b.stop(!1, !0).animate({
                        left: -(f + f * y)
                    }, h);
                    (p == c) && b.stop(!1, !0).animate({
                        left: -(f * (y == z - 1 ? 0 : y + 1))
                    }, h, function () {
                        y == z - 1 && b.css({
                            left: -(f * z)
                        })
                    });
                    (q == c) && b.stop(!1, !0).animate({
                        left: -(f + f * (y == z ? z : y == 0 ? z : y))
                    }, h, function () {
                        y == 0 && b.css({
                            left: -f
                        })
                    });
                    u.find("a").removeClass("cur").eq(y).addClass("cur");
                    break
            }
        };
        var m = function () {
            l = setInterval(function () {
                y == z - 1 ? y = 0 : y++;
                B(q)
            }, d)
        };
        1 == x && m();
        g.hover(function () {
            clearInterval(l)
        }, function () {
            1 == x && m()
        });
        g.on("click", p, function () {
            y = y == 0 ? z - 1 : y - 1;
            B(p)
        }).on("click", q, function () {
            y = y == z - 1 ? 0 : y + 1;
            B(q)
        });
        u.on("click", "a", function () {
            0 == $(this).hasClass("cur") && (y = $(this).index(), B(""))
        })
    };

    $(".Slide").Slide({
        bigPic: ".Bimg", //大图
        smallPic: ".Simg", //小图
        autoPlay: true, //true:自动播放 false:停止自动播放
        interTime: 5000, //自动播放时间
        effect: "leftLoop", //fold:渐变 leftLoop:向左循环移动
        delayTime: 300, //渐变或移动速度
        prevCell: ".Prev",
        nextCell: ".Next"
    });

    $.fn.Slidepic = function (ul, li) {
        return this.each(function () {
            var $this = $(this),
                i = 0,
                length = $this.find(ul).find(li).length,
                len = length % 2 == 0 ? length / 2 - 1 : parseInt(length / 2);
            $this.find(ul).width($this.find(".con").width() * (len + 1));
            if (length > 2) {
                $this.find(".Next").addClass("cur");
            }
            var liwArr = [],
                liw = 0;
            $this.find(ul).find(li).each(function (j) {
                var w = $(this).outerWidth(true);
                liw += j == 0 ? 0 : w * 2 - (j == len ? 70 + (length % 2 == 0 ? 0 : w) : 0);
                liwArr[j] = liw;
            });

            var ymxkmove = function () {
                $this.find(ul).stop(false, true).animate({ "left": -liwArr[i] }, 300);
                if (i == 0) {
                    $this.find(".Prev").removeClass("cur").end().find(".Next").addClass("cur");
                } else if (i == len) {
                    $this.find(".Prev").addClass("cur").end().find(".Next").removeClass("cur");
                } else {
                    $this.find(".Prev").addClass("cur").end().find(".Next").addClass("cur");
                }
            }
            $this.on("click", ".Prev", function () {
                if (i > 0) {
                    i--;
                    ymxkmove();
                }
            }).on("click", ".Next", function () {
                if (i < len) {
                    i++;
                    ymxkmove();
                }
            });
        });
    }
    $(".Slidepic").Slidepic(".Bimg", "li");

    $.fn.Slides = function (ul, li) {
        return this.each(function () {
            var $this = $(this),
                i = 0,
                w = $this.find(ul).find(li).width(),
                len = $this.find(ul).find(li).length;
            var txt = $this.find(ul).find("a").eq(0).attr("data-txt"),
                url = $this.find(ul).find("a").eq(0).attr("data-url");
            $this.find(ul).width(w * (len + 1)).find("a").eq(0).addClass("cur");
            if (isTrim(txt)) {
                $this.find(".MTPF-infor span").html(cutstr(txt, 300));
            }
            if (isTrim(url)) {
                $this.find(".MTPF-infor a").attr("href", url).css("display", !isTrim(url) ? "" : "inline-block");
            }
            if (len > 1) {
                $this.find(".Next").addClass("cur");
            }
            var liwArr = [],
                liw = 0;
            $this.find(ul).find(li).each(function (j) {
                liw += j == 0 ? 0 : $(this).outerWidth(true);
                liwArr[j] = liw;
            });

            var ymxkmove = function () {
                $this.find(ul).stop(false, true).animate({ "left": -liwArr[i] }, 300);
                if (i == 0) {
                    $this.find(".Prev").removeClass("cur").end().find(".Next").addClass("cur");
                } else if (i == len - 1) {
                    $this.find(".Prev").addClass("cur").end().find(".Next").removeClass("cur");
                } else {
                    $this.find(".Prev").addClass("cur").end().find(".Next").addClass("cur");
                }
            }
            $this.on("click", ".Prev.cur", function () {
                var dataurl = $this.find(ul).find("li").eq(0).find("a.cur").attr("data-url");
                var datatxt = $this.find(ul).find("li").eq(0).find("a.cur").attr("data-txt");
                $this.find(".MTPF-infor a").attr("style", dataurl ? "display:inline-block" : "display:inline-none");
                $this.find(".MTPF-infor span").html(cutstr(datatxt, 300));
                if (i > 0) {
                    i--;
                    ymxkmove();
                }
            }).on("click", ".Next.cur", function () {
                var dataurl = $this.find(ul).find("li").eq(1).find("a").eq(0).attr("data-url");
                var datatxt = $this.find(ul).find("li").eq(1).find("a").eq(0).attr("data-txt");
                if (datatxt == '' || typeof (datatxt) == "undefined") {
                    $this.find(ul).find("li").eq(1).find("a").eq(0).removeClass("cur");
                }
                $this.find(".MTPF-infor a").attr("style", dataurl ? "display:inline-block" : "display:inline-none");
                $this.find(".MTPF-infor span").html(cutstr(datatxt, 300));
                if (i < len - 1) {
                    i++;
                    ymxkmove();
                }
            });
            var setTimer, second = 200; //延迟0.2秒
            $this.find(ul).on("mouseover", "a", function () {
                var $thia = $(this),
                    datatxt = $thia.attr("data-txt"),
                    dataurl = $thia.attr("data-url");
                if (!isTrim(datatxt)) {
                    return;
                }
                setTimer = setTimeout(function () {
                    $thia.addClass("cur").siblings().removeClass("cur");
                    $this.find(".MTPF-infor span").html(cutstr(datatxt, 300));
                    $this.find(".MTPF-infor a").attr("href", dataurl).css("display", !isTrim(dataurl) ? "" : "inline-block");
                }, second);
            }).mouseout(function () {
                clearTimeout(setTimer);
            });
        });
    }
    $(".MTPF").Slides(".Bimg", "li"); //图组切换
    HomeScrollR(".Mid_L", ".Mid_R"); //左侧滚动

    if ($(".jpdwlist").length > 0) {
        // $(".jpdwlist").html($(".jpdwlist").html().replace("没有任何记录", ""))
    }

    if (!$(".PVnav a").hasClass("a1")) {
        $(".PVnav .a2").trigger("mouseover");
    }

    /*游民小调查*/
    $.fn.contentVote = function () {
        var $this = $(this);
        var voteid = $this.attr("data-voteid") | 0;
        if (voteid > 0) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//db5.gamersky.com/ContentVoteJsonp.aspx",
                data: { id: voteid, a: "0" },
                success: function (data) {
                    if (data.status == "ok") {
                        var starTime = data.vote.Time1;
                        var endTime = data.vote.Time2;
                        var now = data.vote.NowTime;
                        var VoteTitle = data.vote.VoteTitle;
                        var length = data.items.length;
                        if (starTime > now || endTime < now) {
                            return;
                        }
                        $("#ymtc").show();
                        $this.show().find(".tit").text(VoteTitle).attr("itemType", data.vote.ItemType);
                        var html = "";
                        for (var i = 0; i < length; i++) {
                            html += '<a class="a" href="javascript:;" data-vid="v' + i + '">' + data.items[i].Title + '</a>';
                        }
                        $(".optionslist").html(html);
                        $(".optionslist").on("click", "a", function (event) {
                            event.preventDefault();
                            var itemType = $this.find(".tit").attr("itemType");
                            if (itemType > 0) {
                                $(this).toggleClass("cur");
                            } else {
                                $(this).addClass("cur").siblings().removeClass("cur");
                            }

                        });
                    }
                }
            });
        }
    };
    $(".votelist").contentVote();
    $("#btnVote").click(function () {
        var voteid = $("[data-voteid]").attr("data-voteid");
        var issuccess = true;
        var checkedElement = $(".votelist li.txt a.cur");
        var txt = checkedElement.html();
        if (checkedElement.length == 0) {
            alert("请选择投票项！");
            issuccess = false;
            return false;
        }
        if (checkedElement.length == 1) {
            txt = $(".votelist li.txt a.cur").html();
        }
        if (checkedElement.length > 1) {
            for (var n = 1; n < checkedElement.length; n++) {
                var $checkedElement = $(checkedElement[n]);
                txt = txt + ",";
                txt = txt + $checkedElement.html();
            }
        }
        if (!issuccess) {
            return;
        }
        $.ajax({
            type: "GET",
            dataType: "jsonp",
            url: "//db5.gamersky.com/ContentVoteJsonp.aspx",
            data: { id: voteid, a: "1", v: txt },
            success: function (responseJson) {
                switch (responseJson.status) {
                    case "ok":
                        alert("感谢你的投票");
                        break;
                    case "err":
                        alert(responseJson.message);
                        break;
                }
            }
        });
    });

    var len = $("#like .Bimg li").length;
    if (len <= 3) {
        $("#like").hide().siblings(".MidRtit").hide();
    }

    $.fn.titList = function (options) {
        return this.each(function () {
            if ($(this).find("li").length > 0) {
                $(this).show();
                if ($(this).find(".News").attr("href") == '') {
                    $(this).find(".News").click(function (event) {
                        event.preventDefault();
                        if ($(".Midnav .a").hasClass("a2")) {
                            $("html,body").animate({ scrollTop: $(".Midnav").offset().top }, 400);
                            $(".Midnav .a").removeClass("cur").filter(".a2").addClass("cur");
                            $(".MidLcon").removeClass("block").addClass("none").eq(1).toggleClass("none block");
                        }
                    });
                }
                if ($(this).find(".Handbook").attr("href") == '') {
                    $(this).find(".Handbook").click(function (event) {
                        event.preventDefault();
                        if ($(".Midnav .a").hasClass("a3")) {
                            $("html,body").animate({ scrollTop: $(".Midnav").offset().top }, 400);
                            $(".Midnav .a").removeClass("cur").filter(".a3").addClass("cur");
                            $(".MidLcon").removeClass("block").addClass("none").eq(2).toggleClass("none block");
                        }
                    });
                }
                if ($(this).find(".SoftDown").attr("href") == '') {
                    $(this).find(".SoftDown").click(function (event) {
                        event.preventDefault();
                        if ($(".Midnav .a").hasClass("a4")) {
                            $("html,body").animate({ scrollTop: $(".Midnav").offset().top }, 400);
                            $(".Midnav .a").removeClass("cur").filter(".a4").addClass("cur");
                            $(".MidLcon").removeClass("block").addClass("none").eq(3).toggleClass("none block");
                        }
                    });
                }
            }
        })
    };
    $(".tit-list").titList();

    $('body').on('click', '.tgFenxiaoMini,.Mid_L .YXXX .YXXX-L .btn a', function (e) {
        $.ajax({
            type: "GET",
            dataType: "jsonp",
            url: '//click.gamersky.com/Common/GetHits.aspx',
            data: { id: '1186813', script: "3" },
            success: function (data) { }
        });
    });
    $('#gamersky_player_box').click(function () {
        let ths = $(this).find('a').eq(0)
        let src = ths.attr('data-src')
        let url = ths.attr('data-url')
        let index = ths.attr('data-index')
        console.log('gamersky_player_box', src, url, index)

        if (ths.attr('data-type') == 'video') {
            previewVideoFunc(src)
        } else {
            previewImgFunc(src, url, index, $('.piclist'))
        }
    })
    let imglength = $('.Mid_T_LL  .piclist a').length;
    if (imglength <= 4) {
        $('.nbtn-next').hide()
        $('.nbtn-prev').hide()
    }
    if (imglength == 0) {
        $('.Mid_T_LL').addClass('noData')
    }
    // 幻灯小图片下一个
    $('.nbtn-next').click(function (event) {
        $('.Mid_T .piclist').css('transform', 'translateX(-154px)')
        $(this).addClass('disable')
        $('.nbtn-prev').removeClass('disable')
    })
    // 幻灯小图片上一个
    $('.nbtn-prev').click(function (event) {

        $('.Mid_T .piclist').css('transform', 'translateX(0px)')
        $(this).addClass('disable')
        $('.nbtn-next').removeClass('disable')

    })
    // 查看更多图片

    $('.Mid_T .PVcon .more').click(function () {
        $('.preview_ImgsBg').show();
        $('.preview_Imgs').show()
        let curList = $('.img_Categories .category_n.cur')[0]
        // $(curList).attr('page-index', 0)
        $(curList).attr('has-more', true)


        $('.category_n').click(function () {
            let cate = $(this).attr('data-catrgory');

            $(this).addClass('cur').siblings().removeClass('cur')
            $('.preview_Imgs_list.' + cate).show().siblings('.preview_Imgs_list').hide()
            if ($(this).attr('page-index') == undefined) {
                $(this).attr('page-index', 0)
            }
            if ($(this).attr('page-index') == 0) {
                $(this).attr('has-more', true)
                getNextPageImgs()
            } else {
                console.log($('.preview_Imgs_list.' + cate))
                $('.preview_Imgs_list.' + cate)[0].scrollTop = 0
            }

        })
        $('.preview_ImgsBg').click(function () {

            $('.preview_ImgsBg').hide();
            $('.preview_Imgs').hide()
        })




    })
    // 预览图片
    function previewImgFunc(src, url, index, parent) {
        let num = 0

        if (parent.hasClass('piclist')) {
            num = 4
        } else {
            num = $(parent)[0].children.length
        }

        if (index >= num) {
            $('.preview_box_next').addClass('disable')
            $('.preview_box_prev').removeClass('disable')

        } else if (index <= 1) {
            $('.preview_box_prev').addClass('disable')
            $('.preview_box_next').removeClass('disable')

        } else {
            $('.preview_box_next').removeClass('disable').show()
            $('.preview_box_prev').removeClass('disable').show()
        }
        $('.preview_box img').attr('src', src).attr('data-url', url).show();
        $('.preview_box').attr('href', url).css('display', 'block');

        $('.preview_box video').hide()
        $('.preview_box_nums').text(index + '/' + num).show()
        $('.preview_boxBg').show()
        $('.preview_box_prev:not(".disable")').off('click').click(function () {
            previewImgChanger('prev', index, parent)
            return false

        })
        $('.preview_box_next:not(".disable")').off('click').click(function (event) {
            previewImgChanger('next', index, parent)
            return false

        })

    }
    // 切换图片
    function previewImgChanger(flag, currentIndex, parent) {
        console.log('currentIndex', currentIndex, parent)
        if (flag == 'next') {
            if ($(parent).hasClass('piclist')) {
                let node = $(parent).eq(0).children()[Number(Number(currentIndex))];
                let src = $(node).attr('data-pic')
                let url = $(node).attr('data-url')
                console.log('currentIndex', currentIndex, 'src', src, 'url', url)
                previewImgFunc(src, url, Number(Number(currentIndex) + 1), parent)
            } else {
                $(parent)[0].children[currentIndex].click()

            }

        } else {
            if ($(parent).hasClass('piclist')) {
                let node = $(parent).eq(0).children()[Number(Number(currentIndex) - 2)];
                let src = $(node).attr('data-pic')
                let url = $(node).attr('data-url')
                console.log('currentIndex', currentIndex, 'src', src, 'url', url)
                previewImgFunc(src, url, Number(Number(currentIndex) - 1), parent)
            } else {
                $(parent)[0].children[currentIndex - 2].click()
            }
        }

    }
    // 预览视频
    function previewVideoFunc(vid, sitename) {
        $('.preview_box_nums').hide()
        $('.preview_box_prev').hide()
        $('.preview_box_next').hide()
        $('.preview_boxBg').show()
        $('.preview_box').css('display', 'block').attr('href', vid);
        $('.preview_box').GamerSkyPlayerDefault({ width: 1152, height: 700, vid, sitename, videoSource: ".spvd", videoContainerId: "preview_box" });
        if (vid.indexOf('youku') > -1) {
            $('.preview_box video').hide();
        } else {
            $('.preview_box video').show();
        }
        // $('.preview_box video').append(`<source src="${src}" type="video/mp4">`).show()
        $('.preview_box img').hide()

    }
    // 关闭预览
    $('.preview_boxBg').click(function () {
        $('.preview_box img').attr('src', '');
        $('.preview_box video').attr('src', '');
        $('.preview_boxBg').hide()
        $('.preview_box').hide();
    })

    // 更多图片弹窗
    $('.preview_Imgs_list').each((index, list) => {
        getNextPageImgs()
        $(list).on('scroll', function () {
            let curList = $('.img_Categories .category_n.cur')[0]
            if ($(curList).attr('has-more') && !nextPageLoading && isElementScrolledToBottom(list)) {
                getNextPageImgs()
            }

        })
    })
    var gameId = $('#jcjbContentData').attr('data-generalid') || $('.YXXX .tit_CH').attr('gameid')
    var nextPageLoading = false;


    function getImgs() {

        let curList = $('.img_Categories .category_n.cur')[0];
        var dataCatrgory = $(curList).attr("data-catrgory");
        let listPageIndex = $(curList).attr("page-index") || 0;
        nextPageLoading = true;
        $.ajax({
            type: "GET",
            dataType: "json",
            url: "//router6.gamersky.com/@/lists/getListElements/6.27.10/2516504/App_iOS",
            data: {
                "gameId": gameId,
                'listName': '游戏库/游戏图像列表',
                "recommend": 0,
                "pageIndex": listPageIndex,
                "pageSize": 16,
                "contentType": $(curList).text()
            },
            success: function (data) {
                let result = data.listElements;
                // let imgList = $(".preview_Imgs_list." + dataCatrgory + "")
                // 是否还有更多
                if (result.length < 16) {
                    $(curList).attr('has-more', false)
                } else {
                    $(curList).attr('has-more', true)

                }
                if ($(curList).text() == '视频') {
                    if (result.length > 0) {
                        var html = "";
                        $.each(result, function (index, value) {
                            var timestamp = value.publishTimeCaption;
                            // data-vid=\"" + value.contentUrl + "\"
                            html += "<div class=\"sp_item spvd\" data-sitename=\"youku\" data-vid=\"XNjQ1ODgxMTkyMA==\">";
                            html += "<img src=\"" + value.thumbnailUrls[0] + "\" alt=\"\">";
                            html += "<div class=\"sp_info\">";
                            html += "<div class=\"sp_info_tit\">" + value.title + "</div>";
                            html += "<div class=\"sp_info_time\">" + timestamp + "</div>";
                            html += "</div>";
                            html += "</div>";
                        });
                        $(".preview_Imgs_list.sp").html(html);
                        $(curList).attr('page-index', Number(listPageIndex) + 1)
                        if ($('.preview_Imgs_list.sp .sp_item').length >= data.allListElementsCount) {
                            $(curList).attr('has-more', false)

                        }
                    }
                    else if (result.length == 0 && listPageIndex == 0) {
                        $(".preview_Imgs_list.sp").addClass("no_data");
                        $(curList).attr('has-more', false)

                    }
                    // 视频预览
                    $('.sp_item').off('click').click(function (e) {
                        let src = $(this).attr('data-vid')
                        previewVideoFunc(src)



                    })
                } else {

                    if (result.length > 0) {
                        var html = "";
                        $.each(result, function (index, value) {
                            if (value.thumbnailUrls.length > 0) {
                                html += "<img src=\"" + value.thumbnailUrls[0] + "\" data-url=\"https://www.gamersky.com/showimage/id_gamersky.shtml?" + value.contentUrl + "\" data-pic=\"" + value.contentUrl + "\" alt=\"\">";
                            }
                        });
                        $(".preview_Imgs_list." + dataCatrgory + "").append(html);
                        $(curList).attr('page-index', Number(listPageIndex) + 1)
                    } else if (listPageIndex == 0) {
                        $(".preview_Imgs_list." + dataCatrgory + "").addClass("no_data");
                    }
                    if ($(".preview_Imgs_list." + dataCatrgory + " img").length >= result.allListElementsCount) {
                        $(curList).attr('has-more', false)

                    }
                    // 图片预览
                    $('.preview_Imgs_list:not(".sp") img').off('click').click(function () {
                        $('.preview_box img').src = ''
                        let src = $(this).attr('data-pic')
                        let total = $(this).parent('.preview_Imgs_list')
                        let url = $(this).attr('data-url')
                        previewImgFunc(src, url, $(this).index() + 1, $(this).parent('.preview_Imgs_list'))
                    })
                }

                console.log($(".preview_Imgs_list." + dataCatrgory + " img").length)


                nextPageLoading = false;
            }

        })
    }
    getImgs()
    function getNextPageImgs() {
        let curList = $('.img_Categories .category_n.cur')[0];
        var dataCatrgory = $(curList).attr("data-catrgory");
        let listPageIndex = $(curList).attr("page-index") || 0;
        let hasMore = $(curList).attr("has-more");


        if (!hasMore || nextPageLoading) {
            return
        }
        nextPageLoading = true;
        $.ajax({
            type: "GET",
            dataType: "json",
            url: "//router6.gamersky.com/@/lists/getListElements/6.27.10/2516504/App_iOS",
            data: {
                "gameId": gameId,
                'listName': '游戏库/游戏图像列表',
                "recommend": 0,
                "pageIndex": listPageIndex,
                "pageSize": 16,
                "contentType": $(curList).text()
            },
            success: function (data) {
                let result = data.listElements;
                // let imgList = $(".preview_Imgs_list." + dataCatrgory + "")
                // 是否还有更多
                if (result.length < 16) {
                    $(curList).attr('has-more', false)
                } else {
                    $(curList).attr('has-more', true)

                }
                if ($(curList).text() == '视频') {
                    if (result.length > 0) {
                        var html = "";
                        console.log(result);

                        $.each(result, function (index, value) {
                            var timestamp = value.publishTimeCaption;
                            // data-vid=\"" + value.contentUrl + "\"

                            var sitename = "steam";
                            var vid = "";
                            if (value.contentUrl.indexOf('youku') > -1) {
                                sitename = "youku";
                                if (value.contentUrl.indexOf("==.html") !== -1) {
                                    vid = value.contentUrl.match(/id_(\S*)==/)[1] + '==';
                                } else if (value.contentUrl.indexOf("==/v.swf") !== -1) {
                                    vid = value.contentUrl.match(/sid\/(\S*)==/)[1] + '==';
                                } else if (value.contentUrl.indexOf("embed/") !== -1) {
                                    vid = value.contentUrl.match(/embed\/(\S*)==/)[1] + '==';
                                } else if (value.contentUrl.indexOf("vid=") !== -1) {
                                    if (value.contentUrl.match(/vid=(\S*)==/) != null) {
                                        vid = value.contentUrl.match(/vid=(\S*)==/)[1] + '==';
                                    }
                                    if (vid == "") {
                                        vid = value.contentUrl.match(/vid=(\S*)%3D%3D/)[1] + '==';
                                    }
                                }
                            }

                            html += "<div class=\"sp_item spvd\" data-sitename=\"" + sitename + "\" data-vid=\"" + (vid == "" ? value.contentUrl : vid) + "\" >";
                            html += "<img src=\"" + value.thumbnailUrls[0] + "\" alt=\"\">";
                            html += "<div class=\"sp_info\">";
                            html += "<div class=\"sp_info_tit\">" + value.title + "</div>";
                            html += "<div class=\"sp_info_time\">" + timestamp + "</div>";
                            html += "</div>";
                            html += "</div>";

                        });
                        $(".preview_Imgs_list.sp").html(html);
                        $(curList).attr('page-index', Number(listPageIndex) + 1)
                        if ($('.preview_Imgs_list.sp .sp_item').length >= data.allListElementsCount) {
                            $(curList).attr('has-more', false)

                        }
                    }
                    else if (result.length == 0 && listPageIndex == 0) {
                        $(".preview_Imgs_list.sp").addClass("no_data");
                        $(curList).attr('has-more', false)

                    }
                    // 视频预览
                    $('.sp_item').off('click').click(function (e) {
                        let vid = $(this).attr('data-vid');
                        let sitename = $(this).attr('data-sitename');
                        previewVideoFunc(vid, sitename)

                    })
                } else {

                    if (result.length > 0) {
                        var html = "";
                        $.each(result, function (index, value) {
                            if (value.thumbnailUrls.length > 0) {
                                html += "<img src=\"" + value.thumbnailUrls[0] + "\" data-url=\"https://www.gamersky.com/showimage/id_gamersky.shtml?" + value.contentUrl + "\" data-pic=\"" + value.contentUrl + "\" alt=\"\">";
                            }
                        });
                        $(".preview_Imgs_list." + dataCatrgory + "").append(html);
                        $(curList).attr('page-index', Number(listPageIndex) + 1)
                    } else if (listPageIndex == 0) {
                        $(".preview_Imgs_list." + dataCatrgory + "").addClass("no_data");
                    }
                    if ($(".preview_Imgs_list." + dataCatrgory + " img").length >= result.allListElementsCount) {
                        $(curList).attr('has-more', false)

                    }
                    // 图片预览
                    $('.preview_Imgs_list:not(".sp") img').off('click').click(function () {
                        $('.preview_box img').src = ''
                        let src = $(this).attr('data-pic')
                        let total = $(this).parent('.preview_Imgs_list')
                        let url = $(this).attr('data-url')
                        previewImgFunc(src, url, $(this).index() + 1, $(this).parent('.preview_Imgs_list'))
                    })
                }

                console.log($(".preview_Imgs_list." + dataCatrgory + " img").length)


                nextPageLoading = false;
            }

        })
    }

    function isElementScrolledToBottom(element) {
        // scrollHeight 是元素内容的总高度
        // scrollTop 是元素已经滚动的像素数
        // clientHeight 是元素视口的高度
        return element.scrollHeight - element.scrollTop <= element.clientHeight + 20;
    }

    $(document).ready(function () {
        var isNull = true;
        $(".PZXQ .PZ.TJ .txt span").each(function () {
            if ($(this).html() != "--" && $(this).html() != "-") {
                isNull = false;
            }
        });
        if (isNull) {
            $(".PZnav .PZnav_box").hide();
        }
    });


})(jQuery);