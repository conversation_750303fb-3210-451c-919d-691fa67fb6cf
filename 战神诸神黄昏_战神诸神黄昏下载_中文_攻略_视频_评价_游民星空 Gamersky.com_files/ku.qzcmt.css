.qzcmt {
    padding: 4px 0;
    font-size: 14px;
    font-family: 'Microsoft YaHei', Arial, SimSun;
    background-color: #fff;
    border-radius: 4px;
    overflow: visible;
}

.qzcmt img {
    border: 0;
}

.qzcmt input,
.qzcmt textarea {
    outline: 0;
}

.qzcmt ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.qzcmt ul li {
    list-style-type: none;
    vertical-align: middle;
}

.qzcmt .none {
    display: none;
}

.qzcmt .block {
    display: inline-block;
}

.qzcmt-not {
    padding: 20px 0;
    width: auto;
    height: auto;
    color: #666;
    font-size: 16px;
    text-align: center;
    border-top: 1px solid #eee;
}

/*导航*/
.qzcmt-cont {
    margin-top: -1px;
    padding: 20px 0 0 70px;
    overflow: visible;
    background-color: #fff;
    border-top: 1px solid #eee;
    /* margin-bottom: 15px; */
    position: relative;
}

.qzcmt-head {
    width: 50px;
    height: 50px;
    overflow: visible;
    position: absolute;
    left: 0;
    top: 20px;
    z-index: 1;
}

.qzcmt-head a {
    display: inline-block;
}

.qzcmt-head img {
    display: block;
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.qzcmt-head.cur {
    z-index: 10000;
}

.qzcmt-head.cur .headlayer {
    display: block;
    animation: fadeInUp .2s ease-in-out 0s alternate;
}

.qzcmt-cont-top {
    width: 100%;
    height: 60px;
    overflow: visible;
}

.qzcmt-cont-top .qzcmt-user {
    width: 100%;
    height: auto;
    overflow: visible;
    position: relative;
}

.qzcmt-cont-top .user-name {
    float: left;
    margin-top: 15px;
    height: 26px;
    overflow: visible;
}

/*用户名*/
.qzcmt-cont-top .user-name a {
    float: left;
    margin-right: 8px;
    display: inline-block;
}

.qzcmt-cont-top .user-name a.uname {
    width: auto;
    height: 22px;
    line-height: 22px;
    color: #444;
    font-size: 15px;
    font-weight: bold;
    max-width: 120px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.qzcmt-cont-top .user-name a.uname:hover {
    color: #1ea15b;
}

.qzcmt-cont-top .user-name a.level {
    margin-top: 3px;
    padding: 0 6px;
    height: 15px;
    line-height: 15px;
    color: #777;
    font-size: 13px;
    font-family: simsun;
    font-weight: 700;
    border: 1px solid #ccc;
    border-radius: 8px;
}

.qzcmt-cont-top .user-name a.qzheng {
    width: auto;
    height: 20px;
}

.qzcmt-cont-top .user-name a.qzheng img {
    float: left;
    height: 20px;
}

.qzcmt-cont-top .user-name a.qzheng:hover .zhenglayer {
    display: block;
}

.qzcmt-cont-top .user-name .gamept {
    float: left;
    margin-top: 1px;
    height: 20px;
    overflow: visible;
}

.qzcmt-cont-top .user-name .gamept.cur .gameptlayer {
    display: block;
}

.qzcmt-cont-top .user-name a.attest {
    margin-top: 0;
    width: auto;
    height: 20px;
}

.qzcmt-cont-top .user-name a.attest img {
    float: left;
    display: block;
    width: auto;
    height: 20px;
}

.qzcmt-cont-top .user-name a.attest:hover .attestlayer {
    display: block;
}

.qzcmt-cont-top .qzcmt-xin {
    width: 100%;
    height: 22px;
}

.qzcmt-cont-top .qzcmt-xin .xin {
    float: left;
    margin: 1px 10px 0 0;
    width: 85px;
    height: 20px;
    background: url(//image.gamersky.com/webimg15/zp/xin4.png) repeat-x;
}

.qzcmt-cont-top .qzcmt-xin .xin div {
    float: left;
    width: 0;
    height: 20px;
    background: url(//image.gamersky.com/webimg15/zp/xin4.png) 0 -20px repeat-x;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x0 {
    width: 0;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x1 {
    width: 8px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x2 {
    width: 17px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x3 {
    width: 25px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x4 {
    width: 34px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x5 {
    width: 42px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x6 {
    width: 51px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x7 {
    width: 59px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x8 {
    width: 68px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x9 {
    width: 76px;
}

.qzcmt-cont-top .qzcmt-xin .xin div.x10 {
    width: 85px;
}

.qzcmt-cont-top .qzcmt-xin .txt {
    float: left;
    margin-top: 1px;
    font-size: 15px;
    color: #777;
}

.qzcmt-content-tit .user-state {
    display: inline-block;
    vertical-align: middle;
    margin-right: 7px;
    overflow: visible;
}

/*状态*/
.qzcmt-content-tit .user-state span {
    float: left;
    margin-left: 3px;
    padding: 0 10px;
    display: inline-block;
    height: 20px;
    line-height: 20px;
    color: #fff;
    font-size: 12px;
    border-radius: 5px;
}

.qzcmt-content-tit .user-state span.top {
    background-color: #f7b20f;
}

.qzcmt-content-tit .user-state span.jin {
    background-color: #0099e5;
}

.qzcmt-content-tit .user-state span.box {
    margin-left: 20px;
    padding: 0;
    width: auto;
    display: none;
}

.qzcmt-content-tit .user-state span.box a.arrow {
    display: block;
    width: 30px;
    height: 22px;
    border-radius: 4px;
    background: url(//image.gamersky.com/webimg15/user/club/pc/ab1.png) center no-repeat;
}

.qzcmt-content-tit .user-state span.box a.arrow:hover {
    background-color: #eee;
}

.qzcmt-content-tit .user-state span.box .box-list {
    display: none;
    margin-left: -20px;
    width: 70px;
    height: auto;
    position: absolute;
    z-index: 100;
}

.qzcmt-content-tit .user-state span.box .box-list {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.qzcmt-content-tit .user-state span.box .box-list a {
    display: block;
    width: 100%;
    height: 26px;
    line-height: 26px;
    color: #555;
    font-size: 12px;
    text-align: center;
}

.qzcmt-content-tit .user-state span.box .box-list a:hover {
    background-color: #f2f2f3;
}

.qzcmt-content-tit .user-state span.box.cur .box-list {
    display: block;
}

/*内容*/
.qzcmt-content {
    height: auto;
}

.qzcmt-wrap.cur .qzcmt-content {
    width: auto;
    margin: 0;
}

.qzcmt-content a.ht {
    color: #1c8ddc;
}

.qzcmt-content a.link {
    margin: 0 3px;
    padding-left: 17px;
    color: #1c8ddc;
    background: url(//image.gamersky.com/webimg15/user/club2/pc/cmt-link.png) 0 -20px no-repeat;
}

.qzcmt-content a.link:hover {
    color: #1c8ddc;
    background-position: 0 -20px;
}

.qzcmt-content a.av {
    padding-left: 27px;
    color: #1c8ddc;
    background: url(//image.gamersky.com/webimg15/user/club/pc/cmt-av.png) 0 center no-repeat;
}

.qzcmt-content a.evt-more {
    margin-left: 3px;
    padding-right: 15px;
    color: #1ea15b;
    background: url(//image.gamersky.com/webimg15/user/club/pc/ab4.png) right center no-repeat;
}

.qzcmt-content-tit {
    width: 100%;
    height: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.qzcmt-content-tit span {
    margin-right: 7px;
    padding: 0 10px;
    display: inline-block;
    height: 20px;
    line-height: 20px;
    color: #fff;
    font-size: 12px;
    border-radius: 5px;
}

.qzcmt-content-tit span.top {
    background-color: #f7b20f;
}

.qzcmt-content-tit span.jin {
    background-color: #0099e5;
}

.qzcmt-content-tit a {
    padding: 3px 0;
    display: inline-block;
    line-height: 28px;
    color: #222;
    font-size: 16px;
    font-family: 'Microsoft YaHei';
    vertical-align: middle;
    font-weight: bold;
}

.qzcmt-content-txt {
    width: 100%;
    height: auto;
    line-height: 28px;
    color: #444;
    font-size: 16px;
    text-align: justify;
}

.qzcmt-content-txt {
    display: -webkit-box;
    display: box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
}

.qzcmt-content-txt a {
    color: #444;
}

.qzcmt-morelink {
    margin-bottom: 12px;
    padding: 10px 0;
    height: auto;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
}

.qzcmt-morelink a {
    padding-right: 10px;
    display: inline-block;
    width: auto;
    height: 20px;
    line-height: 20px;
    color: #aaa;
    font-size: 14px;
}

.qzcmt-morelink a {
    background: url(//image.gamersky.com/webimg15/user/club/pc/ar2.png) right center no-repeat;
}

.qzcmt-morelink a:hover {
    color: #1ea15b;
}

/*点赞*/
.qzcmt-action {
    width: 100%;
    height: 30px;
    overflow: hidden;
}

.qzcmt-action .time {
    float: left;
    height: 30px;
    line-height: 30px;
    color: #aaa;
    font-size: 13px;
}

/*日期*/
.qzcmt-action .time a {
    color: #aaa;
}

.qzcmt-action .time a:hover {
    color: #1ea15b;
}

.qzcmt-action .source {
    float: left;
    width: auto;
    height: 30px;
    line-height: 30px;
    color: #999;
    font-size: 12px;
    overflow: hidden;
}

.qzcmt-action .source a {
    color: #999;
}

.qzcmt-action .source a:hover {
    color: #1ea15b;
}

.qzcmt-action .handle {
    float: left;
    width: auto;
    height: 30px;
    overflow: hidden;
}

.qzcmt-action .handle a {
    float: left;
    margin-left: 30px;
    padding-left: 20px;
    display: inline-block;
    width: auto;
    height: 30px;
    line-height: 30px;
    color: #999;
    font-size: 13px;
    overflow: hidden;
}

.qzcmt-action .handle a {
    background: url(//image.gamersky.com/webimg15/zp/zan2.png) no-repeat;
}

.qzcmt-action .handle a.evt-reply {
    background-position: 0 0;
}

.qzcmt-action .handle a.evt-reply:hover {
    background-position-y: -30px;
}

.qzcmt-action .handle a.evt-digg:link {
    background-position-y: -60px;
}

.qzcmt-action .handle a.evt-digg:visited {
    background-position-y: -150px;
}

.qzcmt-action .handle a.evt-digg:hover {
    background-position-y: -90px;
}

.qzcmt-action .handle a.evt-digg:active {
    background-position-y: -120px;
}

.qzcmt-action .handle a.evt-noDigg {
    font-size: 0;
}

.qzcmt-action .handle a.evt-noDigg:link {
    background-position-y: -180px;
}

.qzcmt-action .handle a.evt-noDigg:hover {
    background-position-y: -210px;
}

.qzcmt-action .handle a.evt-noDigg:active {
    background-position-y: -240px;
}

.qzcmt-action .handle a:hover {
    color: #1ea15b;
}

/*小图展示*/
.qzcmt-picdiv {
    width: 135px;
    height: 135px;
    float: right;
    margin: 5px 0 0 29px;
}

.qzcmt-picdiv a {
    display: inline-block;
    width: auto;
    height: 135px;
    position: relative;
}

.qzcmt-picdiv a .txt {
    padding: 0 3px;
    width: auto;
    height: 16px;
    line-height: 16px;
    color: #fff;
    font-size: 12px;
    font-family: SimSun;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
}

.qzcmt-picdiv a .txt {
    background-image: url(//image.gamersky.com/webimg15/user/club2/pc/mk.png);
}

.qzcmt-picdiv a .img {
    float: left;
    width: 135px;
    height: 135px;
    overflow: hidden;
    position: relative;
}

.qzcmt-picdiv a img {
    width: 100%;
}

.qzcmt-picdiv a i {
    display: block;
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -25px 0 0 -25px;
}

.qzcmt-picdiv a i {
    background: url(//image.gamersky.com/webimg15/user/club2/pc/qzcmt-play.png) no-repeat;
}

/*视频展示*/
.qzcmt-videodiv {
    width: 100%;
    height: auto;
}

.qzcmt-videodiv .links {
    width: 100%;
    height: 20px;
    line-height: 20px;
}

.qzcmt-videodiv a {
    display: inline-block;
    width: 240px;
    height: 135px;
    position: relative;
}

.qzcmt-videodiv a img {
    width: 100%;
}

.qzcmt-videodiv a i {
    display: block;
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -25px 0 0 -25px;
}

.qzcmt-videodiv a i {
    background: url(//image.gamersky.com/webimg15/user/club2/pc/qzcmt-play.png) no-repeat;
}

.qzcmt-videodiv .video {
    width: 240px;
    height: 135px;
    position: relative;
    transition: all .25s ease-out;
}

.qzcmt-videodiv .video.cur {
    width: 630px;
    height: 355px;
}

.qzcmt-videodiv .video-img {
    width: 100%;
    height: 100%;
    text-align: center;
    position: relative;
    cursor: pointer;
}

.qzcmt-videodiv .video-img img {
    width: 100%;
}

.qzcmt-videodiv .video-img i {
    display: block;
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -25px 0 0 -25px;
}

.qzcmt-videodiv .video-img i {
    background: url(//image.gamersky.com/webimg15/user/club2/pc/qzcmt-play.png) no-repeat;
}

.qzcmt-videodiv .video-play {
    width: 100%;
    height: 100%;
    background-color: #000;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
}

/*正在加载*/
.loading {
    display: none;
    padding: 10px 0;
    width: auto;
    height: 30px;
    text-align: center;
}

/*没有更多了*/
.loadend {
    display: none;
    padding: 10px 0;
    width: auto;
    height: 30px;
    text-align: center;
}

.loadend fieldset {
    margin: 0 auto;
    border: 0;
    border-top: 1px solid #ccc;
    width: 290px;
}

.loadend fieldset legend {
    padding: 0 10px;
    color: #888;
    font-size: 14px;
}

/* 查看更多 */
.qzcmt-more {
    padding: 20px 0;
    border-top: 1px solid #eee;
    clear: both;
}

.qzcmt-more a {
    padding-right: 10px;
    font-size: 14px;
    color: #666;
    background: url(//image.gamersky.com/webimg15/user/club/pc/ar1.png) right center no-repeat;
}

.qzcmt-more a:hover {
    color: #1ea15b;
}