(function ($) {
    var $kuGameScore = $(".kuGameScore");
    var gameid = $kuGameScore.attr("gameid"); //获取游戏ID

    // var gameid = 1518922; //获取游戏ID
    // var gameid = window.location.search.split('=')[1]; //获取游戏ID

    var gamename = $kuGameScore.attr("gamename"); //获取游戏名
    var userId = 0;
    var UserCookie = cookie("UserCookie");
    if (UserCookie) {
        var response = $.parseJSON(UserCookie);
        userId = response.userid;
    }
    var currentUserContentRelation = {}
    var scoreInfo = {}
    var platformList = []
    var onePlatform = false
    var platform = '全部'
    var isGamePublished = false
    var currentIsGamePublished = false

    var currentScore = 0

    var isEditComment = false

    var fabuDis = false
    var fabuLoading = false;

    var isTrim = function (s) { if (typeof (s) == "undefined") { return false; } return s.replace(/(^\s*)|(\s*$)/g, ""); }; //清除空格

    function gameInfo() {
        $.ajax({
            type: 'get',
            dataType: 'json',
            contentType: 'application/json',
            url: '//router5.gamersky.com/@/game/getGameWithId/6.0.0/' + userId,
            data: {
                gameId: gameid
            },
            success: function (res) {
                currentUserContentRelation = res.game.currentUserContentRelation
                $('#Mid_comment .comment_header .top .left .chang').attr('reviewSubjectId', res.game.reviewSubjectId)

                $.fn.getCommentList(false)
                ptList()
            }
        })
    }
    gameInfo()

    function ptList() {


        $.ajax({
            type: 'get',
            dataType: 'json',
            contentType: 'application/json',
            url: '//router.gamersky.com/@/gameScoreDetailPage/scoreInfo/6.0.0/' + userId + "/App_iOS",
            data: {
                gameId: gameid,
                platform: platform == '全部' ? '' : platform
            },
            success: function (res) {

                scoreInfo = res.gameScoreInfo
                if (platform == '全部') {
                    isGamePublished = res.isGamePublished
                }

                currentIsGamePublished = res.isGamePublished


                $.ajax({
                    type: 'get',
                    dataType: 'json',
                    contentType: 'application/json',
                    url: '//router.gamersky.com/@/gameCommentEditorPage/index/6.0.0/' + userId + "/",
                    data: {
                        gameId: gameid,
                    },
                    success: function (res) {
                        zpHtml.getZpScore()

                        platformList = res.gameUserCommentInfo.publishTimes
                        if (platformList.length == 1) {
                            $('#ym_zhongping .zp_top .zp_header .zp_header_r .jiantou').hide()
                            onePlatform = true
                        }
                        platformList.unshift({ platform: '全部', publishTimeCaption: '' })
                        let ptList = ``;
                        platformList.forEach(item => {
                            if (item.platform == '全部') {
                                ptList += `
                                    <li>
                                        <img src="https://image.gamersky.com/webimg13/zongping-all-plat_dark.svg">
                                        <span>${item.platform}</span>
                                    </li>
                                `
                            } else {

                                ptList += `
                                    <li>
                                        <img src="https://image.gamersky.com/webimg13/icon_20_${item.platform.toUpperCase().toLowerCase()}_fill_dark.svg">
                                        <span>${item.platform}</span>
                                    </li>
                                `
                            }

                        })

                        $('#ptSort').find('ul').html(ptList)

                        let textareaptList = ``

                        platformList.forEach(item => {


                            if (item.platform != '全部') {
                                textareaptList +=
                                    `
                                        <div class="ptItem" time="${item.publishTimeStamp}">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/icon_20_${item.platform.toUpperCase().toLowerCase()}_fill.svg">
                                            <span>${item.platform}</span>
                                        </div>
                                    `
                            }
                        });


                        $('#textarea .pt .ptList').html(textareaptList)

                        $('#textarea .pt .ptList .ptItem').click(function () {

                            const currentTimestamp = Date.now();
                            const targetTimestamp = $(this).attr('time')
                            if ((currentTimestamp < targetTimestamp) && $('#textarea .header .btn .cur').text() == '玩过') {
                                let total =
                                    `
                                        <div class="total">
                                            该平台未上线
                                        </div>
                                    `
                                $('body').append(total)
                                setTimeout(function () {
                                    $('.total').remove()
                                }, 1500)
                                return

                            }
                            $(this).addClass('cur')
                            $(this).find('img').attr('src', `https://image.gamersky.com/webimg13/icon_20_${$(this).find('span').text().toUpperCase().toLowerCase()}_fill_highlight.svg`)

                            $(this).siblings().each((index, item) => {
                                $(item).find('img').attr('src', `https://image.gamersky.com/webimg13/www/newPc/icon_20_${$(item).find('span').text().toUpperCase().toLowerCase()}_fill.svg`)
                            })
                            $(this).siblings().removeClass('cur')
                        })


                        if (scoreInfo.userScore == 0 && currentIsGamePublished) {
                            $('#ym_zhongping .zp_top .score_progress .l_score .rsbz').show()
                            $('#ym_zhongping .zp_top .score_progress .l_score .score_t').hide()
                            $(' #ym_zhongping .zp_bottom .l .t2').hide()
                        }


                        zpHtml.ptClick()

                        if (currentIsGamePublished) {
                            $('.score_progress').css('display', 'flex')
                            $('#ym_zhongping .zp_bottom .l .t1').css('display', 'flex')
                            $('#ym_zhongping .zp_bottom .l .t2').show()
                            $('#ym_zhongping .zp_bottom .l .t3').hide()
                            $('.zp_kong').hide()
                            $('#ym_zhongping .zp_top .zp_header .zp_header_l .num').show()
                        } else {
                            $('.score_progress').css('display', 'none')
                            $('#ym_zhongping .zp_bottom .l .t1').hide()
                            $('#ym_zhongping .zp_bottom .l .t2').hide()
                            $('#ym_zhongping .zp_bottom .l .t3').show()
                            $('.zp_kong').show()
                            $('#ym_zhongping .zp_top .zp_header .zp_header_l .num').hide()
                        }

                        if (scoreInfo.scoreDescription.indexOf('\r\n') == -1) {
                            $('#ym_zhongping .zp_bottom .l .t2').hide()
                        }

                        zpHtml.getCommnet()
                    }

                })

            }
        })
    }

    let delTip =
        `
            <div id="delTip">
                <div class="del_t">
                    <div class="t1">是否确认删除</div>
                    <div class="t2">删除玩过状态，将会同时删除您发布的评分、短评</div>
                </div>
                <div class="del_btn">
                    <div class="qx">取消</div>
                    <div class="qr">确认</div>
                </div>
            </div>
            
            `
    $('body').append(delTip)

    $('#delTip .del_btn div').click(function () {
        if ($(this).text() == '取消') {
            $('#delTip').hide()
            $('#mode').css('z-index', '998')
        } else {
            zpHtml.deleteComment()

        }
    })

    const zpHtml = {
        getZpScore() {
            var Mid_comment = $('#ym_zhongping');
            var htm =
                `   <div class="zp_top">
                        <div class="zp_header">
                            <div class="zp_header_l">
                                <div class="title">游民众评</div>
                                <div class="num">${scoreInfo.playedsCount}人玩过，${scoreInfo.wantPlaysCount}人想玩</div>
                            </div>
                            <div class="zp_header_r">
                `
            if (platform == '全部') {
                htm +=
                    `
                        <img src="https://image.gamersky.com/webimg13/zongping-all-plat_dark.svg">
                    `
            } else {
                htm +=
                    `
                        <img class="ico" src="https://image.gamersky.com/webimg13/icon_20_${platform.toUpperCase().toLowerCase()}_fill_dark.svg">
                    `
            }

            htm +=
                `
                                <div class="ptsx">${platform}</div>
                                <img class="jiantou" src="https://image.gamersky.com/webimg13/arrow-down.svg">
                            </div>
                        </div>
                        <div class="score_progress">
                            <div class="l_score">
                                <div class="score_t">
                                    <div class="score">${scoreInfo.userScore ? scoreInfo.userScore > 9 ? scoreInfo.userScore : scoreInfo.userScore.toFixed(1) : '--'}</div>
                                    <div class="wg_score">
                                        <div class="wg">
                                            <img src="https://image.gamersky.com/webimg13/www/newPc/ku/zongping.svg">
                                            <span>玩过</span>
                                        </div>
                                        <div class="fs">${scoreInfo.realPlayersScore ? scoreInfo.realPlayersScore.toFixed(1) : '--'}</div>
                                    </div>
                                </div>
                                <div class="rsbz">评分人数不足</div>
                                <div class="pfNum">${scoreInfo.scoreUsersCount}人评分</div>
                            </div>
                            <div class="r_progress">
                                <div class="star_pro">
                                    <div class="star">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                    <div class="progress" >
                                        <div rate="${Math.round(scoreInfo.scoreDistributeInfes[4].usersCountRate)}"></div>
                                    </div>
                                    <div class="bfb">${Math.round(scoreInfo.scoreDistributeInfes[4].usersCountRate)}%</div>
                                </div>
                                <div class="star_pro">
                                    <div class="star">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                    <div class="progress">
                                        <div rate="${Math.round(scoreInfo.scoreDistributeInfes[3].usersCountRate)}"></div>
                                    </div>
                                    <div class="bfb">${Math.round(scoreInfo.scoreDistributeInfes[3].usersCountRate)}%</div>
                                </div>
                                <div class="star_pro">
                                    <div class="star">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                    <div class="progress">
                                        <div rate="${Math.round(scoreInfo.scoreDistributeInfes[2].usersCountRate)}"></div>
                                    </div>
                                    <div class="bfb">${Math.round(scoreInfo.scoreDistributeInfes[2].usersCountRate)}%</div>
                                </div>
                                <div class="star_pro">
                                    <div class="star">
                                        <div></div>
                                        <div></div>
                                    </div>
                                    <div class="progress">
                                        <div rate="${Math.round(scoreInfo.scoreDistributeInfes[1].usersCountRate)}"></div>
                                    </div>
                                    <div class="bfb">${Math.round(scoreInfo.scoreDistributeInfes[1].usersCountRate)}%</div>
                                </div>
                                <div class="star_pro">
                                    <div class="star">
                                        <div></div>
                                    </div>
                                    <div class="progress">
                                        <div rate="${Math.round(scoreInfo.scoreDistributeInfes[0].usersCountRate)}"></div>
                                    </div>
                                    <div class="bfb">${Math.round(scoreInfo.scoreDistributeInfes[0].usersCountRate)}%</div> 
                                </div>
                            </div>
                        </div>

                        <div class="zp_kong">
                            <div>
                                ${scoreInfo.wantPlaysCount} 人想玩
                            </div>
                            <div>此游戏尚未上市</div>
                        </div>
                    </div>
                    <div class="zp_bottom">
                        <div class="zp_bottom_box">
                            <div class="l">
                                <div class="t1">
                                    <img class="icon" src="https://image.gamersky.com/webimg13/www/newPc/ku/zongping.svg">
                                    <span class="s">${scoreInfo.scoreDescription.split('\r\n')[0]}</span>
                                    <img class="icon1"  src="https://image.gamersky.com/webimg13/www/newPc/ku/tips-icon.svg">
                                </div>
                                <div class="t2">
                                    <img src="https://image.gamersky.com/webimg13/www/newPc/ku/qushi.svg">
                                    `
            scoreInfo.scoreDescription.split('\r\n').forEach((item, index) => {
                if (index > 0) {
                    htm +=
                        `
                                                <span class="s">${item}</span>
                                            `
                }
            })
            htm +=
                `
                                </div>
                                <div class="t3">点击想玩进行标记，随时记录游玩动态</div>
                            </div>
                            <div class="r">
                                <div class="wantplay">
                                    <img class="icon" src="https://image.gamersky.com/webimg13/wantPlay.svg"> 
                                    <span class="txt">想玩</span>
                                    <img class="icon_r" src="https://image.gamersky.com/webimg13/arrow-down.svg">
                                </div>
                                <div class="played">
                                    <img class="icon" src="https://image.gamersky.com/webimg13/playde.svg"> 
                                    <span class="txt">玩过</span> 
                                </div>
                                <div class="yiwanguo">
                                    <span>已玩过</span>
                                    <div class="star">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                    <span class="t"></span>
                                    <img class="ico" src="https://image.gamersky.com/webimg13/arrow-down.svg">
                                </div>
                            </div>
                        </div>
                    </div>
                `

            $(Mid_comment).html(htm)





            if (isGamePublished) {
                $('#ym_zhongping .zp_bottom .r .played').css('display', 'flex')
                $('#ym_zhongping .zp_bottom .r .wantplay').css('display', 'flex')
            } else {
                $('#ym_zhongping .zp_bottom .r .played').hide()
                $('#ym_zhongping .zp_bottom .r .wantplay').css('display', 'flex')
            }

            if (currentUserContentRelation.beWantPlay) {

                $('#ym_zhongping .zp_bottom .r .wantplay').find('.icon').hide()
                $('#ym_zhongping .zp_bottom .r .wantplay').find('.icon_r').show()
                $('#ym_zhongping .zp_bottom .r .wantplay').find('span').text('已想玩')
                $('#ym_zhongping .zp_bottom .r .wantplay').addClass('cur')
                $('#ym_zhongping .zp_bottom .r').attr('iswantplay', 'true')
            } else if (currentUserContentRelation.bePlayed) {


                $("#ym_zhongping .zp_bottom .r .played").hide()
                $('.wantplay').hide()
                $('.yiwanguo').css('display', 'flex')

                $('.yiwanguo').find('.t').text('写点评')
                $("#ym_zhongping .zp_bottom .r .played").siblings().removeClass('cur')

                $('#ym_zhongping .zp_bottom .r .yiwanguo .star').attr('score', currentUserContentRelation.contentScore)
                $('#ym_zhongping .zp_bottom .r').attr('iswantplay', 'false')
                var i = parseInt(currentScore / 2);

                $('#ym_zhongping .zp_bottom .r .yiwanguo .star div').each(function (index) {

                    $(this).attr('data-sorce', $(this).index() + 1)
                    if (index <= i - 1) {
                        $(this).css('background-image', 'url("https://image.gamersky.com/webimg13/star_cur_12.svg")');
                    }
                });

            }


            $('#ym_zhongping .zp_top .score_progress .r_progress .star_pro .progress div').each(function () {
                $(this).css('width', $(this).attr('rate') + '%');
            });



            let ptSort =
                `<div id="ptSort">
                    <ul></ul>    
                </div>`
            $('#ym_zhongping .zp_top .zp_header').append(ptSort)

            let explain =
                `
                    <div id="explain">
                        <div class="box">
                             <div class="left">
                                <div class="title">
                                    <img src="https://image.gamersky.com/webimg13/www/newPc/ku/zongping.svg">
                                    <span>玩过认证玩家说明</span>
                                </div>
                                <div class="txt">
                                    <p>
                                        绑定了游戏账号（比如Steam、PSN），并且拥有这款游戏的用户会显示此标识；
                                    </p>
                                    <p>
                                        如果您通过共享或者是购买后又退款等形式拥有过此游戏，仍然算作玩过认证玩家!
                                    </p>
                                </div>
                            </div>
                            <div class="right">
                                <img class="ewm" src="http://image.gamersky.com/webimg13/ewm.png">
                                <span>用游民App认证玩过玩家</span>
                            </div>
                        </div>
                        
                    </div>
                `
            $('#ym_zhongping .zp_bottom .l .t1').append(explain)

            let palyMark =
                `
                    <div id="palyMark">
                        <div class="box">
                            <div class="tit">
                                <img src="http://image.gamersky.com/webimg13/icon_success.svg">
                                <span>成功标记玩过</span>
                            </div>
                            <div class="m">
                                您可以继续发表评价，帮助更多的玩家
                            </div>
                            <div class="b">
                                <span>评分</span>
                                 <div class="star">
                                    <div></div>
                                    <div></div>
                                    <div></div>
                                    <div></div>
                                    <div></div>
                                </div>
                                <span>点击星星评分</span>
                            </div>
                        </div>
                    </div>
                `
            $('#ym_zhongping .zp_bottom .r .yiwanguo').append(palyMark)

            let editComment =
                `   
                    <div id="mode"></div>
                    <div id="textarea">
                        <div class="header">
                            <div class="textarea_close"></div>
                            <div class="btn">
                                <div class="cur">想玩</div>
                                <div>玩过</div>
                            </div>

                        </div>
                        <div class="score">
                            <div class="t">评分</div>
                            <div class="star">
                                <div></div>
                                <div></div>
                                <div></div>
                                <div></div>
                                <div></div>
                            </div>
                            <div class="txt">点击星星评分</div>
                        </div>
                        <div class="pt">
                            <div class="t">平台</div>
                            <div class="ptList"></div>
                        </div>
                        <div class="ku-pop-textarea">
                            <div class="con">
                                <div class="textarea-code" id="textarea-code" contenteditable="true" spellcheck="false"></div>
                            </div>
                            <div class="bot">
                                <div class="kuEditor">
                                    <a href="javascript:;" class="a1" data-role="Bold">粗体</a>
                                    <a href="javascript:;" class="a2" data-role="Underline">下划线</a>
                                    <a href="javascript:;" class="a3" data-role="Italic">斜体</a>
                                    <a href="javascript:;" class="a4" data-role="StrikeThrough">删除线</a>
                                    <a href="javascript:;" class="a5" data-role="CreateLink">隐藏文本</a>
                                </div>
                                <div class="kuAlert"><span class="Anum">0</span>/<span class="Bnum">348</span></div>
                            </div>
                        </div>
                        <div class="bottom_btn">
                            <div class="del">
                                <img src="https://image.gamersky.com/webimg13/icon_del.png">
                                <span>删除标记</span>
                            </div>
                            <div class="fabu">发布</div>
                        </div>

                `

            $('body').append(editComment)


            $('#textarea .header .textarea_close').click(function (e) {
                $('#textarea').hide()
                $('#mode').hide()
            })
            $('#textarea .header .btn div').click(function (e) {
                if (!isGamePublished && $(this).text() == '玩过') {
                    return
                }
                $(this).addClass('cur')
                $(this).siblings().removeClass('cur')
                if ($(this).text() == '想玩') {
                    $('#textarea .score').hide()
                } else {
                    $('#textarea .score').show()
                }
                $('#textarea .pt .ptList .ptItem').removeClass('cur')
            })

            $('#textarea .score .star div').click(function () {
                $(this).attr('data-sorce', $(this).index() + 1)
                $(this).addClass('cur')
                $(this).siblings().removeAttr('data-sorce')
                $(this).siblings().removeClass('cur')
                $(this).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                Array.from($(this).siblings()).forEach(item => {
                    if ($(item).index() < $(this).index()) {
                        $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                    } else {
                        $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star.svg")')
                    }
                })
            })

            $('#textarea .bottom_btn .del').click(function () {
                $('#mode').css('z-index', '1000')
                $('#delTip').show()
            })

            $('#ym_zhongping .zp_bottom .l .t1 .icon1').mouseenter(function () {
                $('#explain').show()
            })

            $('#ym_zhongping .zp_bottom .l').mouseleave(function () {
                $('#explain').hide()
            })


            $('#ym_zhongping .zp_top .zp_header .zp_header_r').mouseenter(function (e) {
                e.stopPropagation()
                if (onePlatform) {
                    $(this).css('cursor', 'auto')
                    return
                }

                $('#ptSort').show()
                let t = $(this).find('.ptsx').text()
                $('#ptSort ul li').each((index, item) => {

                    if (t == $(item).find('span').text()) {

                        if (t == '全部') {
                            $(item).find('span').addClass('cur')
                            $(item).find('img').attr('src', 'https://image.gamersky.com/webimg13/zongping-all-plat.svg')
                            $(item).siblings().find('span').removeClass('cur')
                        } else {
                            $(item).find('span').addClass('cur')
                            $(item).find('img').attr('src', `https://image.gamersky.com/webimg13/www/newPc/icon_20_${$(item).find('span').text().toUpperCase().toLowerCase()}_fill.svg`)
                            $(item).siblings().find('span').removeClass('cur')
                        }
                    }
                })
            })


            $('#ym_zhongping .zp_top .zp_header').mouseleave(function () {
                $('#ptSort').hide()
            })

            $('#ptSort').mouseleave(function () {
                $(this).hide()
            })
            $('#ym_zhongping .zp_bottom .r .wantplay').click(function (e) {
                e.stopPropagation()
                if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
                    // $("body").append(PLhtml.login_layer(''));
                    // $('.QZshade').show()
                    // $('.QZlogin').show()

                    // $(".QZlogin").QZloginForm();
                    $.fn.UserOnline(() => { });
                    return false;
                }

                if ($(this).find('span').text() == '想玩') {
                    $(this).find('.icon').hide()
                    $(this).find('.icon_r').show()
                    $(this).find('span').text('已想玩')
                    $(this).addClass('cur')
                } else {
                    zpHtml.kuEditor(); //调用编辑器

                    $('#textarea .score').hide()
                    $('.yiwanguo').css('display', 'none')
                    if (isGamePublished) {
                        $('.played').show()
                    }

                    $('#textarea').show()
                    $('#mode').show()
                    zpHtml.getCommnet()
                }

                $.ajax({
                    type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addwanRating",
                    data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": 1, "Type": 7, "FromDevice": 0 }) },
                    success: function (response) {
                        fun(response);
                    }
                });
            })

            $('#ym_zhongping .zp_bottom .r .played').click(function (e) {
                e.stopPropagation()
                if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
                    // $("body").append(PLhtml.login_layer(''));
                    // $('.QZshade').show()
                    // $('.QZlogin').show()

                    // $(".QZlogin").QZloginForm();
                    $.fn.UserOnline(() => { });
                    return false;
                }


                $(this).hide()
                $('.wantplay').hide()
                $('.yiwanguo').css('display', 'flex')

                $('.yiwanguo').find('.t').text('去评分')
                $(this).siblings().removeClass('cur')
                $('#ym_zhongping .zp_bottom .r .wantplay').find('span').text('想玩')
                $('#ym_zhongping .zp_bottom .r .wantplay').find('.icon').show()
                $('#ym_zhongping .zp_bottom .r .wantplay').find('.icon_r').hide()

                $.ajax({
                    type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addwanRating",
                    data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": 1, "Type": 8, "FromDevice": 0 }) },
                    success: function (response) {
                        $nava.attr("data-click", false);
                        fun(response);
                    }
                });

                $('#palyMark').show()

                setTimeout(function () {
                    $('#palyMark').hide()
                }, 10000)

                zpHtml.kuEditor(); //调用编辑器
            })

            $('#ym_zhongping .zp_bottom .r .yiwanguo').click(function (e) {
                e.stopPropagation()
                if ($.cookie("UserCookie") == undefined || $.cookie("UserCookie") == null) {
                    // $("body").append(PLhtml.login_layer(''));
                    // $('.QZshade').show()
                    // $('.QZlogin').show()

                    $.fn.UserOnline(() => { });
                    return false;
                }

                $('#textarea').show()
                $('#mode').show()
                $('#textarea .header .btn div').click()
                zpHtml.kuEditor(); //调用编辑器
                zpHtml.getCommnet()
                zpHtml.GetRating()
                zpHtml.getPlatform()
            })

            $('#palyMark .box .b .star div').click(function () {

                $(this).attr('data-sorce', $(this).index() + 1)
                $(this).addClass('cur')
                $(this).siblings().removeAttr('data-sorce')
                $(this).siblings().removeClass('cur')
                $(this).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                Array.from($(this).siblings()).forEach(item => {
                    if ($(item).index() < $(this).index()) {
                        $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                    } else {
                        $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star.svg")')
                    }
                })
                let i = $(this).index()
                Array.from($('#textarea .score .star div')).forEach((item, index) => {
                    if (index <= i) {
                        $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                    }
                    if (index == i) {
                        $(item).addClass('cur')
                        $(item).attr('data-sorce', index + 1)
                    }
                })

                zpHtml.getCommnet()
                // zpHtml.Submit()
                $('#textarea').show()
                $('#mode').show()

                $('#textarea .header .btn div').click()
            })

            document.body.addEventListener('click', function () {
                $('#ptSort').hide()
                $('#explain').hide()
                $('#palyMark').hide()
            })



            $('#textarea .bottom_btn .fabu').click(function (event) {
                if (fabuDis) {
                    return
                }
                if (fabuLoading) {
                    return;
                }

                fabuLoading = true;
                event.preventDefault();


                var that = $(this);
                var text = $("#textarea-code").text();
                var con = $("#textarea-code").text();

                var cmtid = $('.myscore li.myremark').attr("cmtid")


                var pfhtml = $('#textarea .pt .ptList .cur span').text()


                var score = $('#textarea .score .star .cur').attr('data-sorce') * 2 || 0;
                var playText = $("#textarea .header .btn .cur").text();

                if (score != "" && pfhtml == "" && playText == '玩过') {
                    alert("请选择平台。");
                    fabuLoading = false;
                    return;
                }

                if (isEditComment) {
                    zpHtml.AddwanRating(function (response) {
                        zpHtml.addPlatform(pfhtml);
                    })
                    let type = playText == '玩过' ? 8 : 7
                    let content = encodeURIComponent(con);

                    if (con) {
                        $.ajax({
                            type: "POST", dataType: 'json', url: "//cm1.gamersky.com/api/updatecomment",
                            data: { "jsondata": JSON.stringify({ "cmtId": cmtid, "content": content, "platform": pfhtml, "score": score, "type": type }) },
                            xhrFields: { withCredentials: true },
                            success: function (response) {
                                if (response.status == "ok") {
                                    $('#textarea').hide();
                                    $('#mode').hide();
                                    fabuLoading = false;
                                } else {
                                    alert(response.body);
                                    fabuLoading = false;
                                }
                            }
                        })
                    } else {
                        $('#textarea').hide();
                        $('#mode').hide();
                        fabuLoading = false;
                    }
                } else {
                    zpHtml.AddwanRating(function (response) {
                        zpHtml.addPlatform(pfhtml);
                        zpHtml.SubmitAjax(con, pfhtml, playText, function (response) {
                            that.attr("data-click", false);
                            if (response.status == "ok") {
                                $('#textarea').hide();
                                $('#mode').hide();
                                fabuLoading = false;
                            } else {
                                alert(response.body);
                                fabuLoading = false;
                            }
                        })
                    })
                }

                zpHtml.updateIndex();
                zpHtml.AddRating();

                var i = parseInt(score ? score / 2 : currentScore / 2);



                $('#ym_zhongping .zp_bottom .r .yiwanguo .star div').each(function (index) {
                    $(this).css('background-image', 'url("https://image.gamersky.com/webimg13/www/newPc/ku/star.svg")');
                    if (index <= i - 1) {
                        $(this).css('background-image', 'url("https://image.gamersky.com/webimg13/star_cur_12.svg")');
                    }
                });

                if (con != '') {
                    $('#ym_zhongping .zp_bottom .r .yiwanguo .t').hide()
                } else {
                    $('#ym_zhongping .zp_bottom .r .yiwanguo .t').text('写点评')
                }


            })
        },
        //获取当前评论内容
        getCommnet: function (div) {
            var $this = $(div);
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/api/exists",
                data: { "GenneralId": gameid },
                success: function (response) {
                    if (response.status != 'err') {
                        var isExist = response.isExist;
                        var cmtId = response.cmtId;
                        var content = response.content;
                        var status = response.status;
                        if (isExist == true) {
                            if (status != '') {
                                if ($(".inAudit").length == 0) {
                                    $(".myscore .fbtn").append('<span class="inAudit">审核中</span>');
                                }
                                $this.hide();
                            } else {
                                $this.show();
                            }
                            if (content != '') {
                                $('#ym_zhongping .zp_bottom .r .yiwanguo .t').hide()
                            }
                            $("#textarea-code").html(content);

                            if (content != '') {
                                isEditComment = true
                            } else {
                                isEditComment = false
                            }
                        }
                    }
                }
            });
        },

        ptClick() {
            $('#ptSort ul li').click(function (e) {
                e.stopPropagation()

                if ($(this).find('span').text() == '全部') {
                    $(this).find('img').attr('src', 'https://image.gamersky.com/webimg13/zongping-all-plat.svg')

                } else {
                    $(this).find('img').attr('src', `https://image.gamersky.com/webimg13/www/newPc/icon_20_${$(this).find('span').text().toUpperCase().toLowerCase()}_fill.svg`)
                }


                $('#ym_zhongping .zp_top .zp_header .zp_header_r .ptsx').text($(this).find('span').text())
                $('#ptSort').hide()
                platform = $(this).find('span').text()
                gameInfo()
            })

        },

        SubmitAjax: function (con, platform, typeString, fun) {


            var sid = $("#Remark").attr("sid"); //文章ID
            var nodeId = $("#Remark").attr("nodeId"); //节点ID
            // var url = window.location.href;
            var url = "https://ku.gamersky.com/2020/black-myth-wu-kong/";
            var title = document.title;
            var content = encodeURIComponent(con);
            // var cmtid = $(".remark-textarea").attr("cmtid");
            var cmtid = $('.myscore li.myremark').attr("cmtid")

            var brow = $.browser.msie ? "jsonp" : "json";
            var score = $('#textarea .score .star .cur').attr('data-sorce') * 2 || 0;
            var type = "7";
            var $atext = $("#textarea .header .btn .cur").text();
            if (typeString == "玩过") {
                type = "8";
            }
            var pfhtml = platform;

            // if (score != "" && pfhtml == "" && $atext == '玩过') {
            //     alert("请选择平台。");
            //     return;
            // }

            if (con) {
                var jsondata = { sid: gameid, content: content, cmtid: cmtid, topicTitle: title, topicUrl: url, nodeId: nodeId, score: score, type: type, platform: pfhtml };
                $.ajax({
                    type: "POST", dataType: 'jsonp', url: "//cm1.gamersky.com/api/addcommnet",
                    data: { "jsondata": JSON.stringify(jsondata) },
                    xhrFields: { withCredentials: true },
                    success: function (response) {
                        fun(response);
                    }
                });
            } else {
                $('#textarea').hide()
                $('#mode').hide()
            }

        },
        AddwanRating: function (fun) {

            var $nava = $("#textarea .header .btn .cur")
            var type = $nava.text() == '玩过' ? 8 : 7
            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addwanRating",
                data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": 1, "Type": type, "FromDevice": 0 }) },
                success: function (response) {
                    $nava.attr("data-click", false);
                    fun(response);
                }
            });
        },
        updateIndex: function (options) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/api/updateindex",
                data: { sid: gameid },
                success: function (response) { }
            });
        },
        //添加玩过的平台
        addPlatform: function (pfhtml) {

            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/addplatform",
                data: { "jsondata": JSON.stringify({ "GenneralId": gameid, "platform": pfhtml }) },
                success: function (response) {
                    if (response.status == "ok") {
                        zpHtml.updateIndex();
                        fabuLoading = false;
                    }
                }
            });
        },
        AddRating: function () {

            var sorce = $('#textarea .score .star .cur').attr('data-sorce') * 2 || 0;
            console.log(sorce);

            if (!sorce || sorce == 0) {
                return false
            }
            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/AddRating",
                data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Sorce": sorce, "Type": 0, "fromDevice": 0 }) },
                success: function (data) {
                    zpHtml.updateIndex();
                }
            });

        },
        deleteComment: function () {
            var cmtId = $('.myscore li.myremark').attr("cmtid")

            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/api/deletecomment",
                data: { "jsondata": JSON.stringify({ "sid": gameid, "cmtid": cmtId }) },
                success: function (response) {
                    if (response.body == "ok") {


                        $('#textarea').hide()
                        $('#mode').show()
                        $('#ym_zhongping .zp_bottom .r .yiwanguo').hide()
                        if (isGamePublished) {
                            $('#ym_zhongping .zp_bottom .r .played').css('display', 'flex')
                            $('#ym_zhongping .zp_bottom .r .played .txt').text('玩过')
                        }

                        $('#ym_zhongping .zp_bottom .r .wantplay .txt').text('想玩')
                        $('#ym_zhongping .zp_bottom .r .wantplay').css('display', 'flex')
                        $('#ym_zhongping .zp_bottom .r .wantplay').find('.icon').show()
                        $('#ym_zhongping .zp_bottom .r .wantplay').find('.icon_r').hide()
                        $('#ym_zhongping .zp_bottom .r div').removeClass('cur')
                        $('#delTip').hide()
                        $('#mode').css('z-index', '998')
                        $('#mode').hide()
                    }
                }
            });
        },
        GetRating: function (div) {
            $.ajax({
                type: "GET",
                dataType: "jsonp",
                url: "//cm1.gamersky.com/apirating/getuserrating",
                data: { "Rating": JSON.stringify({ "GenneralId": gameid, "Type": "0" }) },
                success: function (data) {

                    if (data.status == "ok") {
                        var sorce = data.sorce;
                        currentScore = data.sorce;
                        var i = parseInt(sorce / 2);


                        Array.from($('#textarea .score .star div')).forEach((item, index) => {
                            $(item).attr('data-sorce', index + 1)
                            if (index <= i - 1) {
                                $(item).css('background-image', 'url("https://image.gamersky.com/webimg13/Star_cur.svg")')
                            }
                            if ((index + 1) * 2 == currentScore) {

                            }
                        })
                    }
                }
            });
        },
        //获取玩过的平台
        getPlatform: function () {

            $.ajax({
                type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/apirating/getplatform",
                data: { "jsondata": JSON.stringify({ "GenneralId": gameid }) },
                success: function (response) {
                    if (response.status == 'ok') {

                        $('#textarea .pt .ptList .ptItem').each(function () {
                            if ($(this).find('span').text() === response.platform) {
                                $(this).addClass('cur').siblings().removeClass('cur');
                                $(this).find('img').attr('src', `https://image.gamersky.com/webimg13/icon_20_${$(this).find('span').text().toUpperCase().toLowerCase()}_fill_highlight.svg`)

                            }
                        });

                    }
                }
            });
        },

        kuEditor: function () { //编辑器操作

            var Select = true,
                $textareacode = $("#textarea-code"),
                $kuEditor = $(".kuEditor"),
                p = document.all ? "<p></p>" : "<p><br/></p>";
            var getSelectionText = function () {
                return window.getSelection ? window.getSelection().toString() : document.selection.createRange().text;
            }


            var setTimer;
            var fun = function (str, len1, len2) {
                clearTimeout(setTimer);
                if (len1 == len2) {
                    if (!isTrim(getSelectionText())) {
                        document.execCommand(str);
                    } else {
                        setTimer = setTimeout(function () {
                            fun(str, len1, len2)
                        }, 100);
                    }
                } else {
                    $kuEditor.find("[data-role='" + (str == 'Unlink' ? 'CreateLink' : str) + "']").addClass("cur");
                }
            }

            $textareacode.on("click", function (event) {

                if (!isTrim($textareacode.html())) {
                    $textareacode.html(p).find("p").focus();
                }
                var $this = $(this), evt = event.target;
                var obj = window.getSelection ? window.getSelection() : document.selection.createRange();

                var name = -[1,] ? obj.focusNode.parentNode.tagName.toLowerCase() : evt.nodeName.toLowerCase();
                var len1 = -[1,] ? obj.focusNode.length : evt.innerText.length;
                var len2 = obj.focusOffset;
                $kuEditor.find("a").removeClass("cur");
                switch (name) {
                    case "b":
                    case "strong": fun('Bold', len1, len2); break;
                    case "u": fun('Underline', len1, len2); break;
                    case "i":
                    case "em": fun('Italic', len1, len2); break;
                    case "strike": fun('StrikeThrough', len1, len2); break;
                    case "a": fun('Unlink', len1, len2); break;
                }
                if (isTrim(getSelectionText())) {
                    Select = true;
                }

            }).on("focus keyup blur", function () {

                var $this = $(this), txt = isTrim($this.text());
                if (txt == '') {
                    $this.html(p).find("p").focus();
                }
                zpHtml.textareaNum(txt);
            }).on("paste", function (event) {
                event.preventDefault();
                zpHtml.paste($(this), event);
            });

            $kuEditor.on("click", "a", function (event) {
                event.preventDefault();
                if (!isTrim(getSelectionText())) {
                    return false;
                }
                var $this = $(this), role = $this.attr("data-role");

                if (role == "CreateLink") {
                    document.execCommand(!Select ? "Unlink" : role, false, "javascript:void(0);");
                } else {
                    document.execCommand(role);
                }
                $this.removeClass("cur");
                Select = Select ? false : true;
            });

            var $con = $(".ku-pop-textarea .con");
            $con.attr("data-height", $con.height()).attr("data-i", 1);
            $(".ku-pop-textarea").on("click", ".space", function (event) {
                event.preventDefault();
                var h = parseInt($con.attr("data-height")),
                    i = parseInt($con.attr("data-i")) + 1;
                if (i == 3) {
                    $(this).remove();
                }
                if (i >= 4) {
                    return false;
                }
                $con.height(h * (i == 2 ? i : i + 1)).attr("data-i", i);
                var $layer = $(".ku_pop_layer"), height = $layer.height();
                $layer.css("marginTop", - (height / 2) + 'px'); //定位点评窗口上下剧中
            });
        },
        paste: function (that, evt) {

            var text = '', clp = (evt.originalEvent || evt).clipboardData;
            if (clp == undefined || clp == null) {
                text = window.clipboardData.getData("text") || "";
                if (isTrim(text) != "") {
                    text = that.find("p").length > 0 ? text.replace(/\n/g, '</p><p>') : '<p>' + text.replace(/\n/g, '</p><p>') + '</p>';
                    that.pasteHtmlAtCaret(emojibBatch(text)).focus();
                }
            } else {
                text = clp.getData('text/plain');
                text = text.replace(/       /ig, '　　'); //空格替换
                text = text.replace(/[\r|\t]/ig, ''); //换行清除
                text = text.replace(/[\n][\n]/ig, '</p><p>'); //两个换行转p标签
                text = text.replace(/[\n]/ig, '<br>'); //一个换行转br
                text = text.replace(/<p><br>/ig, '<p>'); //换行清除
                text = text.replace(/<br><\/p>/ig, '</p>'); //换行清除
                text = (text.substr(0, 2) != '<p' ? '<p>' : '') + text + (text.substr(text.length - 2, text.length) != 'p>' ? '</p>' : '');
                text = text.replace(/<p><\/p>/ig, ''); //空格清除

                document.execCommand('insertHTML', false, emojibBatch(text));
            }
        },
        textareaNum: function (txt) {
            var len = txt.length, Bnum = parseInt($(".kuAlert .Bnum").text());
            $(".kuAlert .Anum").text(len).toggleClass("cur", len > Bnum ? true : false);

            if (len > Bnum) {
                $('#textarea .bottom_btn .fabu').css('background', '#eee')
                $('#textarea .bottom_btn .fabu').css('color', '#bbb')
                fabuDis = true
            } else {
                $('#textarea .bottom_btn .fabu').css('background', '#EB413D')
                $('#textarea .bottom_btn .fabu').css('color', '#fff')
                fabuDis = false
            }

            $("#submitbtn").attr("data-click", len > Bnum ? true : false);
        },

    }


    zpHtml.GetRating()

})(jQuery);