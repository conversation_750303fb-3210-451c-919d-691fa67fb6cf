/*弹层*/
.ku_pop_mask {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: .6;
    filter: alpha(opacity=60);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 20000;
}

.ku_pop_mask {
    _height: 1000px;
    _position: absolute;
    _top: expression(eval(document.documentElement.scrollTop));
}

.ku_pop_layer {
    margin: -283px 0 0 -550px;
    padding: 0 30px;
    width: 740px;
    height: auto;
    overflow: hidden;
    background-color: #fff;
    overflow: visible;
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 30000;
}

.ku_pop_layer {
    font-family: 'Microsoft YaHei';
    text-align: left;
    border-radius: 8px;
    _position: absolute;
    _top: expression(eval(document.documentElement.scrollTop+400));
}

.ku-pop-top {
    margin-left: -30px;
    padding: 10px 10px 10px 30px;
    width: 760px;
    /* height: 40px; */
    overflow: hidden;
    background-color: #f8f8f8;
    /* border-bottom: 1px solid #f2f2f3; */
    border-radius: 10px 10px 0 0;
}

.ku-pop-top-img {
    float: left;
    width: 53px;
    height: 40px;
    overflow: hidden;
}

.ku-pop-top-img img {
    float: left;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.ku-pop-top-tit {
    float: left;
    width: auto;
    /* height: 40px;
    line-height: 40px; */
    color: #444;
    font-size: 16px;
    font-weight: bold;
    overflow: hidden;
}

/* .ku-pop-top a.ku_pop_close {
    float: right;
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    color: #ccc;
    font-size: 30px;
    font-weight: bold;
    font-family: SimSun;
} */

/* .ku-pop-top a.ku_pop_close:hover {
    color: #2aae68;
} */

.ku-pop-select {
    padding-top: 15px;
    width: 740px;
    height: auto;
    overflow: hidden;
}

.ku-pop-nav {
    float: left;
    padding-top: 5px;
    width: 198px;
    height: 47px;
    overflow: hidden;
}

.ku-pop-nav a {
    float: left;
    margin-right: 10px;
    display: inline-block;
    width: 78px;
    height: 34px;
    line-height: 34px;
    color: #666;
    font-size: 15px;
    text-align: center;
}

.ku-pop-nav a {
    border: 1px solid #ccc;
    border-radius: 4px;
}

.ku-pop-nav a.stop {
    color: #aaa;
    cursor: default;
}

.ku-pop-nav a.cur {
    color: #fff;
    background-color: #2aae68;
    border-color: #26a260;
}

.ku-pop-con {
    float: left;
    width: 542px;
    height: auto;
    overflow: hidden;
}

.ku-pop-con .dafen {
    width: 542px;
    height: 28px;
    overflow: hidden;
}

.ku-pop-con .dafen .dfen {
    float: left;
    width: auto;
    height: 22px;
    overflow: hidden;
}

.ku-pop-con .dafen .dfen .dftit {
    float: left;
    display: inline-block;
    line-height: 22px;
    color: #888;
    font-size: 14px;
}

.ku-pop-con .dafen .dfen a {
    float: left;
    display: inline-block;
    width: 17px;
    height: 22px;
    background: url(//image.gamersky.com/webimg15/zp/xin7.png) 0 -2px no-repeat;
}

.ku-pop-con .dafen .dfen a.cur {
    background-position-y: -28px;
}

.ku-pop-con .dafen .dfen .dftxt {
    float: left;
    padding-left: 5px;
    display: inline-block;
    line-height: 22px;
    color: #2aae68;
    font-size: 14px;
}

.ku-pop-con .fuxuan {
    width: 572px;
    height: auto;
    overflow: hidden;
}

.ku-pop-con .fuxuan .tit {
    float: left;
    width: auto;
    height: 22px;
    line-height: 22px;
    color: #888;
    font-size: 14px;
    overflow: hidden;
}

.ku-pop-con .fuxuan .fxuanlist {
    width: auto;
    height: auto;
    overflow: hidden;
}

.ku-pop-con .fuxuan .fxuanlist li {
    float: left;
    display: inline-block;
}

.ku-pop-con .fuxuan .fxuanlist a {
    float: left;
    margin: 0 28px 2px 0;
    padding-left: 21px;
    display: inline-block;
    width: auto;
    height: 22px;
    line-height: 22px;
    color: #888;
    font-size: 14px;
}

.ku-pop-con .fuxuan .fxuanlist a {
    background: url(//image.gamersky.com/webimg15/zp/int2.png?01) 0 -44px no-repeat;
}

.ku-pop-con .fuxuan .fxuanlist a.stop {
    color: #aaa;
    cursor: default;
}

.ku-pop-con .fuxuan .fxuanlist a.cur {
    color: #2aae68;
    background-position-y: -66px;
}

.ku-pop-textarea {
    margin-bottom: 25px;
    width: 740px;
    height: auto;
    overflow: hidden;
}

.ku-pop-textarea .tit {
    width: 740px;
    height: 34px;
    overflow: hidden;
}

.ku-pop-textarea .tit h4 {
    float: left;
    display: inline-block;
    line-height: 34px;
    color: #333;
    font-size: 17px;
    font-weight: normal;
}

.ku-pop-textarea .tit .num {
    float: right;
    line-height: 34px;
    color: #999;
    font-size: 12px;
}

.ku-pop-textarea .con {
    padding: 10px 0 10px 18px;
    width: 720px;
    height: 78px;
    border: 1px solid #ccc;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.ku-pop-textarea .con .textarea-code {
    padding: 0 10px 0 0;
    width: 710px;
    height: 100%;
    line-height: 26px;
    color: #333;
    font-size: 14px;
    outline: 0;
    overflow-y: auto;
    position: relative;
}

.ku-pop-textarea .con .textarea-code a[href='javascript:void(0);'] {
    color: #666;
    font-size: 14px;
    background-color: #000;
}

.ku-pop-textarea .con .textarea-html {
    display: none;
}

.ku-pop-textarea .con a.space {
    display: block;
    width: 15px;
    height: 15px;
    position: absolute;
    right: 0;
    bottom: 0;
    background: url(//image.gamersky.com/webimg15/zp/spreads.png) no-repeat;
}

.ku-pop-textarea .bot {
    /* padding: 3px 0; */
    width: 740px;
    height: 26px;
    overflow: hidden;
}

.ku-pop-textarea .kuEditor {
    float: left;
    width: auto;
    height: 26px;
    overflow: hidden;
    *display: none;
}

.ku-pop-textarea .kuEditor a {
    float: left;
    margin-right: 18px;
    display: inline-block;
    width: auto;
    height: 26px;
    line-height: 26px;
    color: #a1a1a1;
    font-size: 14px;
}

/* .ku-pop-textarea .kuEditor a {
    background: url(//image.gamersky.com/webimg15/zp/editor-a.png) no-repeat;
} */

.ku-pop-textarea .kuEditor a.a1 {
    padding-left: 20px;
    /* background-position-x: 0; */
    background: url('https://image.gamersky.com/webimg13/jiacu.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a2 {
    padding-left: 22px;
    /* background-position-x: -63px; */
    background: url('https://image.gamersky.com/webimg13/xiahuaxian.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a3 {
    padding-left: 18px;
    /* background-position-x: -144px; */
    background: url('https://image.gamersky.com/webimg13/xieti.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a4 {
    padding-left: 22px;
    /* background-position-x: -204px; */
    background: url('https://image.gamersky.com/webimg13/shanchuxian.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a5 {
    padding-left: 24px;
    /* background-position-x: -283px; */
    background: url('https://image.gamersky.com/webimg13/yincang.svg') no-repeat;
    background-position-y: center;
}
.ku-pop-textarea .kuEditor a.a1:hover,
.ku-pop-textarea .kuEditor a.a1.cur{
    padding-left: 20px;
    /* background-position-x: 0; */
    background: url('https://image.gamersky.com/webimg13/jiacu_cur.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a2:hover,
.ku-pop-textarea .kuEditor a.a2.cur {
    padding-left: 22px;
    /* background-position-x: -63px; */
    background: url('https://image.gamersky.com/webimg13/xiahuaxian_cur.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a3:hover,
.ku-pop-textarea .kuEditor a.a3.cur {
    padding-left: 18px;
    /* background-position-x: -144px; */
    background: url('https://image.gamersky.com/webimg13/xieti_cur.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a4:hover,
.ku-pop-textarea .kuEditor a.a4.cur {
    padding-left: 22px;
    /* background-position-x: -204px; */
    background: url('https://image.gamersky.com/webimg13/shanchuxian_cur.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a.a5:hover,
.ku-pop-textarea .kuEditor a.a5.cur {
    padding-left: 24px;
    /* background-position-x: -283px; */
    background: url('https://image.gamersky.com/webimg13/yincang_cur.svg') no-repeat;
    background-position-y: center;
}

.ku-pop-textarea .kuEditor a:hover,
.ku-pop-textarea .kuEditor a.cur {
    color: #000;
    font-weight: bold;
    /* background-position-y: -26px; */
}

.ku-pop-textarea .kuAlert {
    float: right;
    width: auto;
    height: 26px;
    line-height: 26px;
    color: #aaa;
    font-size: 13px;
    overflow: hidden;
}

.ku-pop-textarea .kuAlert span.Anum {
    margin-right: 2px;
}

.ku-pop-textarea .kuAlert span.Bnum {
    margin-left: 2px;
}

.ku-pop-textarea .kuAlert span.cur {
    color: #f00;
}

.ku-pop-label {
    margin-bottom: 20px;
    width: 100%;
    height: auto;
    overflow: hidden;
}

.ku-pop-label .title {
    height: 32px;
    line-height: 32px;
    color: #333;
    font-size: 17px;
    overflow: hidden;
}

.ku-pop-label .label {
    margin-bottom: 10px;
    width: 738px;
    min-height: 30px;
    max-height: 87px;
    border: 1px solid #ccc;
    border-radius: 3px;
    overflow-x: hidden;
    overflow-y: scroll;
    position: relative;
}

.ku-pop-label .label-con {
    width: 100%;
    padding-top: 3px;
    padding-left: 3px;
    overflow: hidden
}

.ku-pop-label .label-con span {
    padding: 0 10px;
    display: block;
    width: auto;
    height: 26px;
    line-height: 26px;
    color: #aaa;
    font-size: 14px;
}

.ku-pop-label .label-con .bq {
    float: left;
    margin-right: 4px;
    margin-bottom: 4px;
    display: inline-block;
    width: auto;
    height: 22px;
    border: 1px solid #9cd8b8;
    border-radius: 3px;
}

.ku-pop-label .label-con .bq div {
    float: left;
    display: inline-block;
    height: 22px;
    line-height: 22px;
}

.ku-pop-label .label-con .bq div.txt {
    padding: 0 8px 0 12px;
    color: #1ea15c;
    font-size: 12px;
    outline: 0;
}

.ku-pop-label .label-con .bq div.gb {
    padding-right: 5px;
    color: #1ea15c;
    font-size: 14px;
    font-family: SimSun;
    cursor: pointer;
}

.ku-pop-label .label-con input {
    width: 730px;
    height: 24px;
    margin: 0;
    border: 0;
}

.ku-pop-label .label-my,
.ku-pop-label .label-used {
    margin-bottom: 6px;
    width: 100%;
    height: auto;
    overflow: hidden;
}

.ku-pop-label .tit {
    float: left;
    width: 72px;
    height: 24px;
    line-height: 24px;
    color: #aaa;
    font-size: 14px;
    overflow: hidden;
}

.ku-pop-label .con {
    float: left;
    width: 640px;
    height: 28px;
    overflow: hidden;
}

.ku-pop-label .con.cur {
    width: 668px;
    height: auto;
}

.ku-pop-label .con.cur #myLabel {
    width: 674px;
}

.ku-pop-label .con.cur #usedLabel {
    width: 674px;
}

.ku-pop-label .con a {
    float: left;
    margin: 0 6px 6px 0;
    padding: 0 12px;
    display: inline-block;
    width: auto;
    height: 22px;
    line-height: 22px;
    color: #1ea15c;
    font-size: 12px;
}

.ku-pop-label .con a {
    border: 1px solid #9cd8b8;
    border-radius: 3px;
}

.ku-pop-label .con a.cur {
    color: #aaa;
    border-color: #ddd;
}

.ku-pop-label a.zhanbtn {
    float: right;
    display: block;
    width: 22px;
    height: 22px;
    line-height: 22px;
    color: #1ea15c;
    font-size: 12px;
    border: 1px solid #9cd8b8;
    border-radius: 3px;
}

.ku-pop-label a.zhanbtn {
    background: url(//image.gamersky.com/webimg15/zp/v.png) center no-repeat;
}

.ku-pop-btn {
    margin-left: -30px;
    padding: 9px 30px 12px;
    width: 740px;
    height: auto;
    border-top: 1px solid #eee;
    overflow: hidden;
}

.ku-pop-btn .con {
    float: left;
    width: 600px;
    height: 40px;
    line-height: 40px;
    color: #f00;
    font-size: 14px;
    text-align: center;
    overflow: hidden;
}

.ku-pop-btn .btn {
    float: right;
    width: auto;
    height: 40px;
    overflow: hidden;
}

.ku-pop-btn .btn a {
    float: left;
    display: inline-block;
    width: 80px;
    height: 36px;
    line-height: 36px;
    color: #fff;
    font-size: 15px;
    text-align: center;
    background-color: #2aae68;
    border-radius: 3px;
}

.ku-pop-btn .btn a:hover {
    background-color: #2fc374;
}

.ku-pop-btn .btn a[data-click="true"] {
    color: #bbb;
    background-color: #eee;
    cursor: default;
}

.kuGameScore {
    margin-bottom: 30px;
    width: 100%;
    height: auto;
    overflow: visible;
    transition: all .25s ease-out;
}

/*玩家评分*/
.wjscore {
    margin-bottom: 15px;
    width: 320px;
    height: auto;
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    border-bottom: 1px solid #ebebeb\9;
}

.wjscore-num {
    float: left;
    margin-right: 10px;
    width: 120px;
    height: 126px;
}

.wjscore-num .num {
    height: 72px;
    line-height: 70px;
    color: #444;
    font-size: 70px;
    font-family: Arial;
    font-weight: bold;
    text-align: center;
}

.wjscore-num .num span {
    display: block;
    width: 100%;
    height: 70px;
    line-height: 70px;
    color: #555;
    font-size: 12px;
    font-family: 'Microsoft YaHei';
    font-weight: normal;
}

.wjscore-num .num span {
    background: url(//image.gamersky.com/webimg15/zp/pf0.png) center center no-repeat;
}

.wjscore-num .num i {
    display: inline-block;
    width: 40px;
    height: 60px;
    background: url(//image.gamersky.com/webimg15/zp/num.png) center 0;
    no-repeat;
    vertical-align: top;
}

.wjscore-num .num i.n0 {
    background-position-y: 0;
}

.wjscore-num .num i.n1 {
    background-position-y: -60px;
}

.wjscore-num .num i.n2 {
    background-position-y: -120px;
}

.wjscore-num .num i.n3 {
    background-position-y: -180px;
}

.wjscore-num .num i.n4 {
    background-position-y: -240px;
}

.wjscore-num .num i.n5 {
    background-position-y: -300px;
}

.wjscore-num .num i.n6 {
    background-position-y: -360px;
}

.wjscore-num .num i.n7 {
    background-position-y: -420px;
}

.wjscore-num .num i.n8 {
    background-position-y: -480px;
}

.wjscore-num .num i.n9 {
    background-position-y: -540px;
}

.wjscore-num .num i.nn {
    width: 30px;
    background-position-y: -600px;
}

.wjscore-num .num i.nm {
    width: 10px;
    background: transparent;
}

.wjscore-num .pin {
    margin: -3px auto 6px;
    width: 80px;
    height: 20px;
}

.wjscore-num .pin .xin {
    width: 80px;
    height: 20px;
    background: url(//image.gamersky.com/webimg15/zp/xin0.png) repeat-x;
}

.wjscore-num .pin .xin div {
    float: left;
    width: 0;
    height: 20px;
    transition: all .25s ease-out;
    background: url(//image.gamersky.com/webimg15/zp/xin0.png) 0 -20px repeat-x;
}

/******************** 1星 *****************/
.wjscore-num .pin .xin div.x0 {
    width: 0;
}

.wjscore-num .pin .xin div.x1 {
    width: 2px;
}

.wjscore-num .pin .xin div.x2 {
    width: 4px;
}

.wjscore-num .pin .xin div.x3 {
    width: 6px;
}

.wjscore-num .pin .xin div.x4 {
    width: 7px;
}

.wjscore-num .pin .xin div.x5 {
    width: 8px;
}

.wjscore-num .pin .xin div.x6 {
    width: 9px;
}

.wjscore-num .pin .xin div.x7 {
    width: 10px;
}

.wjscore-num .pin .xin div.x8 {
    width: 12px;
}

.wjscore-num .pin .xin div.x9 {
    width: 14px;
}

.wjscore-num .pin .xin div.x10 {
    width: 16px;
}

/******************** 2星 *****************/
.wjscore-num .pin .xin div.x11 {
    width: 18px;
}

.wjscore-num .pin .xin div.x12 {
    width: 20px;
}

.wjscore-num .pin .xin div.x13 {
    width: 22px;
}

.wjscore-num .pin .xin div.x14 {
    width: 23px;
}

.wjscore-num .pin .xin div.x15 {
    width: 24px;
}

.wjscore-num .pin .xin div.x16 {
    width: 25px;
}

.wjscore-num .pin .xin div.x17 {
    width: 26px;
}

.wjscore-num .pin .xin div.x18 {
    width: 28px;
}

.wjscore-num .pin .xin div.x19 {
    width: 30px;
}

.wjscore-num .pin .xin div.x20 {
    width: 32px;
}

/******************** 3星 *****************/
.wjscore-num .pin .xin div.x21 {
    width: 34px;
}

.wjscore-num .pin .xin div.x22 {
    width: 36px;
}

.wjscore-num .pin .xin div.x23 {
    width: 38px;
}

.wjscore-num .pin .xin div.x24 {
    width: 39px;
}

.wjscore-num .pin .xin div.x25 {
    width: 40px;
}

.wjscore-num .pin .xin div.x26 {
    width: 41px;
}

.wjscore-num .pin .xin div.x27 {
    width: 42px;
}

.wjscore-num .pin .xin div.x28 {
    width: 44px;
}

.wjscore-num .pin .xin div.x29 {
    width: 46px;
}

.wjscore-num .pin .xin div.x30 {
    width: 48px;
}

/******************** 4星 *****************/
.wjscore-num .pin .xin div.x31 {
    width: 50px;
}

.wjscore-num .pin .xin div.x32 {
    width: 52px;
}

.wjscore-num .pin .xin div.x33 {
    width: 54px;
}

.wjscore-num .pin .xin div.x34 {
    width: 55px;
}

.wjscore-num .pin .xin div.x35 {
    width: 56px;
}

.wjscore-num .pin .xin div.x36 {
    width: 57px;
}

.wjscore-num .pin .xin div.x37 {
    width: 58px;
}

.wjscore-num .pin .xin div.x38 {
    width: 60px;
}

.wjscore-num .pin .xin div.x39 {
    width: 62px;
}

.wjscore-num .pin .xin div.x40 {
    width: 64px;
}

/******************** 5星 *****************/
.wjscore-num .pin .xin div.x41 {
    width: 66px;
}

.wjscore-num .pin .xin div.x42 {
    width: 68px;
}

.wjscore-num .pin .xin div.x43 {
    width: 70px;
}

.wjscore-num .pin .xin div.x44 {
    width: 71px;
}

.wjscore-num .pin .xin div.x45 {
    width: 72px;
}

.wjscore-num .pin .xin div.x46 {
    width: 73px;
}

.wjscore-num .pin .xin div.x47 {
    width: 74px;
}

.wjscore-num .pin .xin div.x48 {
    width: 76px;
}

.wjscore-num .pin .xin div.x49 {
    width: 78px;
}

.wjscore-num .pin .xin div.x50 {
    width: 80px;
}

.wjscore-num .txt {
    height: 20px;
    line-height: 20px;
    color: #888;
    font-size: 12px;
    text-align: center;
}

.wjscore-num .txt span {
    color: #000;
    font-family: Arial;
}

.wjscore-con {
    float: left;
    width: 190px;
    height: auto;
}

.wjscore-con .li-n {
    width: 100%;
    height: 14px;
}

.wjscore-con .li-n div {
    float: left;
}

.wjscore-con .li-n div.num {
    width: 25px;
    height: 14px;
    line-height: 14px;
    color: #bbb;
    font-size: 12px;
}

.wjscore-con .li-n div.jindu {
    margin-top: 2px;
    width: auto;
    height: 10px;
}

.wjscore-con .li-n div.jindu .tiao {
    width: 0;
    height: 10px;
    background-color: #2aae68;
    border-radius: 0 2px 2px 0;
    transition: all .25s ease-out;
}

.wjscore-con .li-n div.bili {
    padding-left: 5px;
    width: auto;
    height: 14px;
    line-height: 14px;
    color: #888;
    font-size: 12px;
}

.wjscore-con .li-c {
    margin-top: 23px;
    width: 100%;
    height: auto;
}

.wjscore-con .li-c div {
    width: auto;
    height: 24px;
    line-height: 24px;
    color: #888;
    font-size: 12px;
}

.wjscore-con .li-c div.p span {
    color: #000;
}

.wjscore-con .li-c div.n a[href="javascript:;"] {
    color: #888;
    cursor: default;
}

.wjscore-con .li-c div.n a[href="javascript:;"]:hover {
    color: #222;
}

.wjscore-bot {
    padding: 8px 0;
    width: 100%;
    height: auto;
    border-top: 1px dotted rgba(0, 0, 0, .2);
    border-top: 1px dotted #bdbdbd\9;
}

.wjscore-bot .p {
    width: auto;
    height: 24px;
    line-height: 24px;
    color: #888;
    font-size: 12px;
}

.wjscore-bot .p span {
    color: #000;
}

.wjscore-bot .p a {
    color: #888;
}

.wjscore-bot .p a:hover {
    color: #222;
}

.wjscore-bot .n {
    width: auto;
    height: 24px;
    line-height: 24px;
    color: #000;
    font-size: 12px;
}

.wjscore-bot .n i {
    display: inline-block;
    vertical-align: middle;
    font-style: normal;
}

.wjscore-bot .n i.rz {
    margin: -3px 5px 0 2px;
    width: 12px;
    height: 14px;
    background: url(//image.gamersky.com/webimg15/zp/v1/rz2.png) no-repeat;
}

.wjscore-bot .n i.wh {
    float: right;
    margin-top: 5px;
    width: 14px;
    height: 14px;
    cursor: pointer;
    background: url(//image.gamersky.com/webimg15/zp/v1/wh.png) no-repeat;
}

/*问题弹窗*/
.wh_layer {
    margin-left: -1.5px;
    width: 17px;
    height: 14px;
}

.wh_layer .wh_vvv {
    width: 17px;
    height: 14px;
    position: absolute;
    margin-top: 14px;
    z-index: 10;
    cursor: default;
}

.wh_layer .wh_vvv {
    background: url(//image.gamersky.com/webimg15/zp/v1/vv.png) no-repeat;
}

.wh_layer .wh_con {
    padding: 20px;
    width: 220px;
    height: auto;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;
    cursor: default;
}

.wh_layer .wh_con {
    position: absolute;
    margin-top: 28px;
    margin-left: -120.5px;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, .4);
}

.wh_layer .wh_con h3 {
    margin: 0 0 15px;
    padding-left: 38px;
    height: 32px;
    line-height: 32px;
    color: #222;
    font-size: 18px;
}

.wh_layer .wh_con h3 {
    background: url(//image.gamersky.com/webimg15/zp/v1/rz1.png) 0 center no-repeat;
}

.wh_layer .wh_con p {
    margin: 0;
    line-height: 22px;
    color: #666;
    font-size: 14px;
    text-align: justify;
}

.wh_layer.cur .wh_vvv {
    margin-top: -14px;
    background-position-y: -14px;
}

.wh_layer.cur .wh_con {
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, .4);
}

/*未上市显示期待数量*/
.wjscore-qdpf {
    padding-bottom: 25px;
    width: 320px;
    height: auto;
}

.wjscore-qdpf .qdnum {
    float: left;
    width: 116px;
    height: 50px;
    text-align: center;
}

.wjscore-qdpf .qdnum .num {
    margin-top: -2px;
    height: 36px;
    line-height: 36px;
    color: #24a661;
    font-size: 36px;
    font-family: Arial;
    font-weight: bold;
}

.wjscore-qdpf .qdnum .txt {
    height: 14px;
    line-height: 14px;
    color: #aaa;
    font-size: 12px;
}

.wjscore-qdpf .qdcon {
    float: left;
    padding-left: 15px;
    width: 188px;
    height: 48px;
    overflow: visible;
    border-left: 1px dotted rgba(0, 0, 0, .2);
    border-left: 1px dotted #bdbdbd\9
}

.wjscore-qdpf .qdcon .p {
    margin-top: -4px;
    height: 22px;
    line-height: 18px;
    color: #aaa;
    font-size: 12px;
}

/*我的评分*/
.myscore {
    margin: 0 auto;
    width: 320px;
    height: auto;
}

.myscore li {
    width: 100%;
}

.myscore li.img {
    float: left;
    width: 60px;
    height: 60px;
}

.myscore li.img img {
    width: 50px;
    height: 50px;
    border-radius: 5px;
}

.myscore li.fen {
    float: left;
    width: 260px;
    height: auto;
}

.myscore li.fen .ftxt {
    width: 260px;
    height: 25px;
}

.myscore li.fen .ftxt .wtxt {
    float: left;
    width: auto;
    height: 20px;
    line-height: 20px;
    color: #444;
    font-size: 14px;
}

.myscore li.fen .ftxt .wbtn {
    float: right;
    width: auto;
    height: 20px;
}

.myscore li.fen .ftxt .wbtn a {
    float: left;
    margin-left: 10px;
    display: inline-block;
    width: auto;
    height: 20px;
    line-height: 20px;
    color: #aaa;
    font-size: 14px;
}

.myscore li.fen .ftxt .wbtn a:hover {
    color: #1ea15b;
}

.myscore li.fen .fxin {
    margin-bottom: 3px;
    width: 260px;
    height: auto;
}

.myscore li.fen .datetime {
    width: auto;
    height: 32px;
}

.myscore li.fen .datetime a {
    float: left;
    margin-right: 10px;
    display: inline-block;
    width: 54px;
    height: 24px;
    line-height: 24px;
    color: #fff;
    font-size: 12px;
    text-align: center;
}

.myscore li.fen .datetime a {
    background-color: #2aae68;
    border: 1px solid #2aae68;
    border-radius: 3px;
}

.myscore li.fen .datetime a:hover {
    color: #21a45f;
    background-color: transparent;
    border-color: #71c398;
}

.myscore li.fen .dfen {
    width: auto;
    height: 26px;
}

.myscore li.fen .dfen span {
    float: left;
    display: inline-block;
    width: auto;
    height: 26px;
    line-height: 26px;
}

.myscore li.fen .dfen span.dftit {
    color: #333;
}

.myscore li.fen .dfen span.dftxt {
    padding-left: 5px;
    color: #333;
    font-size: 14px;
}

.myscore li.fen .dfen a {
    float: left;
    display: inline-block;
    width: 17px;
    height: 26px;
    background: url(//image.gamersky.com/webimg15/zp/xin7.png) no-repeat;
}

.myscore li.fen .dfen a.cur {
    background-position-y: -26px;
}

.myscore li.fen .dfen.wss span.tit {
    color: #bbb;
}

.myscore li.fen .dfen.wss a {
    cursor: default;
    background: url(//image.gamersky.com/webimg15/zp/xin7.png) 0 2px no-repeat;
}

.myscore li.fen .fbtn {
    margin-bottom: 6px;
    width: 260px;
    height: 22px;
}

.myscore li.fen .fbtn a {
    float: left;
    margin-right: 10px;
    display: inline-block;
    width: auto;
    height: 22px;
    line-height: 22px;
}

.myscore li.fen .fbtn a.pybtn {
    padding-left: 18px;
    background: url(//image.gamersky.com/webimg15/zp/pybtn.png) 0 -22px no-repeat;
}

.myscore li.fen .fbtn a.pybtn:hover {
    background-position-y: -22px;
}

.myscore li.fen .fbtn a.editbtn,
.myscore li.fen .btn a.delbtn {
    color: #aaa;
}

.myscore li.fen .fbtn a.editbtn:hover,
.myscore li.fen .btn a.delbtn:hover {
    color: #1ea15b;
}

.myscore li.fen .fbtn .inAudit {
    float: left;
    padding-left: 20px;
    display: inline-block;
    width: auto;
    height: 22px;
    line-height: 22px;
    color: #ff5a00;
    font-size: 14px;
}

.myscore li.fen .fbtn .inAudit {
    background: url(//image.gamersky.com/webimg15/zp/ino.png) 0 center no-repeat;
}

.myscore li.doneTxt {
    padding-bottom: 6px;
    width: 320px;
    height: auto;
    line-height: 20px;
    color: #aaa;
    font-size: 14px;
}

.myscore li.myTag {
    padding-bottom: 6px;
    width: 320px;
    height: auto;
    line-height: 20px;
    color: #aaa;
    font-size: 14px;
}

.myscore li.myTag a {
    margin-right: 5px;
    color: #aaa;
}

.myscore li.myTag a:hover {
    color: #000;
}

.myscore li.myremark {
    width: 320px;
    height: auto;
}

.myscore li.myremark .con {
    width: auto;
    height: 78px;
    line-height: 26px;
    color: #666;
    text-indent: 35px;
    text-align: justify;
}

.myscore li.myremark .con {
    background: url(//image.gamersky.com/webimg15/zp/hao3.png) 0 5px no-repeat;
}

.myscore li.myremark .con p {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
}

.myscore li.myremark .con a[href='javascript:void(0);'] {
    color: #bbb;
    background-color: #bbb;
}

.myscore li.myremark .con a[href='javascript:void(0);']:hover {
    color: #fff;
}

.myscore li.myremark .con i {
    font-style: italic !important;
}

.myscore li.myremark .con.cur {
    height: auto;
}

.myscore li.myremark .con.cur p {
    display: block;
}

.myscore li.myremark .btn {
    width: auto;
    height: 20px;
    text-align: center;
}

.myscore li.myremark .btn a {
    display: inline-block;
    width: 50px;
    height: 20px;
    background: url(//image.gamersky.com/webimg15/zp/zhanbtn.png) center 0 no-repeat;
}

.myscore li.myremark .btn a.cur {
    background-position-y: -26px;
}

/*确定删除*/
.ppp_mask {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: .5;
    filter: alpha(opacity=50);
    position: fixed;
    _position: absolute;
    top: 0;
    left: 0;
    z-index: 2000;
}

.ppp_mask {
    _height: 1000px;
    _position: absolute;
    _top: expression(eval(document.documentElement.scrollTop));
}

.ppp_layer {
    margin: -90px 0 0 -225px;
    padding: 25px 25px 0 25px;
    width: 400px;
    height: 155px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, .5);
}

.ppp_layer {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 3000;
    _position: absolute;
    _top: expression(eval(document.documentElement.scrollTop+400));
}

.ppp_layer .con {
    width: auto;
    height: 105px;
    line-height: 26px;
    color: #333;
    font-size: 15px;
}

.ppp_layer .con center {
    text-align: center;
}

.ppp_layer .con span {
    color: #f35600;
}

.ppp_layer .btn {
    width: auto;
    height: 50px;
}

.ppp_layer .btn a {
    margin-left: 5px;
    display: inline-block;
    width: 95px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    font-size: 15px;
    border-radius: 2px;
    text-align: center;
}

.ppp_layer .btn a.cancelbtn {
    float: right;
    background-color: #ccc;
}

.ppp_layer .btn a.determinebtn {
    float: right;
    background-color: #666;
}

.ppp_layer .btn a.cancelbtn:hover {
    background-color: #bababa;
}

.ppp_layer .btn a.determinebtn:hover {
    background-color: #6e6e6e;
}