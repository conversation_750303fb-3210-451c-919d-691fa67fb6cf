function setSearchKeys() {
        var linkType = 'soso';
        var gameName = [
            {
                name: '剑星',
                linkName: '剑星'
            },{
                name: '多重人生',
                linkName: '多重人生'
            },{
                name: '捞女游戏',
                linkName: '捞女游戏'
            },{
                name: '黑神话：悟空',
                linkName: '黑神话悟空'
            },{
                name: '光与影：33号远征队',
                linkName: '光与影33号远征队'
            },{
                name: '开球！REMATCH',
                linkName: '开球REMATCH'
            },{
                name: '葛叶雷道复刻版',
                linkName: '葛叶雷道复刻版'
            },{
                name: '幻想生活i',
                linkName: '幻想生活i转圈圈的龙和偷取时间的少女'
            },{
                name: '匹诺曹的谎言',
                linkName: '匹诺曹的谎言'
            },{
                name: '莱恩岛生存指南',
                linkName: '莱恩岛生存指南'
            }
                    ];
        var SearchHtm = '';
        var linkName = '';

        if (linkType == 'baidu') {
            linkName = 'https://www.baidu.com/s?ie=utf-8&wd=';
            $('#search-form .form').attr('data-action', 'https://www.baidu.com/s?ie=utf-8');
        } else {
            linkName = 'http://soso.gamersky.com/cse/search?s=3068275339727451251&q=';
            $('#search-form .form').parent().data('action', 'http://soso.gamersky.com/cse/search?s=3068275339727451251')
        }
        $.each(gameName, function (n, d) {
            if (linkType == 'baidu') {
                SearchHtm += '<a class="searchWord" target="_blank" href="' + linkName + encodeURIComponent(d.linkName + ' site:www.gamersky.com') + '"><i></i>' + d.name + '</a>'
            } else if (linkType == 'soso') {
                SearchHtm += '<a class="searchWord" target="_blank" href="'  + linkName + d.linkName + '"><i></i>' + d.name + '</a>'
            }
        });

        $('.searchItems').html(SearchHtm);
    }
    setSearchKeys()