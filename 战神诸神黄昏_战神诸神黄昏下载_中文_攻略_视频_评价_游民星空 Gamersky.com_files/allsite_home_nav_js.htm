var isTrim = function (s) { return !s.replace(/(^\s*)|(\s*$)/g, ""); }; //清除空格
var getUrlParam = function (name, values) {
    var hash = window.location.hash;
    if (!isTrim(hash)) {
        hash = hash.replace("#", "");
        if (!isTrim(hash)) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = hash.match(reg);
            if (r != null) return unescape(r[2]); return values;
        }
    }
    return values;
}
var QZbgHtm = '<div class="QZnav" id="QZnav">';
QZbgHtm += '<div class="topbar">';
QZbgHtm += '<div class="topbar1">';
// 游戏库
if (decodeURIComponent(window.location.href).indexOf('游戏库UI改版') > 0 || decodeURIComponent(window.location.href).indexOf('ku.gamersky.com') > 0) {
    QZbgHtm += '<div class="QZ-home"><a class="alik ymxk yxk" href="https://ku.gamersky.com/" target="_blank"></a></div>';
} else {
    QZbgHtm += '<div class="QZ-home"><a class="alik ymxk" href="https://www.gamersky.com/" target="_blank">GAMERSKY<em>.COM</em></a></div>';
}
QZbgHtm += '<div class="QZ-nav">';
QZbgHtm += '<i>|</i><a class="alik link" href="https://www.gamersky.com/pcgame/" target="_blank"> 单机游戏</a>';
QZbgHtm += '<i>|</i><a class="alik link" href="https://ol.gamersky.com/" target="_blank">网络游戏</a>';
QZbgHtm += '<i>|</i><a class="alik link" href="https://tv.gamersky.com/" target="_blank">电视游戏</a>';
QZbgHtm += '<i>|</i><a class="alik link" href="https://shouyou.gamersky.com/" target="_blank">手机游戏</a>';
QZbgHtm += '<i>|</i><a class="alik link" href="https://acg.gamersky.com/" target="_blank">动漫星空</a>';
QZbgHtm += '<i>|</i><a class="alik link" href="http://club.gamersky.com/" target="_blank">游民社区</a>';
QZbgHtm += '</div>';
QZbgHtm += '</div>';
QZbgHtm += '<div class="Search">';
QZbgHtm += '<div class="SearchL cur" id="search-form">';
QZbgHtm += '<div class="radio">'
QZbgHtm += '<div for="S02" class="S02">本站</div>';
QZbgHtm += '<div class="S02_options" style="display:none">';
QZbgHtm += '<div class="S02_option" data-value="本站">搜本站</div>';
QZbgHtm += '<div class="S02_option" data-value="百度">搜百度</div>';
QZbgHtm += '</div>';
QZbgHtm += '</div>';

QZbgHtm += '<div class="form block" data-action="https://so.gamersky.com/">';
QZbgHtm += '<input class="hiddenInput" type="text"  style="display:none">';
QZbgHtm += `<input class="Sinput" type="text" name="s" value="" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');">`;
QZbgHtm += '<input class="Sbutton" type="submit" value="本站" autocomplete="off">';
QZbgHtm += '</div>';
QZbgHtm += '</div>';
QZbgHtm += '<div class="SearchList">';
QZbgHtm += '<div class="SearchTitle">热门搜索</div>';
QZbgHtm += '<div class="searchItems">';
QZbgHtm += '</div>';
QZbgHtm += '</div>';
QZbgHtm += '</div>';
QZbgHtm += '<div class="topbar2">';

QZbgHtm += '<div class="QZ-links">';
// 游戏库不显示深浅切换
if (decodeURIComponent(window.location.href).indexOf('ku.gamersky.com') <= -1) {
    QZbgHtm += '<span class="navtt">';
    QZbgHtm += '<a class="alik theme" href="javascript:;" id="switch"><i></i>深浅切换</a>';
    QZbgHtm += '</span>';
}
QZbgHtm += '<span class="navtt">';
QZbgHtm += '<p class="alik app"><i></i>客户端</p>';
QZbgHtm += '<div class="topbar-menu app">';
QZbgHtm += '<ul class="topbar-appul">';
QZbgHtm += '<li class="con-l">';
QZbgHtm += '<a class="applogo" href="https://a.gamersky.com/index.html?3&3" target="_blank"></a>';
QZbgHtm += '<h3><a href="https://a.gamersky.com/index.html?3&3" target="_blank"> 游民星空APP</a></h3>';
QZbgHtm += '<div class="txt">随时随地，获取最新游戏资讯攻略</div>';
QZbgHtm += '<div class="lik">';
QZbgHtm += '<a href="https://at.umeng.com/WT15jC" tit="0jaObe" target="_blank" data-itemid="995690" class="countHit countHitSql android"> Android版下载</a>';
QZbgHtm += '<i>|</i>';
QZbgHtm += '<a href="https://at.umeng.com/mqO5zq" tit="D419PD" target="_blank" data-itemid="995690" class="countHit countHitSql ios"> iOS版下载</a>';
QZbgHtm += '</div></li><li class="con-r"><img src="//image.gamersky.com/webimg15/a/33/code110x110_homenav.png" /></li>';
QZbgHtm += '</ul>';
QZbgHtm += '</div>';
QZbgHtm += '</span>';
QZbgHtm += '<span class="navtt">';
QZbgHtm += '<a class="alik tongzhi" href="javascript:;"><i></i>消息</a>';
QZbgHtm += '</span>';
QZbgHtm += '</div>';
QZbgHtm += '<div class="noLogin" id="QZNotLog">';
QZbgHtm += '<a class="alik Login" href="javascript:;">登录</a>';
QZbgHtm += '<a class="alik Reg" href="https://i.gamersky.com/user/register" target="_blank">注册</a>';
QZbgHtm += '</div>';
QZbgHtm += '<div class="okLogin" id="QZHasLog">';
QZbgHtm += '<a class="alik uname" href="https://i.gamersky.com/home/" target="_blank"><img src="//image.gamersky.com/avatar/original/game/game001.jpg"></a>';
QZbgHtm += '<div class="userLayer" id="QZuserLayer">';
QZbgHtm += '<div class="zx"></div>';
QZbgHtm += '<ul class="topbar-usercon">';
QZbgHtm += '<li class="user1">';
QZbgHtm += '<div class="img"><a href="http://i.gamersky.com/home/" target="_blank"><img src="//image.gamersky.com/avatar/original/game/game001.jpg"></a></div>';
QZbgHtm += '<div class="name"></div>';
QZbgHtm += '<div class="binding">';
QZbgHtm += '<a href="https://i.gamersky.com/user/info/emailbind" class="email" data-tit="邮箱" target="_blank" title="点击绑定"></a>';
QZbgHtm += '<a href="https://i.gamersky.com/user/info/mobilebind" class="phone" data-tit="手机" target="_blank" title="点击绑定"></a>';
QZbgHtm += '<a href="https://i.gamersky.com/user/info/baseconnect/" class="weixin" data-tit="微信" target="_blank" title="点击绑定"></a>';
QZbgHtm += '<a href="https://i.gamersky.com/user/info/baseconnect/" class="qq" data-tit="QQ" target="_blank" titl= 点击绑定"></a>';
// <!-- QZbgHtm+='<a href="https://i.gamersky.com/user/info/baseconnect/" class="sina" data-tit="新浪" target="_blank" title="点击绑定"></a>'; -->
QZbgHtm += '</div>';
QZbgHtm += '</li>';
QZbgHtm += '<li class="user2">';
QZbgHtm += '<a class="jb" href="https://i.gamersky.com/store" target="_blank"><i></i> <p> 金币商城</p></a>';
QZbgHtm += '<a class="zl" href="https://i.gamersky.com/author" target="_blank"><i></i><p> 创作中心</p></a>';
QZbgHtm += '</li>';
QZbgHtm += '<li class="user3">';
QZbgHtm += '<div class="link"><a href="https://i.gamersky.com/user/info/modifyinfo" target="_blank"> 修改资料</a> | <a href="https://i.gamersky.com/user/info/password" target="_blank"> 修改密码</a></div>';
QZbgHtm += '<div class="ExitSign"><a href="javascript:;" id="QZsign">退出登录</a></div>';
QZbgHtm += '</li>';
QZbgHtm += '</ul>';
QZbgHtm += '</div></div></div></div></div>';

var QZbgHtm1 = '<div class="QZshade"></div>';
var QZbgHtm2 = '<div class="QZlogin">';
QZbgHtm2 += '<a class="QZ-close" href="javascript:;">×</a>';
QZbgHtm2 += '<div class="QZ-button"><span class="QZ-sms QZ-active">短信登录</span><span class="QZ-password">密码登录</span></div>';
QZbgHtm2 += '<div class="sms-login"><div class="QZ-input inp-phone"><input name="userNumber" type="text" id="userNumber" maxlength="35" placeholder="请输入手机号" class="cur"></div><div class="QZ-input code"><input name="code" type="code" id="QZcode" maxlength="35" placeholder="请输入验证码" class="cur"> <button id="code-btn" class="code-btn">获取验证码</button></div><div id="mpanel2"></div><div class="QZ-btn"><a id="QZsmsbtn" href="javascript:;">登 录 / 注 册</a></div><div class="QZ-chk"><label for="QZcookie"><input type="checkbox" name="cookie" id="QZcookie" checked="checked">下次自动登录</label></div><div class="QZ-chk"><span>登录即代表同意<a href="//i.gamersky.com/user/agreement/">《游民星空服务条款与隐私条款》</a></span></div></div>';
QZbgHtm2 += '<div class="password-login"><div class="QZ-input"><input name="userName" type="text" id="QZuserName" maxlength="35" placeholder="请输入手机号/邮箱/用户名" class="cur"></div> <div class="QZ-input code"><input name="password" type="password" id="QZpassword" maxlength="35" placeholder="请输入密码" class="cur"><a href="https://i.gamersky.com/user/getpassword" target="_blank" class="QZ-link forgetPaw">忘记密码</a> </div><div class="QZ-btn"><a id="QZbtn" href="javascript:;">登 录</a></div><div class="QZ-chk"><label for="QZcookie"><input type="checkbox" name="cookie" id="QZcookie"  checked="checked">下次自动登录</label><a class="QZ-reg" href="https://i.gamersky.com/user/register" target="_blank">立即注册</a></div><div class="QZ-chk"><span>登录即代表同意<a href="//i.gamersky.com/user/agreement/">《游民星空服务条款与隐私条款》</a></span></div> </div>';
QZbgHtm2 += '<fieldset><legend></legend></fieldset>';
QZbgHtm2 += '<div class="QZ-con"> <a href="javascript:;" id="QZweixinLogin"></a><a href="javascript:;" id="QZqqLogin"></a></div>';
QZbgHtm2 += '</div>';


if (getUrlParam("p", 0) <= 0) { document.write(QZbgHtm); } document.write(QZbgHtm1); document.write(QZbgHtm2);
document.write('<script src="//j.gamersky.com/g/v1/QZnav_n.js"> </script>');
// document.write('<script src="./j.gamersky.com/g/QZnav_n.js"> </script>');
// document.write('<script src="//project-test.gamersky.com/zhongping2_test/j.gamersky.com/g/QZnav_n.js"> </script>');
document.write('<script src="https://www.gamersky.com/autoinc/allsite/v1/search_hot.htm"> </script>');

document.write('<script src="//j.gamersky.com/g/paho-mqtt.js"></script>');
document.write('<script src="//j.gamersky.com/g/login/js/crypto-js.js"></script>');
document.write('<script src="//j.gamersky.com/g/login/js/ase.js"></script>');
document.write('<script src="//j.gamersky.com/g/login/js/verify.js?20220803"></script>');
document.write('<link rel="stylesheet" href="//j.gamersky.com/g/login/css/verify.css"></link>');