/*关注弹窗*/
@keyframes fadeInUp{0%{opacity:0;transform:translateY(10px);}100%{opacity:1;transform:translateY(0);}}
.headlayer {display:none; width:0; height:0; overflow:visible; position:absolute; z-index:1000000;}
.headlayer .headlayer-btn{position: absolute;left:392px;top: 20px;writing-mode: tb-rl;height: 100px; width: 30px;background: #fff;text-align: center;line-height: 34px;border-radius: 0 6px 6px 0;color: #666}
.headlayer .headlayer-btn {
    width: 31px;
    height: 104px;
    background: #ffffff;
    border: 0.5px solid #cccccc;
    border-radius: 0px 6px 6px 0px;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.10); 
}
.headlayer-btn a{
    color: #333;
}
.headlayer-btn a:hover{
    color: #d93a3a;
}
.headlayer .headlayer-vvv {width:17px; height:15px; position:absolute; left:6px; top:-13px; z-index:10; background:url(//image.gamersky.com/webimg15/user/center2/headlayer-arr1.png) no-repeat;}
.headlayer .headlayer-con {overflow: visible;padding:20px 20px 0; width:351px; height:295px; background-color:#fff; border-radius:5px; box-shadow:1px 1px 5px 0 rgba(0,0,0,0.3);}
.headlayer .headlayer-con {position:absolute; left:0; top:0;}
.headlayer .headlayer-con {
    background: #ffffff;
    border: 0.5px solid #cccccc;
    box-shadow: 0px -2px 8px 0px rgba(0,0,0,0.10); 
 }
.headlayer .headlayer-list {width:351px; min-height:36px; max-height:90px; font-size:0; text-align:center;position: absolute;margin-top: 102px;}
.headlayer .headlayer-list img {margin:0 1px; width:78px!important; height:90px!important; border-radius:0!important;}
.headlayer .headlayer-pic {padding:2px; width:60px; height:60px; background-color:#fff; border-radius:50%; overflow:visible; position:relative;margin-left: 16px;}
/* .headlayer .headlayer-pic img {width:60px!important; height:60px!important; border-radius:50%;} */
.userHeadFrameItem {top:-13px;left:-13px;position: absolute;width: 90px;height: 90px;}
.headlayer .headlayer-pic a.zheng {display:block; width:24px; height:24px; background-color:#fff; border-radius:50%; position:absolute; right:0; bottom:0;}
.headlayer .headlayer-pic a.zheng img {margin:5px 0 0 3px; width:18px!important; height:14px!important;}
.headlayer .headlayer-pic a.zheng div {margin-left:-84px;}
.userHeadFrame img {width: 90px;height: 90px;}
.headlayer .headlayer-tit {width:auto; height:34px; text-align:left;margin-left: 15px;}
.headlayer .headlayer-tit .name {padding:0; display:inline-block; width:auto; height:34px; line-height:34px; color:#333; font-size:16px; font-family:'Microsoft YaHei';}
.headlayer .headlayer-tit .level {margin-left: 0;display:inline-block; padding:0 6px; padding-bottom: 2px ;color:#777; font-size:13px; font-family:simsun; font-weight:700;  border-radius:8px;}
.headlayer .headlayer-tit .level {width:39px; height:19px; line-height:16px!important;margin-left: 10px;}
.headlayer .headlayer-tit .level:hover {text-decoration:none!important;}
.headlayer .headlayer-guanzhu {padding:5px 0 15px; height:auto; text-align:center; font-size:0;}
.headlayer .headlayer-guanzhu a {margin:0 4px; margin-top:16px; display:inline-block; width:180px; height:30px; line-height:30px; color:#fff; font-size:14px; font-family:'Microsoft YaHei';}
.headlayer .headlayer-guanzhu a {background-color:#2786ed; text-align:center; border-radius:5px;}
.headlayer .headlayer-guanzhu a.cur {color:#fff; background-color:#b4bac0;}
.headlayer .headlayer-guanzhu a:hover {text-decoration:none!important;}
.headlayer .headlayer-state {margin:0 -20px; padding:0 30px; width:331px; height:auto; border-top:1px solid #eee;margin-top: 214px;position: absolute;}
.headlayer .headlayer-state .statelist {width:300px; height:44px; border:0;margin:0;border-collapse:collapse;border-spacing:0;}
.headlayer .headlayer-state .statelist td {border:0; text-align:center;}
.headlayer .headlayer-state .statelist span {display:inline-block; width:auto; height:44px; line-height:44px; text-align:center;}
.headlayer .headlayer-state .statelist span.tit {padding-right:2px; color:#999; font-size:14px; font-family:'Microsoft YaHei';}
.headlayer .headlayer-state .statelist span.num {padding-left:3px; color:#444; font-size:16px; font-family:Arial; font-weight:700;}
.headlayer .headlayer-state .statelist a {width:auto!important; height:auto!important;}
.headlayer .headlayer-state .statelist a:hover span {color:#5ca4f1;}
.headlayer.cur .headlayer-vvv {top:auto; bottom:-13px; background:url(//image.gamersky.com/webimg15/user/center2/headlayer-arr2.png) no-repeat;}
.headlayer.cur .headlayer-con {top:auto; bottom:0; box-shadow:1px 0 5px 0 rgba(0,0,0,0.3);}

.statelist tr td {margin-left: 12px;border:0;}
.statelist .tit {width: 28px;height: 20px;font-size: 14px;font-family: PingFangSC, PingFangSC-Regular;font-weight: 400;color: #666666;line-height: 20px;}
.headlayer-userInfo {width: 351px;overflow: visible;display: flex;position: absolute;margin-top: 16px;}
.headlayer-infoList {position: absolute;margin-left: 78px;}
.headlayer-pic {position: absolute;margin-top: 0px;}
.statelist {margin-left: 0px;}
.statelist a{min-width:60px;width: auto;height:auto!important;}
.num.gzNum {width: 9px;height: 24px;font-size: 16px;font-family: PingFangSC, PingFangSC-Medium;font-weight: 500;text-align: left;color: #222222;line-height: 24px;margin-left: 5px;}
.num.fsNum {width: 9px;height: 24px;font-size: 16px;font-family: PingFangSC, PingFangSC-Medium;font-weight: 500;text-align: left;color: #222222;line-height: 24px;margin-left: 5px;}
.num.dtNum {width: 9px;height: 24px;font-size: 16px;font-family: PingFangSC, PingFangSC-Medium;font-weight: 500;text-align: left;color: #222222;line-height: 24px;margin-left: 5px;}
.num.wzNum {width: 9px;height: 24px;font-size: 16px;font-family: PingFangSC, PingFangSC-Medium;font-weight: 500;text-align: left;color: #222222;line-height: 24px;margin-left: 5px;}

.ccmt_head .headlayer-pic img {
    width: 60px;
    height: 60px;
}

/*游戏名片弹窗提示*/
.gamepticon {float:left; margin:0 5px 0 0!important; display:block; width:18px!important; height:18px!important; overflow:hidden;}
.gamepticon.steam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steam.svg) 0 center no-repeat;}
.gamepticon.steam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steam.png) 0 center no-repeat\9;}
.gamepticon.psn {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-psn.svg) 0 center no-repeat;}
.gamepticon.psn {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-psn.png) 0 center no-repeat\9;}
.gamepticon.xbox,.gamepticon.xbl {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-xbox.svg) 0 center no-repeat;}
.gamepticon.xbox,.gamepticon.xbl {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-xbox.png) 0 center no-repeat\9;}
.gamepticon.steampsn,.gamepticon.psnsteam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steampsn.svg) 0 center no-repeat;}
.gamepticon.steampsn,.gamepticon.psnsteam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steampsn.png) 0 center no-repeat\9;}
.gamepticon.steamxbox,.gamepticon.steamxbl,
.gamepticon.xboxsteam,.gamepticon.xblsteam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steamxbox.svg) 0 center no-repeat;}
.gamepticon.steamxbox,.gamepticon.steamxbl,
.gamepticon.xboxsteam,.gamepticon.xblsteam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steamxbox.png) 0 center no-repeat\9;}
.gamepticon.psnxbox,.gamepticon.psnxbl,
.gamepticon.xboxpsn,.gamepticon.xblpsn {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-psnxbox.svg) 0 center no-repeat;}
.gamepticon.psnxbox,.gamepticon.psnxbl,
.gamepticon.xboxpsn,.gamepticon.xblpsn {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-psnxbox.png) 0 center no-repeat\9;}
.gamepticon.steampsnxbox,.gamepticon.steampsnxbl,
.gamepticon.steamxboxpsn,.gamepticon.steamxblpsn,
.gamepticon.psnxboxsteam,.gamepticon.psnxblsteam,
.gamepticon.xboxpsnsteam,.gamepticon.xblpsnsteam,
.gamepticon.xboxsteampsn,.gamepticon.xblsteampsn,
.gamepticon.psnsteamxbox,.gamepticon.psnsteamxbl {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steampsnxbox.svg) 0 center no-repeat;}
.gamepticon.steampsnxbox,.gamepticon.steampsnxbl,
.gamepticon.steamxboxpsn,.gamepticon.steamxblpsn,
.gamepticon.psnxboxsteam,.gamepticon.psnxblsteam,
.gamepticon.xboxpsnsteam,.gamepticon.xblpsnsteam,
.gamepticon.xboxsteampsn,.gamepticon.xblsteampsn,
.gamepticon.psnsteamxbox,.gamepticon.psnsteamxbl {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steampsnxbox.png) 0 center no-repeat\9;}

/*玩过认证*/
.attestlayer {display:none; width:17px; height:18px; overflow:visible; position:relative; bottom:18px; z-index:10000;}
.attestlayer .con {padding:10px 0 10px; width:216px; height:auto; background-color:#fff; border:1px solid #bcbcbc; border-radius:5px; overflow:visible;}
.attestlayer .con {position:absolute; left:50%; bottom:13px; margin-left:-108px; box-shadow:0 0 10px 0 rgba(0,0,0,.2);}
.attestlayer .con h3 {margin:0 auto; padding-left:35px; width:155px; height:32px; line-height:32px; color:#222; font-size:18px;}
.attestlayer .con h3 {background:url(//image.gamersky.com/webimg15/zp/v1/rz1.png) 0 center no-repeat;}
.attestlayer .con p {margin:0 auto; width:190px; height:20px; line-height:20px; color:#666; font-size:14px; font-family:'Microsoft YaHei'; text-align:right;}
.attestlayer .vvv {display:block; width:17px; height:14px; position:absolute; left:50%; bottom:0; margin-left:-8.5px;}
.attestlayer .vvv {background:url(//image.gamersky.com/webimg15/zp/v1/vv.png) 0 -14px no-repeat;}

/*圈子认证*/
.zhenglayer {display:none; width:18px; height:18px; overflow:visible; position:relative; bottom:18px; z-index:10000;}
.zhenglayer .con {width:200px; height:auto; text-align:center; overflow:visible; position:absolute; left:50%; bottom:13px; margin-left:-100px;}
.zhenglayer .con span {display:inline-block; padding:0 12px; width:auto; height:34px; line-height:34px; color:#444; font-size:14px; font-family:'Microsoft YaHei';}
.zhenglayer .con span {background-color:#fff; border:1px solid #bcbcbc; border-radius:5px; box-shadow:0 0 10px 0 rgba(0,0,0,.2);}
.zhenglayer .vvv {display:block; width:17px; height:14px; position:absolute; left:50%; bottom:0; margin-left:-8.5px;}
.zhenglayer .vvv {background:url(//image.gamersky.com/webimg15/zp/v1/vv.png) 0 -14px no-repeat;}

/*游戏名片*/
.gameptlayer {display:none; width:18px; height:18px; overflow:visible; position:relative; top:18px; z-index:10000;}
.gameptlayer .gamept-vvv {width:17px; height:14px; position:absolute; left:50%; top:0; z-index:10001; margin-left:-8.5px;}
.gameptlayer .gamept-vvv {background:url(//image.gamersky.com/webimg15/zp/v1/vv.png) no-repeat;}
.gameptlayer p,.gameptlayer dl,.gameptlayer dd {margin:0; padding:0; list-style-type:none;}
.gameptlayer .gamept-con {padding:15px; width:260px; height:auto; font-family:'Microsoft YaHei'; background-color:#fff; border:1px solid #bcbcbc; border-radius:8px;}
.gameptlayer .gamept-con {position:absolute; left:50%; top:13px; margin-left:-145px; box-shadow:0 0 10px 0 rgba(0,0,0,.2);}
.gameptlayer .gamept-con img.loading {margin:30px auto; display:block; width:40px; height:40px;}
.gameptlayer.cur {top:auto; bottom:18px;}
.gameptlayer.cur .gamept-vvv {top:auto; bottom:0; background-position:0 -14px;}
.gameptlayer.cur .gamept-con {top:auto; bottom:13px;}
.gameptlayer .gamept-lists {height:auto; overflow:hidden;}

.gameptlayer .gamept-lists dd {margin-bottom:10px; height:40px; border-radius:8px; position:relative; margin-bottom: 30px;}

.gameptlayer .gamept-lists dd.psn {background-image:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-bg-psn.png);}
.gameptlayer .gamept-lists dd.xbox {background-image:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-bg-xbox.png);}

.gameptlayer .gamept-lists dd .gamept-head {display:flex; align-items: center; width:100%; height:50px; position:absolute; left:8px; top:7px;}
.gameptlayer .gamept-lists dd .gamept-icon {display:block; width:26px; height:26px; border-radius:10px; margin-right: 10px;}
.gameptlayer .gamept-lists dd .gamept-icon.steam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steam.svg) center no-repeat;}
.gameptlayer .gamept-lists dd .gamept-icon.steam {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-steam.png) center no-repeat\9;}
.gameptlayer .gamept-lists dd .gamept-icon.psn {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-psn.svg) center no-repeat;}
.gameptlayer .gamept-lists dd .gamept-icon.psn {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-psn.png) center no-repeat\9;}
.gameptlayer .gamept-lists dd .gamept-icon.xbox {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-xbox.svg) center no-repeat;}
.gameptlayer .gamept-lists dd .gamept-icon.xbox {background:url(//image.gamersky.com/webimg15/user/club2/pc/gamept-xbox.png) center no-repeat\9;}

.gameptlayer .gamept-lists dd .gamept-name {display:block; color:#333; font-size:14px; white-space:nowrap; text-overflow:ellipsis; overflow:hidden;}

.gameptlayer .gamept-lists dd .gamept-name {overflow: initial;}
.gameptlayer .gamept-lists dd .gamept-name .name {font-size: 16px;font-weight: 700;}
.gameptlayer .gamept-lists dd .gamept-name .txt {color: #666;}
.gameptlayer .gamept-lists dd .gamept-num {width:73px; height:auto; position:absolute; right:12px; top:5px;}
.gameptlayer .gamept-lists dd .gamept-num p {margin:0}
.gameptlayer .gamept-lists dd .gamept-num p.num {padding-right:3px; height:16px; line-height:16px; color:#222; font-size:16px; font-weight:700; text-align:right;}
.gameptlayer .gamept-lists dd .gamept-num p.txt {height:12px; line-height:12px; color:#666; font-size:12px; text-align:right;}
.gameptlayer .gamept-bot {height:66px; position:relative;}
.gameptlayer .gamept-bot p {width:166px; height:20px; line-height:20px; color:#858585; font-size:12px; position:absolute; left:0;}
.gameptlayer .gamept-bot p.txt1 {top:20px;}
.gameptlayer .gamept-bot p.txt2 {top:40px;}
.gameptlayer .gamept-bot .code {display:block; width:86px; height:66px; position:absolute; top:0; right:0;}


.gameptlayer .gamept-box{display: flex; justify-content: space-between;align-items: center; background: #f5f5f5; padding: 15px 5px; border-radius: 6px;}


/*私信弹窗*/
.letterpop-mask {width:100%; height:100%; background-color:#000; opacity:.1; filter:alpha(opacity=10); position:fixed; _position:absolute; top:0; left:0; z-index:20000;}
.letterpop-mask {_height:1000px; _position:absolute;_top:expression(eval(document.documentElement.scrollTop));}
.letterpop-cont {margin-left:-350px; width:700px; height:auto; background-color:#fff; box-shadow:0 0 20px 0 rgba(0,0,0,.5); transition:all .25s ease-out;}
.letterpop-cont {position:fixed; top:50%; left:50%; z-index:30000; _position:absolute;_top:expression(eval(document.documentElement.scrollTop+400));}
.letterpop-top {padding:12px 17px; width:666px; height:24px; background-color:#1795dd;}
.letterpop-top h3 {float:left;margin:0; padding-left:36px; width:auto; height:24px; line-height:24px; color:#fff; font-size:16px; font-weight:400;}
.letterpop-top h3 {background:url(//image.gamersky.com/webimg15/user/center2/letter-icon.png) no-repeat;}
.letterpop-top h3 b {padding:0 5px;}
.letterpop-top a {float:right; display:block; width:24px; height:24px; transition:all .25s ease-out;}
.letterpop-top a {background:url(//image.gamersky.com/webimg15/user/center2/letter-icon.png) 0 -24px no-repeat;}
.letterpop-top a:hover {transform:rotateZ(180deg);}
.letterpop-mid {padding:0 20px; width:660px; height:auto; overflow-y:auto; transition:all .25s ease-out;}
.letterpop-mid::-webkit-scrollbar {width:10px; background-color:transparent;} /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.letterpop-mid::-webkit-scrollbar-track {background-color:#ebeced;} /*定义滚动条轨道 内阴影+圆角*/
.letterpop-mid::-webkit-scrollbar-thumb {background-color:#cfd1d4;} /*定义滑块 内阴影+圆角*/
.letterpop-mid .letter-content {width:100%; height:auto; overflow:hidden;}
.letterpop-mid .letter-loading {display:block; width:auto; height:52px; background:url(//image.gamersky.com/webimg15/user/center2/loading.gif) center center no-repeat;}
.letterpop-mid .letter-more {padding-left:50px; width:600px; height:50px; line-height:50px; text-align:center;}
.letterpop-mid .letter-more a {color:#e23703; font-size:14px; text-decoration:none;}
.letterpop-mid .letter-list {width:650px; height:auto;}
.letterpop-mid .letter-xxxx {padding:5px 0 0 50px; overflow:visible;}
.letterpop-mid .letter-xxxx span {display:block; width:100%; height:10px; text-align:center; border-bottom:1px dotted #ccc;}
.letterpop-mid .letter-xxxx span i {padding:0 10px; display:inline-block; color:#999; font-size:14px; font-style:normal; background-color:#fff;}
.letterpop-mid .letter-error {padding:5px 0 0 50px; margin:30px 0; text-align:center;}
.letterpop-mid .letter-error span {padding-left:22px; color:#999; font-size:14px; background:url(//image.gamersky.com/webimg15/user/center2/error.png) no-repeat;}
.letterpop-mid .letter-cont {margin:25px 0; width:650px; overflow:hidden;}
.letterpop-mid .letter-cont .letter-head {float:left; width:50px; height:50px;}
.letterpop-mid .letter-cont .letter-head .userlink img {width:50px; height:50px; border-radius:50%;}
.letterpop-mid .letter-cont .letter-wrap {float:left; width:600px; overflow:visible;}
.letterpop-mid .letter-cont .letter-wrap .arr {float:left; width:16px; height:31px; background:url(//image.gamersky.com/webimg15/user/center2/letter-left-arr.png) 0 10px no-repeat;}
.letterpop-mid .letter-cont .letter-wrap .con {float:left; display:inline-block; padding:10px 10px 10px 20px; background-color:#f2f4f5; border-radius:10px; overflow:visible;}
.letterpop-mid .letter-cont .letter-wrap .content {float:left; max-width:400px; line-height:26px; color:#333; font-size:15px;}
.letterpop-mid .letter-cont .letter-wrap .state {float:left; overflow:visible;} /*状态*/
.letterpop-mid .letter-cont .letter-wrap .state .box {display:inline-block; width:30px; height:30px;}
.letterpop-mid .letter-cont .letter-wrap .state .box a.arrow {display:block; width:30px; height:22px; border-radius:4px;}
.letterpop-mid .letter-cont .letter-wrap .state .box a.arrow {background:url(//image.gamersky.com/webimg15/user/center2/ab1.png) center center no-repeat;}
.letterpop-mid .letter-cont .letter-wrap .state .box .box-list {display:none; width:48px; height:auto; text-align:center; position:absolute; z-index:100;}
.letterpop-mid .letter-cont .letter-wrap .state .box .box-list {background-color:#fff; border:1px solid #ddd; border-radius:4px; box-shadow:0.782px 4.938px 5px 0px rgba(0, 0, 0, 0.1);}
.letterpop-mid .letter-cont .letter-wrap .state .box .box-list a {display:block; width:100%; height:28px; line-height:28px; color:#555; font-size:12px; text-align:center; text-decoration:none;}
.letterpop-mid .letter-cont .letter-wrap .state .box .box-list a:hover {background-color:#f2f2f3;text-decoration:none!important;}
.letterpop-mid .letter-cont .letter-wrap .state .box.cur .box-list {display:block;}
.letterpop-mid .letter-cont.right .letter-head {float:right;}
.letterpop-mid .letter-cont.right .letter-wrap {float:right;}
.letterpop-mid .letter-cont.right .letter-wrap .arr {float:right; background:url(//image.gamersky.com/webimg15/user/center2/letter-right-arr.png) 0 10px no-repeat;}
.letterpop-mid .letter-cont.right .letter-wrap .con {float:right; background-color:#1795dd;}
.letterpop-mid .letter-cont.right .letter-wrap .content {color:#fff;}
.letterpop-bot {padding:15px 20px; width:660px; height:auto; border-top:1px solid #d9d9d9;}
.letterpop-bot .letter-textarea {margin-bottom:10px; width:auto; height:90px }
.letterpop-bot .letter-textarea textarea {width:100%; height:90px; line-height:22px; color:#444; font-size:14px; font-family:'Microsoft YaHei';}
.letterpop-bot .letter-textarea textarea {border:0; resize:none; padding:0; outline:0; background:transparent; overflow-y:auto;}
.letterpop-bot .letter-textarea textarea::-webkit-scrollbar {width:10px; background-color:transparent;} /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.letterpop-bot .letter-textarea textarea::-webkit-scrollbar-track {background-color:#ebeced;} /*定义滚动条轨道 内阴影+圆角*/
.letterpop-bot .letter-textarea textarea::-webkit-scrollbar-thumb {background-color:#cfd1d4;} /*定义滑块 内阴影+圆角*/
.letterpop-bot .letter-btn {width:auto; height:40px;}
.letterpop-bot .letter-btn .con {float:left; width:540px; height:40px;}
.letterpop-bot .letter-btn .con .restNum {float:right; display:block; width:auto; height:40px; line-height:40px; color:#999; font-size:15px;}
.letterpop-bot .letter-btn .btn {float:left; width:120px; height:40px;}
.letterpop-bot .letter-btn .btn a {float:right; display:block; width:100px; height:40px; line-height:40px; color:#fff; font-size:16px; text-align:center; background-color:#1795dd;}
.letterpop-bot .letter-btn .btn a:hover {background-color:#1ca4f1;text-decoration:none!important;}

/*举报弹窗*/
.reportpop-mask {width:100%; height:100%; background-color:#000; opacity:.5; filter:alpha(opacity=50); position:fixed; _position:absolute; top:0; left:0; z-index:40000;}
.reportpop-mask {_height:1000px; _position:absolute;_top:expression(eval(document.documentElement.scrollTop));}
.reportpop-cont {margin:-220px 0 0 -200px; padding:0; width:398px; height:auto; background-color:#fff; border:1px solid #ddd; border-radius:5px; box-shadow:1.564px 9.877px 20px 0 rgba(0,0,0,0.1);}
.reportpop-cont {position:fixed; top:50%; left:50%; z-index:50000; _position:absolute;_top:expression(eval(document.documentElement.scrollTop+400));}
.reportpop-cont .tit {padding:0 12px; width:374px; height:28px; background-color:#f5f5f5; position:relative;}
.reportpop-cont .tit h3 {float:left; margin:0; width:auto; height:28px; line-height:28px; color:#333; font-size:14px; font-weight:400;}
.reportpop-cont .tit a.reportpop-close {display:block; width:21px; height:21px; position:absolute; right:5px; top:3px;}
.reportpop-cont .tit a.reportpop-close {background:url(//image.gamersky.com/webimg15/user/club/pc/close1.png) no-repeat;}
.reportpop-cont .tit a.reportpop-close:hover {background-position:0 -21px;}
.reportpop-cont .con {padding:20px 35px 0; width:328px; height:auto;  color:#333; font-size:16px;}
.reportpop-cont .con p {margin:0; line-height:30px; font-size:14px;}
.reportpop-cont .con p a {color:#3694ca;}
.reportpop-cont .con p a:hover {text-decoration:underline;}
.reportpop-cont .con .content {margin:5px 0; padding:10px 10px 10px 15px; width:auto; height:40px; background-color:#f8f8f8;}
.reportpop-cont .con .content .img {float:left; width:40px; height:40px;}
.reportpop-cont .con .content .img img {width:40px; height:40px; border-radius:50%;}
.reportpop-cont .con .content .txt {padding-left:53px; overflow:visible; line-height:20px; color:#888; font-size:12px;}
.reportpop-cont .con .content .txt a {color:#3694ca;}
.reportpop-cont .con .content .txt a:hover {text-decoration:underline;}
.reportpop-cont .con .content .txt span {color:#888;}
.reportpop-cont .con .vlist {margin-top:-3px; width:100%; height:auto;}
.reportpop-cont .con .vlist li.int {width:100%; height:28px;}
.reportpop-cont .con .vlist li.int input {float:left; margin:7px 7px 0 0; width:14px; height:14px;}
.reportpop-cont .con .vlist li.int label {float:left; width:auto; height:28px; line-height:28px; color:#333; font-size:14px;}
.reportpop-cont .con .vlist li.text {width:100%; height:78px; padding-top:6px;}
.reportpop-cont .con .vlist li.text textarea {padding:5px 10px; width:306px; height:66px; line-height:24px; color:#444; font-size:15px; font-family:'Microsoft YaHei';}
.reportpop-cont .con .vlist li.text textarea {border:1px solid #d5d5d5; resize:none; outline:0; background:transparent; overflow-y:auto;}
.reportpop-cont .con .vlist li.text textarea::-webkit-scrollbar {width:10px; background-color:transparent;} /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.reportpop-cont .con .vlist li.text textarea::-webkit-scrollbar-track {background-color:#ebeced;} /*定义滚动条轨道 内阴影+圆角*/
.reportpop-cont .con .vlist li.text textarea::-webkit-scrollbar-thumb {background-color:#cfd1d4;} /*定义滑块 内阴影+圆角*/
.reportpop-cont .btn {padding:15px 0; width:100%; height:auto; text-align:center;}
.reportpop-cont .btn a {margin:0 5px; display:inline-block; width:75px; height:32px; line-height:32px; font-size:14px; border-radius:4px;}
.reportpop-cont .btn a.okbtn {color:#fff; background-color:#2786ed;}
.reportpop-cont .btn a.nobtn {color:#333; background-color:#e5e5e5;}
.reportpop-cont .btn a.okbtn:hover {background-color:#3293fc;text-decoration:none!important;}
.reportpop-cont .btn a.nobtn:hover {background-color:#d4d2d2;text-decoration:none!important;}

/*确定删除*/
.qzpop-mask {width:100%; height:100%; background-color:#000; opacity:.5; filter:alpha(opacity=50); position:fixed; _position:absolute; top:0; left:0; z-index:40000;}
.qzpop-mask {_height:1000px; _position:absolute;_top:expression(eval(document.documentElement.scrollTop));}
.qzpop-cont {margin:-100px 0 0 -150px; padding:0 30px; width:258px; height:auto; background-color:#fff; border:1px solid #ddd; border-radius:5px; box-shadow:1.564px 9.877px 20px 0 rgba(0,0,0,0.1);}
.qzpop-cont {position:fixed; top:50%; left:50%; z-index:50000; _position:absolute;_top:expression(eval(document.documentElement.scrollTop+400));}
.qzpop-cont .tit {margin:0 -30px; padding:0 12px; width:294px; height:28px; background-color:#f5f5f5; position:relative;}
.qzpop-cont .tit h3 {float:left; width:auto; height:28px; line-height:28px; color:#333; font-size:14px; font-weight:400;}
.qzpop-cont .tit a.qzpop-close {display:block; width:21px; height:21px; position:absolute; right:5px; top:3px;}
.qzpop-cont .tit a.qzpop-close {background:url(//image.gamersky.com/webimg15/user/club/pc/close1.png) no-repeat;}
.qzpop-cont .tit a.qzpop-close:hover {background-position:0 -21px;}
.qzpop-cont .con {padding:25px 0 10px; width:100%; height:auto; line-height:30px; color:#333; font-size:16px;}
.qzpop-cont .con p {margin:0;}
.qzpop-cont .con p.p1 {font-size:14px;}
.qzpop-cont .con p.p2 {line-height:18px; color:#888; font-size:12px;}
.qzpop-cont .btn {padding:15px 0; width:100%; height:auto; text-align:center;}
.qzpop-cont .btn a {margin:0 5px; display:inline-block; width:75px; height:32px; line-height:32px; font-size:14px; border-radius:4px;}
.qzpop-cont .btn a.okbtn {color:#fff; background-color:#2786ed;}
.qzpop-cont .btn a.nobtn {color:#333; background-color:#e5e5e5;}
.qzpop-cont .btn a.okbtn:hover {background-color:#3293fc;text-decoration:none!important;}
.qzpop-cont .btn a.nobtn:hover {background-color:#d4d2d2;text-decoration:none!important;}

/*成功提示*/
.complete {margin:-30px 0 0 -120px; width:234px; height:64px; line-height:64px; color:#333; font-size:14px; text-align:center;}
.complete {background-color:#fff; border:1px solid #ddd; border-radius:5px; box-shadow:1.564px 9.877px 20px 0 rgba(0,0,0,0.1);}
.complete {position:fixed; top:50%; left:50%; z-index:60000; _position:absolute;_top:expression(eval(document.documentElement.scrollTop+400));}