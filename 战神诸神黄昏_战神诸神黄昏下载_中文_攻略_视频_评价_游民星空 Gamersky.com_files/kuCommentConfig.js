(function ($) {
    var link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = 'https://j.gamersky.com/g/jubao.css?123';
    document.head.appendChild(link);
    var isTrim = function (s) { return s.replace(/(^\s*)|(\s*$)/g, ""); }; //清除空格
    var PLhtml = {
        login_text: function (htm) {
            htm += '<div class="remark-textarea">';
            htm += '<div class="textarea-b"><textarea id="mytextarea" class="textarea-fw" spellcheck="false"></textarea></div>';
            htm += '<div class="remark-action">';
            htm += '<div class="button">';
            htm += '<div class="btn-a"><a href="javascript:;" class="btn" name="bq" title="插入表情"></a></div>';
            htm += '</div>';
            htm += '<div class="action-btn"><a class="remark-btn" href="javascript:;" data-top="false" data-click="false">发布</a></div>';
            htm += '<div class="action-alert"></div>';
            htm += '</div>';
            htm += '</div>';
            return htm;
        },
        bqPopHtm: function () { //表情弹窗
            var htm = '<div class="ccmt-popup bq">';
            htm += '<div class="arr"></div>';
            htm += '<div class="con">';
            htm += '<div class="bq-con">' + emojiNav() + '</div>';
            htm += '</div>';
            htm += '</div>';
            return htm;
        }
    };
    var PLogin = {
        //点击发布按钮
        Submit: function (div) {
            var $this = $(div);
            $this.off("click").on("click", "a.btn[name='bq']", function () {
                var $this = $(this), role = $this.attr("name");
                var popup = ".ccmt-popup." + role;
                if ($this.hasClass("cur")) {
                    return;
                }
                $('.ccmt-popup.tp').hide();
                if ($this.siblings(popup).length == 0) {
                    $this.after(PLhtml.bqPopHtm()); //载入表情弹窗
                } else {
                    $this.next(popup).toggle();
                }
            }).on("click", ".bqbtn", function () {
                var $this = $(this), title = $this.attr("title");
                $(".textarea-fw").focus();
                document.execCommand('insertHTML', false, title);
                $(this).parents(".button").find(".ccmt-popup").hide();
                emojiHit($this.attr("data-cid")); //统计
            }).on("click", "a.remark-btn", function (event) {
                event.preventDefault();
                var that = $(this), con = $("#mytextarea").val();
                if (!isTrim(con)) {
                    $this.find(".action-alert").text('未输入评论内容');
                } else if (con.length > 1000) {
                    $this.find(".action-alert").text('输入内容过长,请重新输入！');
                } else {
                    if (that.attr("data-click") == "true") {
                        return;
                    }
                    that.attr("data-click", true);

                    PLogin.SubmitAjax(con, function (responseJson) {
                        that.attr("data-click", false);
                        if (responseJson.status == "ok") {
                            var data = $.parseJSON(responseJson.body);
                            var $html = $(data.ReplyComment);
                            var $reply = that.parents('.remark-build-cont-floor');
                            if ($reply.length > 0) {
                                that.parents('.remark-build-cont').append($html);
                            } else {
                                that.parents('.remark-reply-con').siblings(".remark-build-cont").append($html);
                            }
                            var $em = that.parents('.remark-build').siblings(".remark-action").find(".t1 em");
                            if ($em.length == 0) {
                                that.parents('.remark-build').siblings(".remark-action").find(".t1").html("展开回复（<em>1</em>）");
                                that.parents('.remark-reply-con').siblings(".remark-build-cont-links").show();
                            } else {
                                var m = parseInt($em.text()) + 1;
                                $em.text(m);
                            }
                            $(".remark-textarea").remove();
                            $(".remark-issue,.floor-user").zhankai();
                        } else {
                            $this.find(".action-alert").text(responseJson.body); //未登录
                        }
                    });
                }
            }).on("keyup", ".textarea-fw", function () {
                $(this).parents(".wrap-reply,.cmt-top-cont").next(".cmt-alert").html('').removeClass("block").addClass("none");
            }).on("click", "a.evt-more", function (event) {
                event.preventDefault();
                $(this).parent().prev(".wrap-issue").addClass("cur");
                $(this).parent().remove();
            });
        },
        //这里Ajax提交发布信息并刷新页面
        SubmitAjax: function (con, fun) {
            var sid = $("#Remark").attr("sid"); //文章ID
            var nodeId = $("#Remark").attr("nodeId"); //节点ID
            var cmtid = $(".remark-textarea").attr("cmtid");
            var url = window.location.href;
            var title = document.title;
            var brow = $.browser.msie ? "jsonp" : "json";
            $.ajax({
                type: "POST", dataType: brow, url: "//cm1.gamersky.com/api/addcommnet",
                data: { jsondata: JSON.stringify({ sid: sid, content: con, cmtid: cmtid, topicTitle: title, topicUrl: url, nodeId: nodeId }) },
                xhrFields: { withCredentials: true },
                success: function (responseJson) {
                    fun(responseJson);
                }
            });
        }
    };

    var kuHtml = {
        //调用函数
        callingFun: {
            userUrl: function (uid) { return 'http://i.gamersky.com/u/' + uid; },
            //认证调用
            zheng: function (n) {
                var con;
                switch (n) {
                    case 6:
                    case 10:
                    case 20:
                    case 21:
                    case 22:
                        con = '//image.gamersky.com/webimg15/user/club/pc/zheng-huangV.png'; //黄色认证
                        break;
                    case 18:
                    case 19:
                        con = '//image.gamersky.com/webimg15/user/club/pc/zheng-lanV.png'; //蓝色认证
                        break;
                    default:
                        con = '//image.gamersky.com/webimg15/user/club/pc/zheng-wj.png'; //紫色认证
                }
                return con;
            },
            //时间戳转为为普通日期格式
            formatDate: function (format, strTime) {
                format = format || 'yyyy-mm-dd hh:ff:ss';
                var dateMap = function (t) {
                    var add = function (n) { return n < 10 ? '0' + n : n };
                    var time = {
                        yyyy: t.getFullYear(),
                        mm: add(t.getMonth() + 1),
                        dd: add(t.getDate()),
                        hh: add(t.getHours()),
                        ff: add(t.getMinutes()),
                        ss: add(t.getSeconds())
                    };
                    return time;
                };
                strTime = isNaN(strTime) ? strTime.replace(/-/g, '/') : strTime;
                var dtime = new Date(), ttime = new Date(strTime);
                var ddate = dateMap(dtime), tdate = dateMap(ttime);
                var goday = tdate.yyyy + '/' + tdate.mm + '/' + tdate.dd;
                var today = ddate.yyyy + '/' + ddate.mm + '/' + ddate.dd;
                var aTime = new Date(goday).getTime();
                var bTime = new Date(today).getTime();
                var tHour = 24 * 60 * 60 * 1000;
                var fHour = 24 * 60 * 60 * 1000 * 2;
                var yTime = new Date(bTime - tHour).getTime();
                var fTime = new Date(bTime - fHour).getTime();

                for (var k in tdate) { format = format.replace(new RegExp(k, 'g'), tdate[k]); }

                if (aTime >= bTime) {
                    return '今天 ' + format.split(' ')[1];
                } else if (aTime < bTime && yTime <= aTime) {
                    return '昨天 ' + format.split(' ')[1];
                } else if (aTime < yTime && fTime <= aTime) {
                    return '前天 ' + format.split(' ')[1];
                } else {
                    return format;
                }
            }
        },
        kuHtm: function (htm) {
            htm += '<div class="remark-nav">';
            htm += '  <div class="nav1" id="remark-nav1">';
            htm += '    <a href="javascript:;" loadtype="1" class="cur">玩过(<i>0</i>)</a>';
            htm += '    <a href="javascript:;" loadtype="2">想玩(<i>0</i>)</a>';
            htm += '  </div>';
            htm += '  <div class="nav2" id="remark-nav2">';
            htm += '    <a href="javascript:;" data-type="1" class="cur">推荐</a>';
            htm += '    <a href="javascript:;" data-type="0">最新</a>';
            htm += '  </div>';
            htm += '  <div class="nav3" id="remark-nav3">';
            htm += '    <a href="javascript:;" nametype="all" class="cur"> 全部 </a>';
            htm += '    <a href="javascript:;" nametype="rz">玩过认证<i></i><em></em></a>';
            htm += '    <a href="javascript:;" nametype="hp">好评<i></i></a>';
            htm += '    <a href="javascript:;" nametype="cp">差评<i></i></a>';
            htm += '    <a href="javascript:;" nametype="steam">Steam评论</a>';
            htm += '  </div>';
            htm += '  <div class="text" id="remark-text">';
            htm += '    <h3>玩过认证玩家说明</h3>';
            htm += '    <p>绑定了游戏账号（比如Steam、PSN），并且拥有这款游戏的用户会显示此标识；</p>';
            htm += '    <p>如果您通过共享或者是购买后又退款等形式拥有过此游戏，仍然算作玩过认证玩家!</p>';
            htm += '    <div class="code"><img src="//image.gamersky.com/webimg15/zp/v1/code.jpg" /><span>用游民App认证玩过玩家</span></div>';
            htm += '  </div>';
            htm += '</div>';
            htm += '<div class="remark-list"></div>';
            htm += '<div class="remark-page" data-count="0" data-pagesize="0"></div>';
            return htm;
        },
        listHtm: function (item, foorPageSize) {
            var htm = '', cFun = kuHtml.callingFun;
            var type = { 1: '玩过', 2: '想玩' };
            $.each(item, function (index, value) {
                var reviewid = value.reviewid;
                var userid = value.user_id;
                var userLink = cFun.userUrl(userid);
                var zheng = cFun.zheng(value.userGroupId);
                var createTime = value.create_time;
                var modifyTime = value.modify_time;
                var userTime = cFun.formatDate('yyyy-mm-dd hh:ff', createTime == modifyTime ? createTime : modifyTime);
                var headimg = value.img_URL || '//image.gamersky.com/webimg15/comment/anonymous.jpg';
                var username = value.nickname || '游民用户';
                var reviewCount = value.reviewCount;
                var content = value.content || '';
                htm += '<div class="remark-list-floor" cmtid="' + reviewid + '">';
                htm += '<div class="remark-cont-head">';
                htm += '<a class="userlink" href="' + userLink + '" target="_blank" uid="' + userid + '">';
                htm += '<img src="' + headimg.replace("http:", "") + '" alt="用户头像" />';
                htm += '</a>';
                htm += '</div>';
                htm += '<div class="remark-cont-wrap">';
                htm += '<div class="remark-wrap">';
                htm += '<div class="wrap-user" cmtid="' + reviewid + '">';
                htm += '<div class="user-name">';
                htm += '<a class="uname" href="' + userLink + '" uid="' + userid + '" target="_blank">' + username + '</a>';
                htm += '<a class="level" href="javascript:;">Lv' + parseInt(value.userLevel) + '</a>';
                if (value.userAuthentication != '') {
                    htm += '<a class="qzheng">';
                    htm += '<img src="' + zheng + '" />';
                    htm += '<div class="zhenglayer"><div class="con"><span>' + value.userAuthentication + '</span></div><div class="vvv"></div></div>';
                    htm += '</a>';
                }
                // if (value.thirdPlatformBound != '') {
                //     htm += '<div class="gamept">';
                //     htm += '<a class="gamepticon ' + value.thirdPlatformBound.replace(/,/g, "") + '" uid="' + userid + '"></a>';
                //     htm += '<div class="gameptlayer"><i class="gamept-vvv"></i><div class="gamept-con"></div></div>';
                //     htm += '</div>';
                // }
                if (value.isUserRealPlayer) {
                    htm += '<a class="attest">';
                    htm += '<img src="//image.gamersky.com/webimg15/zp/v1/rz1.png" />';
                    htm += '<div class="attestlayer"><div class="con"><h3>玩过认证</h3><p>绑过游戏平台后得以验证</p></div><div class="vvv"></div></div>';
                    htm += '</a>';
                }
                htm += '</div>';
                htm += '<div class="user-xin">';
                if (value.likeType == 1) {
                    htm += '<div class="xin"><div class="x' + (value.rating / 2) + '"></div></div>';
                }
                var userPlayTime = '';
                if (value.isUserRealPlayer) {
                    if (value.playInfo != null && value.playInfo.length > 0) {
                        var playInfo = value.playInfo;
                        var maxPlayTime = 0;
                        for (var i = 0; i < playInfo.length; i++) {
			/*
                            if (value.platform == playInfo[i].playPlatform) {
                                if (playInfo[i].playTime>0) {
                                    // userPlayTime = Math.floor(playInfo[i].playTime);
                                    userPlayTime=playInfo[i].playTime
                                    
                                    // if (String(playInfo[i].playTime).indexOf('.')>-1) {
                                    //     userPlayTime = Number(playInfo[i].playTime).toFixed(1);
                                    // }else{
                                    //     userPlayTime=playInfo[i].playTime
                                    // }
                                    
                                    userPlayTime += '小时';
                                }
                            }*/
							
                            if (playInfo[i].playTime > maxPlayTime) {
                                maxPlayTime = playInfo[i].playTime;
                            }
                        }
						
						var pcPlayInfo = playInfo.find((o) => { return o["playPlatform"] == "PC" });
						var ps4PlayInfo = playInfo.find((o) => { return o["playPlatform"] == "PS4" });
						var ps5PlayInfo = playInfo.find((o) => { return o["playPlatform"] == "PS5" });
						
						if(pcPlayInfo != undefined && value.platform=="PC"){
							if(pcPlayInfo.playTime>0){
								userPlayTime=pcPlayInfo.playTime;
							    userPlayTime += '小时';
							}
						}
						
						if(value.platform=="PS"){
							var psPlayTime = 0;
							if(ps4PlayInfo != undefined){
								if(ps4PlayInfo.playTime>0){
									psPlayTime += ps4PlayInfo.playTime;
								}
							}
							if(ps5PlayInfo != undefined){
								if(ps5PlayInfo.playTime>0){
									psPlayTime += ps5PlayInfo.playTime;
								}
							}
							if(psPlayTime>0){
								userPlayTime=psPlayTime;
								userPlayTime += '小时';
							}
						}
                     
                        if (value.platform == '' && maxPlayTime > 0) {
                            userPlayTime = Math.floor(maxPlayTime) + '小时';
                        }
                    }
                }



                htm += '<div class="txt">' + (value.platform != '' ? '在' + value.platform + '平台' : '') + type[value.likeType] + userPlayTime + '</div>';
                htm += '</div>';
                htm += '<div class="user-time">';

                if (value.danyeId > 0) {
                    htm += '<a href="https://ku.gamersky.com/activity/n/' + value.danyeId + '" target="_blank">' + (createTime == modifyTime ? userTime : '最后修改 ' + userTime) + '</a>';
                } else {
                    htm += createTime == modifyTime ? userTime : '最后修改 ' + userTime;
                }
                htm += '</div>';
                htm += '</div>';
                htm += '<div class="remark-issue">';
                htm += '<div class="remark-content">' + emojibBatch(content) + '</div>';
                htm += '</div>';
                htm += '<div class="remark-action">';
                htm += '<div class="remark-action-btn">';
                htm += '<span class="report"><a href="javascript:;" class="report-btn" cmtid="' + reviewid + '" data-userid="' + userid + '" data-contenttype="zhongPing">举报</a><i>|</i></span>';
                htm += '<a href="javascript:;" class="remark-support" cmtid="' + reviewid + '" date-type="5" uid="' + userid + '">' + value.like + '</a><i>|</i>';
                htm += '<a href="javascript:;" class="remark-notsupport" cmtid="' + reviewid + '" date-type="6" uid="' + userid + '"></a><i>|</i>';
                htm += '<a href="javascript:;" class="remark-reply' + (reviewCount > 0 ? ' cur' : '') + '" cmtid="' + reviewid + '">';
                htm += '<span class="t1">' + (reviewCount > 0 ? '展开回复(<em>' + reviewCount + '</em>)' : '回复') + '</span>';
                htm += '<span class="t2">收起回复</span>';
                htm += '</a>';
                htm += '</div>';
                htm += '</div>';
                htm += '<div class="remark-build' + (reviewCount > 0 ? ' cur' : '') + '" cmtid="' + reviewid + '">';
                htm += '<div class="remark-build-cont">';
                htm += kuHtml.replyListHtm(value.reviews, 3);
                htm += '</div>';
                htm += '<!-- 分页 -->';
                htm += '<div class="remark-build-cont-links">';
                htm += '<div class="links-con">';
                if (reviewCount > 3) {
                    htm += '<span>还有' + (reviewCount - 3) + '条回复，</span>';
                    htm += '<a href="javascript:;" class="remark-floors-more" data-cmtid="' + reviewid + '" data-count="' + reviewCount + '" data-pagesize="' + foorPageSize + '">点击查看</a>';
                }
                htm += '</div>';
                htm += '<div class="links-page" data-count="' + reviewCount + '" data-pagesize="' + foorPageSize + '"></div>';
                htm += '<div class="links-btn"><a href="javascript:;" class="btn-reply" cmtid="' + reviewid + '">我也说一句</a></div>';
                htm += '</div>';
                htm += '<div class="remark-reply-con"></div>';
                htm += '</div>';
                htm += '</div>';
                htm += '</div>';
                htm += '</div>';
            });
            return htm;
        },
        replyListHtm: function (item, len, name) {
            var htm = '', cFun = kuHtml.callingFun;
            var delHtml = function (str) { return str.replace(/<[^>]+>/g, ''); }
            name = name || '';
            $.each(item, function (index, value) {
                var cmtid = value.reviewid || value.replyId;
                var userId = value.user_id || value.userId;
                var userLink = cFun.userUrl(userId);
                var headimg = value.img_URL || value.userHeadImageURL || '//image.gamersky.com/webimg15/comment/anonymous.jpg';
                var username = value.nickname || value.userName || '游民用户';
                var replyname = value.replynickname || value.replyUserName || "";
                var createTime = cFun.formatDate('yyyy-mm-dd hh:ff', value.create_time || value.replyTime);
                var content = value.content || value.replyContent || '';
                var text = delHtml(content.replace(/\u003c/g, '<').replace(/\u003e/g, '>'));
                var floorcon = text.length <= 125 ? text : text.substring(0, 125) + "…";
                htm += '<div class="remark-build-cont-floor' + (index < len ? '' : ' none') + '" cmtid="' + cmtid + '">';
                htm += '<div class="remark-cont-head">';
                htm += '<a class="userlink" uid="' + userId + '" href="' + userLink + '" target="_blank"><img src="' + headimg.replace("http:", "") + '" alt="用户头像" /></a>';
                htm += '</div>';

                htm += '<div class="remark-cont-wrap">';
                htm += '<div class="remark-wrap">';
                htm += '<div class="floor-user" cmtid="' + cmtid + '">';
                htm += '<a class="uname" href="' + userLink + '" target="_blank">' + username + '</a>';
                if (replyname != name) {
                    htm += '<em>回复</em><a class="uname" href="' + userLink + '" target="_blank">' + replyname + '</a>';
                }
                htm += '<i>：</i>';
                htm += '<span class="floor-con">' + emojibBatch(floorcon) + '</span>';
                if (text.length > 125) {
                    htm += '<a class="remark-allcon" href="javascript:;" data-con="' + content + '">展开</a>';
                }
                htm += '</div>';
                htm += '<div class="floor-action">';
                htm += '<span class="report">';
                htm += '<a href="javascript:;" class="report-btn" cmtid="' + cmtid + '" data-userid="' + userId + '" data-contenttype="zhongPing">举报</a>';
                htm += '<i>|</i>';
                htm += '</span>';
                htm += '<a href="javascript:;" class="btn-reply" cmtid="' + cmtid + '">回复</a> <i>|</i>';
                htm += '<span class="remark-time">' + createTime + '</span>';
                htm += '</div>';
                htm += '<div class="remark-reply-con"></div>';
                htm += '</div>';
                htm += '</div>';
                htm += '</div>';
            });
            return htm;
        }
    };

    //评论载入
    $.fn.GetComment = function (options) {
        return this.each(function () {
            var $div = $(this);
            var $list = $div.find(".remark-list");
            var articleId = $div.attr("sid");
            var steamid = $div.attr("data-steamid");
            var loadtype = $("#remark-nav1 a.cur").attr("loadtype");
            var datetype = $("#remark-nav2 a.cur").attr("data-type");
            var nametype = $("#remark-nav3 a.cur").attr("nametype");

            if (loadtype == "1" && nametype == 'steam') {
                $("#remark-nav2,#remark-text").hide();
                $("#remark-nav3").show();
                if (steamid == "") {
                    return;
                }
                $.ajax({
                    type: "get", dataType: "jsonp", url: "//cm1.gamersky.com/api/GetSteamId",
                    data: { jsondata: JSON.stringify({ steamid: steamid }) },
                    beforeSend: function (XHR) {
                        $list.html('<div class="remark-loading"><img src="//image.gamersky.com/webimg15/comment/loading.gif"></div>');
                        $(".remark-page").html('');
                    },
                    success: function (data) {
                        if (data.status == "ok") {
                            var data = $.parseJSON(data.body);
                            if (data.html != null && data.html != "") {
                                var img_a1 = 'http://store.akamai.steamstatic.com/public/shared/images/userreviews/icon_thumbsUp_v6.png';
                                var img_a2 = 'http://store.akamai.steamstatic.com/public/shared/images/userreviews/icon_thumbsDown_v6.png';
                                var img_b1 = '//image.gamersky.com/webimg15/zp/steam-zan1.png';
                                var img_b2 = '//image.gamersky.com/webimg15/zp/steam-zan2.png';
                                var replac = function (src) {
                                    return src == img_a1 ? src.replace(img_a1, img_b1) : src.replace(img_a2, img_b2);
                                }
                                var $body = $(data.html);
                                $body.find(".rightcol .posted").html('');
                                $body.find(".user_reviews_sub_header").remove();
                                $body.find(".rightcol .hr").remove();
                                $body.find(".rightcol .control_block").remove();
                                $body.find(".rightcol .vote_info").remove();
                                var html = $body.html();
                                $list.html('<div class="remark-list">' + html + '</div>');
                                $list.find(".review_box .rightcol").each(function () {
                                    var $this = $(this);
                                    if ($this.find(".content").height() > 145) {
                                        $this.addClass("partial").find(".posted").html("<a href='javascript:;'>展开阅读</a>").show();
                                    }
                                    $this.find(".thumb img").attr("src", replac($this.find(".thumb img").attr("src")));
                                });
                                $list.on("click", ".posted a", function (event) {
                                    event.preventDefault();
                                    $(this).parents(".review_box").find(".content").addClass("cur");
                                    $(this).remove();
                                });
                                $list.find(".persona_name a").each(function () {
                                    $(this).attr("target", "_blank");
                                });
                                $list.find(".rightcol a.vote_header").each(function () {
                                    $(this).attr("href", "javascript:void(0);").css("cursor", "default");
                                });

                            } else {
                                $list.html('<div class="remark-loading">暂无点评</div>');
                            }
                        } else {
                            $list.html('<div class="remark-loading">暂无点评</div>');
                        }
                    }
                });
            } else {
                $("#remark-nav2").show();
                var minScore = 0, maxScore = 10;
                var isRealPlayer = loadtype == "1" && nametype == 'rz' ? true : false;
                if (loadtype == "1") {
                    if (nametype == 'cp') { minScore = 2; maxScore = 4; }
                    if (nametype == 'hp') { minScore = 8; maxScore = 10; }
                    if (nametype == 'rz') {
                        $("#remark-text").show();
                    } else {
                        $("#remark-text").hide();
                    }
                    $("#remark-nav3").show();
                } else {
                    $("#remark-nav3").hide();
                    $("#remark-text").hide();
                }

                var pageIndex = options || 1;
                var pageSize = 10;
                var foorPageSize = 8;
                var orderType = datetype == 0 ? "updateTimeDESC" : "";
                var jsondata = {
                    "request": {
                        "articleId": articleId,
                        "dataType": datetype,
                        "loadType": loadtype,
                        "pageIndex": pageIndex,
                        "pageSize": pageSize,
                        "floorPageSize": foorPageSize,
                        "isNeedOnlyRealPlayer": isRealPlayer,
                        "minUserScore": minScore,
                        "maxUserScore": maxScore,
                        "isFiltra": 2,
                        "orderType": orderType
                    }
                };
                $.ajax({
                    type: "post", dataType: "json", url: "https://instrument.gamersky.com/HTTPHelper/CrossPost",
                    data: { "url": "http://appapi2.gamersky.com/game/GetAllReview", "postData": JSON.stringify(jsondata) },
                    beforeSend: function (XHR) {
                        $list.html('<div class="remark-loading"><img src="//image.gamersky.com/webimg15/comment/loading.gif"></div>');
                    },
                    success: function (data) {
                        var result = $.parseJSON(data).result;
                        console.log('---', result);
                        var reviewCount = result.reviewCount;
                        var reviews = result.reviews;
                        if (reviewCount > 0) {
                            $list.html(kuHtml.listHtm(reviews, foorPageSize));
                            $div.find(".remark-page").attr({ "data-count": reviewCount, "data-pagesize": pageSize });
                            $div.find(".remark-page").pageList(pageIndex, function (page) {
                                $("body,html").scrollTop($div.offset().top - 10);
                                $div.GetComment(page);
                            });
                            $(".remark-support,.remark-notsupport").addLike();
                            $(".remark-issue,.floor-user").zhankai();
                            $div.Operation(); //操作
                        } else {
                            $list.html('<div class="remark-loading">暂无点评</div>').next(".remark-page").html('');
                        }
                    }
                });
            }
        });
    };

    //分页调用
    $.cachedScript('//j.gamersky.com/g/pagination.js');
    $.fn.pageList = function (pageIndex, callback) {
        return this.each(function () {
            var $this = $(this);
            var count = parseInt($this.attr("data-count"));
            var pagesize = $this.attr("data-pagesize");
            pagination.init({
                wrapid: $this, //页面显示分页器容器id
                total: count, //总条数
                pagesize: pagesize, //每页显示10条
                currentPage: pageIndex, //当前页
                btnCount: 7, //页数过多时，显示省略号的边界页码按钮数量，可省略，且值是大于5的奇数
                onPagechange: callback
            });
        });
    };

    //获取 回复评论
    $.fn.getReplyList = function (options) {
        var $this = $(this);
        var cmtId = options.commentId;
        var pageIndex = options.pageIndex;
        var pageSize = options.pageSize;
        var jsondata = { "request": { "commentId": cmtId, "pageIndex": pageIndex, "pageSize": pageSize } };
        $.ajax({
            type: "post", dataType: "json", url: "https://instrument.gamersky.com/HTTPHelper/CrossPost",
            data: {
                "url": "http://appapi2.gamersky.com/game/getGameCommentReplies",
                "postData": JSON.stringify(jsondata)
            },
            success: function (data) {
                var result = $.parseJSON(data).result;
                var replies = result.replies;
                if (replies.length > 0) {
                    var name = $(".wrap-user[cmtid='" + cmtId + "'] .uname").text();
                    var htm = kuHtml.replyListHtm(replies, pageSize, name);
                    $this.find(".remark-build-cont").html(htm);
                }
            }
        });
    };

    $.fn.addLike = function (options) {
        return this.each(function () {
            var $this = $(this);
            $this.unbind("click").click(function (event) {
                event.preventDefault();
                var cmtid = $this.attr("cmtid"),
                    type = $this.attr("date-type"),
                    uid = $this.attr("uid"),
                    num = parseInt($this.text()) + 1,
                    sid = $("#Remark").attr("sid");
                $this.UserOnline(function () {
                    $.ajax({
                        type: "GET", dataType: "jsonp", url: "//cm1.gamersky.com/api/addlike",
                        data: { jsondata: JSON.stringify({ commentId: cmtid, generalId: sid, fromId: uid, type: type, platform: 0 }) },
                        success: function (responseJson) {
                            switch (responseJson.status) {
                                case -1:
                                    $("#QZNotLog .Login").trigger("click");
                                    break;
                                case 0:
                                    $this.html(num);
                                    break;
                                case 1:
                                    var meassage = responseJson.notsupport == true ? "踩过" : "点过赞";
                                    alert("你已经" + meassage + "！");
                                    break;
                            }
                        }
                    });
                });
            });
        });
    };
    /*主评论展开*/
    $.fn.zhankai = function () {
        return this.each(function () {
            var $div = $(this);
            if ($div.hasClass("floor-user")) {
                $div.on("click", ".remark-allcon", function (event) {
                    event.preventDefault();
                    var $this = $(this), txt = $this.attr("data-con");
                    $div.find(".floor-con").html(emojibBatch(txt));
                    $this.remove();
                });
            }
            if ($div.hasClass("remark-issue")) {
                var rch = $div.find(".remark-content").height();
                var coh = $div.find(".remark-content")[0].scrollHeight;
                if (coh > rch) {
                    if ($div.find(".remark-zhan").length == 0) {
                        $div.append('<div class="remark-zhan"><a class="remark-allcon" href="javascript:;">展开阅读全文</a></div>');
                        $div.on("click", ".remark-allcon", function (event) {
                            event.preventDefault();
                            $div.find(".remark-content").addClass("cur");
                            $(this).parents(".remark-zhan").remove();
                        });
                    }
                }
            }
        });
    };
    /*操作功能*/
    $.fn.Operation = function (options) {
        return this.each(function () {
            var $list = $(this);
            $list.off("click").on("click", ".level", function () {
                var $this = $(this);
                $this.UserOnline(function () {
                    $this.attr({ "href": "//i.gamersky.com/my/level", "target": "_blank" });
                });
            }).on("click", ".remark-reply,.btn-reply", function (event) {
                event.preventDefault();
                var $this = $(this), cmtid = $this.attr("cmtid");
                $(".remark-reply-con").html('');
                if ($this.hasClass("remark-reply")) {
                    $this.toggleClass("cur");
                    $remarkbuild = $(".remark-build[cmtid='" + cmtid + "']");
                    $remarkbuild.toggleClass("cur");
                    if ($this.find(".t1 em").length == 0) {
                        $remarkbuild.find(".remark-build-cont-links").hide();
                        $remarkbuild.find(".remark-reply-con").html(PLhtml.login_text('')).find(".textarea-fw").focus();
                    }
                }
                if ($this.hasClass("btn-reply")) {
                    $this.parents(".remark-build-cont-links,.floor-action").next(".remark-reply-con").html(PLhtml.login_text('')).find(".textarea-fw").focus();
                }
                $(".remark-action").each(function () {
                    var $em = $(this).find(".remark-reply").not($this);
                    if ($em.find(".t1 em").length == 0) {
                        $em.removeClass("cur");
                        $em.parents(".remark-action").siblings(".remark-build ").removeClass("cur");
                    }
                });
                $(".remark-textarea").attr("cmtid", cmtid);
                PLogin.Submit(".remark-list");
            }).on("click", ".remark-floors-more", function () { //回复列表展开更多
                var $this = $(this);
                var pageIndex = 1;
                var cmtid = $this.attr("data-cmtid");
                var count = $this.attr("data-count") | 0;
                var pageSize = $this.attr("data-pagesize") | 0;
                var $build = $this.parents(".remark-build");
                $build.find(".remark-build-cont-floor").removeClass("none");
                $build.find(".links-con").html('共' + count + '条回复');
                if (count > pageSize) {
                    $build.find(".links-page").pageList(pageIndex, function (page) {
                        $build.getReplyList({ commentId: cmtid, pageIndex: page, pageSize: pageSize }); //获取回复分页内容
                    });
                }
            }).on("mouseenter", ".remark-list-floor", function () {
                $(this).addClass("cur");
            }).on("mouseleave", ".remark-list-floor", function () {
                $(this).removeClass("cur");
            });
        });
    };
    /*Steam评论显示隐藏*/
    $.fn.GetSteamId = function (steamid) {
        return this.each(function () {
            var $this = $(this);
            if (steamid == "") {
                return;
            }
            $.ajax({
                type: "get", dataType: "jsonp", url: "//cm1.gamersky.com/api/GetSteamId",
                data: { jsondata: JSON.stringify({ steamid: steamid }) },
                success: function (data) {
                    if (data.status == "ok") {
                        var body = $.parseJSON(data.body);
                        if (body == null) {
                            return;
                        }
                        if (body.html && isTrim($(body.html).find(".leftcol").html()) == "") {
                            $this.hide();
                        } else {
                            $this.show();
                        }
                    }
                }
            });
        });
    };

    var addThousandthSign = function (n) { return n.toString().replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, "$1,"); };
    $.fn.ajaxCount = function (nametype, callback) {
        return this.each(function () {
            var $div = $(this);
            var articleId = $div.attr("sid");
            var isRealPlayer = nametype == 'rz' ? true : false;
            var minScore = 0, maxScore = 10;
            if (nametype == 'cp') { minScore = 2; maxScore = 4; }
            if (nametype == 'hp') { minScore = 8; maxScore = 10; }
            var jsondata = {
                "request": {
                    "articleId": articleId,
                    "loadType": 1,
                    "dataType": 1,
                    "pageIndex": 1,
                    "pageSize": 0,
                    "floorPageSize": 0,
                    "isNeedOnlyRealPlayer": isRealPlayer,
                    "minUserScore": minScore,
                    "maxUserScore": maxScore,
                    "isFiltra": 0
                }
            };
            $.ajax({
                type: "post", dataType: "json", url: "https://instrument.gamersky.com/HTTPHelper/CrossPost",
                data: { "url": "http://appapi2.gamersky.com/game/GetAllReview", "postData": JSON.stringify(jsondata) },
                success: function (data) {
                    var result = $.parseJSON(data).result;
                    var reviewCount = result.reviewCount;
                    callback(' ' + addThousandthSign(reviewCount));
                }
            });
        });
    };

    $.fn.kuComment = function (options) {
        return this.each(function () {
            var $Remark = $(this);
            //比较日期大小
            function judgeTime(startTime) {
                var reg = new RegExp("[\\u4E00-\\u9FFF]+");
                if (typeof (startTime) == "undefined" || reg.test(startTime)) {
                    return false;
                } else {
                    var add = function (n) { return n < 10 ? '0' + n : n };
                    var myDate = new Date();
                    var yyyy = myDate.getFullYear();
                    var mm = add(myDate.getMonth() + 1);
                    var dd = add(myDate.getDate());
                    var endTime = yyyy + '-' + mm + "-" + dd;
                    var start = new Date(startTime).getTime(); //指定时间
                    var end = new Date(endTime).getTime(); //当前时间
                    if (start - end > 0) {
                        return false;
                    }
                }
                return true;
            }
            function checkMarketTime(div) {
                var txt = '未上市';

                var timeList = []
                $(div).find("a").each(function () {
                    timeList.push($(this).attr("data-time"))
                });
                //判断最早玩过时间
                var TheEarliestTimeToPlay = $('.TheEarliestTimeToPlay').eq(0).html()
                if (TheEarliestTimeToPlay) {
                    //处理格式
                    var clData = new Date(TheEarliestTimeToPlay);
                    var year = clData.getFullYear()
                    var Month = clData.getMonth() + 1
                    var clMoth = Month < 10 ? "0" + Month : Month
                    var day = clData.getDate() < 10 ? "0" + clData.getDate() : clData.getDate()
                    var downData = year + "-" + clMoth + "-" + day;
                    timeList.push(downData)
                }
                $.each(timeList, function (index, item) {
                    var time = judgeTime(item);
                    if (time) {
                        txt = "已上市";
                        return false;
                    } else {
                        txt = "未上市";
                    }
                })

                return txt;
            }

            var selltime = checkMarketTime(".pingtai"); //检查上市时间
            var n = selltime == '未上市' ? 2 : 1;
            var articleId = $Remark.attr("sid");
            var steamid = $Remark.attr("data-steamid");
            $.ajax({
                type: "get", dataType: "jsonp", url: "//cm1.gamersky.com/api/GetComment",
                data: { jsondata: JSON.stringify({ dateType: 0, loadType: 0, pageIndex: 1, pageSize: 2, foorPageSize: 5, articleId: articleId }) },
                success: function (data) {
                    if (data.status == "ok") {
                        var data = $.parseJSON(data.body);
                        var AllCount = addThousandthSign(data.AllCount);
                        var PlayedCount = addThousandthSign(data.PlayedCount);
                        var WantPlayCount = addThousandthSign(data.WantPlayCount);
                        $("#remark-nav1 a[loadtype='0'] i").html(AllCount);
                        $("#remark-nav1 a[loadtype='1'] i").html(PlayedCount);
                        $("#remark-nav1 a[loadtype='2'] i").html(WantPlayCount);
                        var loadtype = n == 2 ? n : data.PlayedCount > 0 ? 1 : data.WantPlayCount > 0 ? 2 : 1;
                        $("#remark-nav1 a[loadtype='" + loadtype + "']").addClass("cur").siblings().removeClass("cur");
                        $("#remark-nav3 a[nametype='steam']").GetSteamId(steamid);

                        $Remark.ajaxCount('rz', function (i) { $("#remark-nav3 a[nametype='rz'] i").html(i); });
                        $Remark.ajaxCount('hp', function (i) { $("#remark-nav3 a[nametype='hp'] i").html(i); });
                        $Remark.ajaxCount('cp', function (i) { $("#remark-nav3 a[nametype='cp'] i").html(i); });
                        $Remark.GetComment();
                    }
                }
            });

            $("#remark-nav1,#remark-nav2,#remark-nav3").on("click", "a", function (event) {
                event.preventDefault();
                $(this).addClass("cur").siblings().removeClass("cur");
                $Remark.GetComment();
            });


        });
    };


    //关注插件
    $.cachedScript('//j.gamersky.com/web2015/ku/js/guanzhu.js?v=' + dateFtt({ f: 10 }));
    //举报插件
    $.cachedScript('//j.gamersky.com/user/club2/pc/js/report-comment.js?v=' + dateFtt({ f: 10 }));
    $.cachedScript('//j.gamersky.com/user/emote/emoji.js?v=' + dateFtt({ h: 1 })).done(function () {
        var $Remark = $("#Remark");
        var htm = kuHtml.kuHtm('');
        $Remark.html(htm).kuComment();

    });
})(jQuery);